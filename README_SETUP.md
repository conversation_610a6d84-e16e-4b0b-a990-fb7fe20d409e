# RAPSAP Flutter Project

This Flutter project has been prepared for handover with all sensitive data removed.

## ⚠️ IMPORTANT: Setup Required

This project requires configuration before it can be built and run. All sensitive files have been removed for security purposes.

## Quick Setup Guide

### 1. Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your actual values
nano .env  # or use your preferred editor
```

### 2. Firebase Setup
```bash
# Install FlutterFire CLI
dart pub global activate flutterfire_cli

# Configure Firebase for your project
flutterfire configure
```

### 3. Google Maps API Key
- Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
- Update `mapsApiKey` in `lib/utils/constants.dart`

### 4. Local Development Setup
- Update `android/local.properties` with your SDK paths
- Run `flutter pub get` to install dependencies
- Run `flutter build` to generate missing files

## Documentation

See `HANDOVER_DOCUMENTATION.md` for detailed information about:
- What sensitive data was removed
- Complete setup instructions
- Security recommendations
- API keys and credentials that need to be configured

## Project Structure

```
lib/
├── controllers/     # GetX controllers
├── model/          # Data models
├── services/       # API and external services
├── utils/          # Utilities and constants
├── view/           # UI screens and widgets
└── main.dart       # Application entry point
```

## Dependencies

This project uses:
- Flutter SDK
- Firebase (Auth, Analytics, Crashlytics, Messaging)
- Google Maps
- GetX for state management
- Dio for HTTP requests
- And many more (see `pubspec.yaml`)

## Build Instructions

```bash
# Get dependencies
flutter pub get

# Run on device/emulator
flutter run

# Build APK
flutter build apk

# Build for iOS
flutter build ios
```

## Support

For setup assistance or questions about the removed credentials, contact <EMAIL>

---
**⚠️ Remember to configure all sensitive data before building the project!**
