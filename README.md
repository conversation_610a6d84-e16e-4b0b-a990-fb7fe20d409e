# RAPSAP Flutter Project

A comprehensive Flutter application for grocery delivery and e-commerce platform.

## 📋 Table of Contents
- [Project Overview](#project-overview)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Setup Instructions](#setup-instructions)
- [Configuration](#configuration)
- [Project Structure](#project-structure)
- [Dependencies](#dependencies)
- [Build & Run](#build--run)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## 🎯 Project Overview

RAPSAP is a Flutter-based mobile application that provides:
- **Grocery Delivery Service**: Order groceries and get them delivered
- **E-commerce Platform**: Browse products, manage cart, and checkout
- **User Authentication**: Google Sign-in and OTP verification
- **Real-time Tracking**: Track orders with live updates
- **Payment Integration**: Razorpay payment gateway
- **Location Services**: Google Maps integration for delivery addresses
- **Push Notifications**: Firebase messaging for order updates

## ✨ Features

### 🛒 Core Features
- **Product Catalog**: Browse categories and products
- **Shopping Cart**: Add, remove, and manage items
- **Order Management**: Place orders, track status, and view history
- **User Profiles**: Personal details and preferences
- **Address Management**: Multiple delivery addresses
- **Payment Integration**: Secure payment processing
- **Notifications**: Real-time order updates

### 🔐 Authentication
- Phone number verification with OTP

### 📍 Location Services
- Google location sharing
- Address autocomplete
- Delivery location tracking
- Store location finder

### 🔔 Notifications
- Push notifications for order updates
- Local notifications for app events
- Background message handling

## 🔧 Prerequisites

### Development Environment
- **Flutter SDK**: 3.7.2 or higher
- **Dart SDK**: Included with Flutter
- **Android Studio**: Latest version with Android SDK
- **Xcode**: Latest version (for iOS development)
- **Git**: Version control

### Platform Requirements
- **Android**: API level 21 (Android 5.0) or higher
- **iOS**: iOS 12.0 or higher

### External Services
- **Firebase Project**: For authentication, analytics, and messaging
- **Google Cloud Platform**: For Maps API and other services
- **Razorpay Account**: For payment processing

## 🚀 Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd new_project
```

### 2. Install Flutter Dependencies
```bash
flutter pub get
```

### 3. Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
nano .env  # or use your preferred editor
```

### 4. Firebase Setup
```bash
# Install FlutterFire CLI
dart pub global activate flutterfire_cli

# Configure Firebase for your project
flutterfire configure
```

### 5. Platform-Specific Setup

#### Android Setup
1. Update `android/local.properties`:
```properties
sdk.dir=/path/to/your/android/sdk
flutter.sdk=/path/to/your/flutter/sdk
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1
```

2. Add `google-services.json` to `android/app/` directory

#### iOS Setup
1. Add `GoogleService-Info.plist` to `ios/Runner/` directory
2. Update iOS deployment target in `ios/Runner.xcodeproj`

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# API Configuration
API_URL = "https://your-api-url.com"
ORG_DB_NAME = "your_organization_db_name"

# MQTT Configuration
MQTT_BROKER = "wss://your-mqtt-broker.com"
MQTT_PORT = 443
MQTT_USERNAME = "your_mqtt_username"
MQTT_PASSWORD = "your_mqtt_password"
MQTT_TOPIC = "your/mqtt/topic"

# UI Theme Colors
pageBackgroundColor = '#F3F3F3'
footerTextColor = '#333333'
lavendarGrey = '#C5C8CD'
darkGrey = '#808080'
alert = '#ff3b30'
primaryColor = '#5074f3'
lightPrimaryColor = '#edf1fe'
```

### API Keys to Configure
1. **Firebase Configuration**: Update `lib/firebase_options.dart`
2. **Google Maps API**: Update `mapsApiKey` in `lib/utils/constants.dart`
3. **Razorpay Keys**: Configure in payment service

## 📁 Project Structure

```
lib/
├── app.dart                 # Main app configuration
├── main.dart               # Application entry point
├── firebase_options.dart   # Firebase configuration
├── controllers/            # GetX controllers for state management
│   ├── account_controller.dart
│   ├── location_controller.dart
│   ├── order_controller.dart
│   └── user_controller.dart
├── model/                  # Data models
│   ├── category_color.dart
│   ├── google_places/
│   ├── user_model/
│   └── model.dart
├── services/               # External services
│   ├── firebase_services.dart
│   ├── location_service.dart
│   ├── notification_service.dart
│   └── user_services.dart
├── utils/                  # Utilities and constants
│   ├── constants.dart
│   ├── exception_handler.dart
│   └── utils.dart
└── view/                   # UI components
    ├── screens/            # Application screens
    └── widgets/            # Reusable UI components

assets/
├── images/                 # Image assets
├── svg/                    # SVG icons
├── json/                   # Lottie animations
└── gif/                    # GIF animations
```

## 📦 Dependencies

### Core Dependencies
- **flutter**: Flutter framework
- **get**: State management and navigation
- **get_storage**: Local storage solution
- **http**: HTTP client for API calls
- **dio**: Advanced HTTP client with interceptors

### Firebase Integration
- **firebase_core**: Firebase core functionality
- **firebase_auth**: User authentication
- **firebase_messaging**: Push notifications
- **firebase_analytics**: Analytics tracking
- **firebase_crashlytics**: Crash reporting

### UI & UX
- **google_fonts**: Custom fonts
- **flutter_svg**: SVG support
- **cached_network_image**: Image caching
- **shimmer**: Loading animations
- **lottie**: Lottie animations
- **confetti**: Celebration animations

### Maps & Location
- **google_maps_flutter**: Google Maps integration
- **geolocator**: Location services
- **geocoding**: Address geocoding

### Payment & Commerce
- **razorpay_flutter**: Payment gateway
- **qr_code_scanner**: QR code functionality

### Utilities
- **intl**: Internationalization
- **path_provider**: File system paths
- **sqflite**: Local database
- **connectivity_plus**: Network connectivity
- **package_info_plus**: App information

## 🏗️ Build & Run

### Development
```bash
# Run on connected device/emulator
flutter run

# Run with specific flavor
flutter run --flavor dev

# Hot reload during development
# Press 'r' in terminal or use IDE hot reload
```

### Debug Build
```bash
# Android APK
flutter build apk --debug

# iOS (requires macOS)
flutter build ios --debug
```

### Release Build
```bash
# Android APK
flutter build apk --release

# Android App Bundle (for Play Store)
flutter build appbundle --release

# iOS (requires macOS)
flutter build ipa --release
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run specific test file
flutter test test/widget_test.dart
```

### Test Structure
```
test/
├── widget_test.dart        # Widget tests
├── unit_tests/            # Unit tests
└── integration_tests/     # Integration tests
```

## 🚀 Deployment

### Android Deployment
1. **Generate Signed APK**:
   ```bash
   flutter build appbundle --release
   ```

2. **Upload to Play Store**:
   - Upload the generated AAB file
   - Configure store listing
   - Set up release management

### iOS Deployment
1. **Build for App Store**:
   ```bash
   flutter build ipa --release
   ```

2. **Upload to App Store**:
   - Use Xcode or Application Loader
   - Configure App Store Connect
   - Submit for review

## 🔍 Troubleshooting

### Common Issues

#### 1. Firebase Configuration
**Error**: `No Firebase App '[DEFAULT]' has been created`
**Solution**: Ensure `Firebase.initializeApp()` is called in `main.dart`

#### 2. Google Maps Not Loading
**Error**: Maps show blank or gray screen
**Solution**: 
- Verify Google Maps API key is valid
- Check if required APIs are enabled in Google Cloud Console
- Ensure billing is enabled for the project

#### 3. Build Failures
**Error**: Various build errors
**Solution**:
```bash
# Clean build files
flutter clean
flutter pub get

# Reset Flutter
flutter doctor
```

#### 4. Permission Issues
**Error**: Location or camera permissions denied
**Solution**: Check `android/app/src/main/AndroidManifest.xml` and `ios/Runner/Info.plist`

### Debug Commands
```bash
# Check Flutter installation
flutter doctor

# Check connected devices
flutter devices

# Analyze code for issues
flutter analyze

# Check for dependency issues
flutter pub deps
```
