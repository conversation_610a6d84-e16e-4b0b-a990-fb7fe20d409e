name: customer_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  avatar_glow: ^3.0.1
  bouncerwidget: ^0.0.5
  cached_network_image: any
  carousel_slider: ^5.0.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  dotted_border: ^2.0.0+2
  firebase_analytics: ^10.1.0
  firebase_core: ^2.4.1
  firebase_crashlytics: ^3.0.9
  firebase_core_platform_interface: ^5.0.0
  firebase_messaging: ^14.2.1
  flutter:
    sdk: flutter
  flutter_displaymode: ^0.6.0
  flutter_local_notifications: ^17.0.1
  flutter_svg: ^2.0.6
  geocoding: ^3.0.0
  geolocator: ^11.0.0
  get: ^4.6.5
  get_storage: ^2.0.3
  google_fonts: 6.1.0
  google_maps_flutter: ^2.6.1
  google_sign_in: ^6.1.3
  group_button: ^5.2.2
  http:
  image_picker: ^1.0.8
  intl: ^0.19.0
  loading_indicator: ^3.1.0
  modal_bottom_sheet: 3.0.0-pre
  package_info_plus: ^4.0.2
  pinput: ^4.0.0
  razorpay_flutter: ^1.3.5
  scratcher: ^2.2.1
  shimmer: ^3.0.0

  timeline_tile: ^2.0.0
  url_launcher: ^6.1.6
  marquee: ^2.2.3
  staggered_grid_view_flutter: ^0.0.4
  connectivity_plus: ^6.0.2
  confetti: ^0.7.0
  upgrader: ^10.2.0
  fluttertoast: ^8.0.2
  path: ^1.8.3
  path_provider: ^2.1.3
  sqflite: ^2.3.2
  lottie: ^3.1.0
  dio: ^5.5.0+1
  awesome_dio_interceptor: ^1.2.0
  flutter_dotenv: ^5.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons:
    ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/svg/
    - assets/json/
    - assets/gif/
    - .env

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
dependency_overrides:
  fading_edge_scrollview: ^4.1.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_icons:
  image_path: "assets/rapsap-icon.png"
  android: true
  ios: true
  remove_alpha_ios: true