import 'dart:developer';

// import 'package:carousel_slider/carousel_controller.dart';
import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
// import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../model/model.dart';
import '../model/user_model/user_model.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import '../view/screens/map_screen/allow_location_screen.dart';
import '../view/screens/root_page/root_page.dart';
import '../services/firebase_services.dart';
import '../services/user_services.dart';
import '../view/screens/login/personal_details_screen.dart';
import '../main.dart';
import 'account_controller.dart';
import 'location_controller.dart';

class UserController extends GetxController {
  late Dio _dio;
  late UserService _userService;

  RxBool showpassword = true.obs;
  RxBool tester = false.obs;

  bool loading = false;
  RxBool buttonloading = false.obs;

  // RxBool numberSelected = true.obs;

  final TextEditingController phoneController = TextEditingController();
  Rxn<UserData> userdata = Rxn();
  NearbyStore? nearbyStore;
  List<double>? coordinates;

  bool isGettingStore = false;

  Future<bool> updateUserDetails({
    required String name,
    required String email,
  }) async {
    try {
      await _userService.updateUserDetails(name: name, email: email);

      return true;
    } on DioException catch (e) {
      Fluttertoast.showToast(
        msg: 'Error ${e.response?.statusCode}',
        backgroundColor: Colors.red,
      );
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Fluttertoast.showToast(msg: 'Error $e', backgroundColor: Colors.red);
    }
    return false;
  }

  Future<bool> getNearbyStore(List<double>? coordinates) async {
    isGettingStore = true;
    update();
    try {
      final result = await _userService.getNearbyStore(coordinates);
      if (result != null) {
        nearbyStore = result;
        this.coordinates = coordinates;
        update();
        return true;
      }
      return false;
    } on DioException catch (e) {
      Fluttertoast.showToast(
        msg: 'Error ${e.response?.statusCode}',
        backgroundColor: Colors.red,
      );
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Fluttertoast.showToast(msg: 'Error $e', backgroundColor: Colors.red);
    } finally {
      isGettingStore = false;
      update();
    }
    return false;
  }

  void googleSigninVerify(params) async {
    final user = await UserService.onGoogleSignin(params);
    if (user.success == true) {
      // await storage.write('JWT', user.data?.token);
      // log(storage.read('JWT'));
      // currentUser.value = _user;
      // print('current user ${currentUser.value.msg}');
      storage.write('userdata', user.toJson());

      userdata.value = user;

      await FirebaseService.firebaseAnalytics.logEvent(
        name: 'login_user',
        parameters: user.toJson(),
      );
      await FirebaseService.firebaseAnalytics.logLogin(loginMethod: "google");

      Future.delayed(const Duration(milliseconds: 10), () {
        if (storage.read('coordinates') == null) {
          Get.to(() => const AllowLocationScreen(), binding: LocationBinding());
        } else {
          Get.to(() => const RootPage());
        }
      });
      // updateFirebaseToken(_user.data!.id);
    } else {
      Get.back();

      Utils.customToast(message: 'Login details invalid! try again.');
    }
  }

  Future<void> verifyOtp(
    BuildContext context, {
    required String phone,
    required String otp,
  }) async {
    loading = true;
    update();

    try {
      final result = await _userService.verifyOtp(
        phone: phone,
        otp: otp,
        fcmToken: await FirebaseMessaging.instance.getToken() ?? '',
      );

      if (result.success ?? false) {
        // await storage.write('JWT', result.data.token);
        await storage.write('userdata', result.data.toJson());
        userdata.value = result.data;

        if (result.data.isNewUser) {
          await Get.to(() => PersonalDetailsScreen(phonenumber: phone));
        }
        await Future.delayed(
          Durations.short4,
          () async => await Get.find<AccountController>().getaddress(),
        );

        Get.to(() => const AllowLocationScreen(), binding: LocationBinding());
      }
    } catch (e, st) {
      log('[RAPSAP]: $e, $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    } finally {
      loading = false;
      update();
    }
  }

  Future<bool> sendOtp(String phone) async {
    loading = true;
    update();

    try {
      await _userService.sendOTP(phone);
      return true;
    } catch (e) {
      log(e.toString());
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    } finally {
      loading = false;
      update();
    }
    return false;
  }

  @override
  void onInit() async {
    _dio = Dio(BaseOptions(baseUrl: baseURL, validateStatus: (_) => true))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _userService = UserService(_dio);
    super.onInit();
  }
}
