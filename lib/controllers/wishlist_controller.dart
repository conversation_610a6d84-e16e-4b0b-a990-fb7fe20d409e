import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/item_list_model/item_list_model.dart';
import '../services/product_service.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import 'user_controller.dart';

class WishlistController extends GetxController {
  late Dio _dio;
  late ProductService _productService;

  @override
  void onInit() async {
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _productService = ProductService(_dio);
    super.onInit();
  }

  List<Item> wislistItems = [];
  bool loading = false;

  Future<void> removeFromwishlist(String itemId) async {
    try {
      final result = await _productService.removeFromWishList(itemId: itemId);
      if (result.success ?? false) {
        Utils.customToast(
            message: result.message ?? 'Removed',
            backgroundColor: Colors.green);
      } else {
        throw result.message ?? 'Something went wrong';
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st',
          name: 'WishlistController.removeFromwishlist');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
  }

  Future<void> addToWishlist(String itemId) async {
    try {
      final result = await _productService.addToWishList(itemId: itemId);
      if (result.success ?? false) {
        Utils.customToast(
            message: result.message ?? 'Added', backgroundColor: Colors.green);
      } else {
        throw result.message ?? 'Something went wrong';
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st',
          name: 'WishlistController.addToWishlist');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
  }

  Future<void> getWishlist() async {
    loading = true;
    update(['wishlist']);
    try {
      final result = await _productService.getWishListItems(
          storeId: Get.find<UserController>().nearbyStore!.id!);
      wislistItems = result.docs;
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st',
          name: 'WishlistController.getWishlist');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    } finally {
      loading = false;
      update(['wishlist']);
    }
  }
}
