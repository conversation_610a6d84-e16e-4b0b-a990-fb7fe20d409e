import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/model.dart';
import '../model/product_review_model/product_review_model.dart';
import '../services/product_service.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';

class ProductViewController extends GetxController {
  late final Dio _dio;
  late final ProductService _productService;

  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _productService = ProductService(_dio);
  }

  int _carouselIndex = 0;
  // ignore: non_constant_identifier_names
  // RxInt variant_index = 0.obs;
  bool favourite = false;
  double currentdiscount = 0;

  List<ProductReviewModel> reviewList = [];

  bool _productDetailsTileExpanded = false;

  int get carouselIndex => _carouselIndex;
  set carouselIndex(int val) {
    _carouselIndex = val;
    update();
  }

  bool get productDetailsTileExpanded => _productDetailsTileExpanded;
  set productDetailsTileExpanded(bool val) {
    _productDetailsTileExpanded = val;
    update();
  }

  Future<Item?> getItemById(String itemId) async {
    try {
      final result = await _productService.getProductbyID(productId: itemId);

      return result;
    } catch (e, st) {
      log('[RAPSAP]: Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
      return null;
    }
  }

  Future<List<Item>> getItems({int page = 1, int limit = 10}) async {
    try {
      final ResponseModel<ItemListModel?> itemListModel =
          await _productService.getItems(page: page, limit: limit);

      if (itemListModel.success ?? false) {
        return itemListModel.data?.docs ?? [];
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
    return [];
  }
}
