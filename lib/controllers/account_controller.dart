import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../model/order/order_list_model/order_list_model.dart';
import '../services/database_helper.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import '../view/screens/login/login_screen.dart';
import '../view/widgets/loading_screen.dart';
import '../view/widgets/widgets.dart';
import '../model/address_model.dart';
import '../services/account_services.dart';
import '../main.dart';
import 'cart_controlller.dart';
import 'order_controller.dart';
import 'user_controller.dart';

class AccountController extends GetxController {
  late final Dio _dio;
  late final AccountService _accountService;
  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(baseUrl: baseURL))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _accountService = AccountService(_dio);
  }

  final UserController userController = Get.find<UserController>();

  Rx<AddressModel> addressModel = AddressModel().obs;
  Rxn<Address> selectedAddress = Rxn<Address>();
  RxBool loading = false.obs;

  Future<AddressModel?> getaddress({bool? isOnSplashScreen}) async {
    if (userController.userdata.value != null) {
      loading.value = true;
      update();
      try {
        final result = await _accountService.getAddress();
        addressModel.value = result!;

        if (addressModel.value.data?.isNotEmpty ?? false) {
          // check if the selected address is present in the nely fetched list
          // if (selectedAddress.value != null) {
          //   final check = addressModel.value.data?.where(
          //     (element) => element.id == selectedAddress.value?.id,
          //   );
          //   if (check?.isNotEmpty ?? false) {
          //     selectedAddress.value = check?.firstOrNull;
          //   }
          // } else {
          selectedAddress.value = addressModel.value.data?.firstOrNull;
          // }
        }

        return result;
      } catch (e, st) {
        log('Error: $e,\nStacktrace: $st');
        Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
      } finally {
        loading.value = false;
        update();
      }
    }
    return null;
  }

  Future<void> createAddress({
    String? id,
    String? name,
    String? phone,
    String? type,
    String? line1,
    String? line2,
    String? landmark,
    String? state,
    String? city,
    String? pincode,
    double? longitude,
    double? latitude,
  }) async {
    if (userController.userdata.value != null) {
      loading.value = true;
      update();
      try {
        final result = await _accountService.createAddress(
          id: id,
          name: name,
          phone: phone,
          type: type,
          line1: line1,
          line2: line2,
          landmark: landmark,
          state: state,
          city: city,
          pincode: pincode,
          latitude: latitude,
          longitude: longitude,
        );
        if (result) {
          getaddress();

          Future.delayed(const Duration(milliseconds: 500), () async {
            loading.value = false;
            update();
            Get.close(1);
          });
          Utils.customToast(
              message:
                  "Address ${id != null ? "Updated" : "Created"} successfully");
        }
      } catch (e, st) {
        log('Error: $e,\nStacktrace: $st');
        Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
      } finally {
        loading.value = false;
        update();
      }
    }
  }

  Future logoutUser() async {
    try {
      Get.to(() => const LoadingScreen(
            text: 'Logging out',
          ));

      GetStorage('cart').erase();
      final cartController = Get.find<CartController>();
      final OrderController orderController = Get.find<OrderController>();

      userController.userdata.value = null;

      DatabaseHelper.instance.clearAllOrders();
      DatabaseHelper.instance.clearShopingCart();
      DatabaseHelper.instance.clearPayment();

      addressModel.value = AddressModel();
      await cartController.clearCart();
      orderController.orderListModel.value = OrderListModel();

      selectedAddress.value = null;

      Future.delayed(
        const Duration(seconds: 1),
        () => Get.offAll(() => const LoginScreen()),
      );
      storage.erase();
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
  }

  Future<void> deleteAddress(String addressID) async {
    loading.value = true;
    update();

    try {
      await _accountService.deleteAddress(addressID.toString());

      await getaddress();
      if (addressModel.value.data?.isEmpty ?? false) {
        selectedAddress.value = null;
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    } finally {
      loading.value = false;
      update();
    }
  }
}
