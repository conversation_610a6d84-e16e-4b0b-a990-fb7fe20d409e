import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/model.dart';
import '../services/product_service.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';

class SearchController extends GetxController {
  late final Dio _dio;
  late final ProductService _productService;

  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors
          .add(AwesomeDioInterceptor(logger: log, logResponseHeaders: false));
    _productService = ProductService(_dio);
  }

  ItemListModel searchresults = ItemListModel();

  RxList<String> searchlist = <String>[].obs;
  RxInt searchtotal = 0.obs;

  RxBool searchLoading = false.obs;
  final TextEditingController textEditingController = TextEditingController();

  bool nextpageloading = false;

  Future<void> getproducts({
    query,
    int page = 1,
    int limit = 10,
  }) async {
    if (page == 1) {
      searchLoading.value = true;
    } else {
      nextpageloading = true;
    }
    update();
    try {
      final ResponseModel<ItemListModel?> result =
          await _productService.getItems(page: page, searchQuery: query);

      if ((result.success ?? false) && result.data != null) {
        if (page == 1) {
          searchresults = result.data!;
        } else {
          searchresults.docs.addAll(result.data!.docs);
        }
        searchtotal.value = result.data?.totalCount ?? 0;
      }
    } catch (e, st) {
      log("Error: $e\nstacktrace: $st");
      Utils.customToast(
        message: e.toString(),
        backgroundColor: Colors.red,
      );
    } finally {
      if (page == 1) {
        searchLoading.value = false;
      } else {
        nextpageloading = false;
      }
      update();
    }
  }

  @override
  void onClose() {
    textEditingController.dispose();
    super.onClose();
  }
}
