import 'dart:async';
import 'package:get/get.dart';

class CountdownTimerController extends GetxController {
  int sCount = 30;

  late Timer _timer;
  @override
  void onInit() {
    stateTimerStart();
    super.onInit();
  }

  void stateTimerStart() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (sCount > 0) {
        sCount--;
        update();
      } else {
        _timer.cancel();
      }
    });
  }
}
