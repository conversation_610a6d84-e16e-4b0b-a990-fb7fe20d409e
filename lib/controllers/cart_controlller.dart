import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../model/coupon_list_model.dart';
import '../model/database_models/database_models.dart';
import '../model/items_not_in_stock.dart';
import '../services/cart_service.dart';
import '../services/database_helper.dart';
import '../utils/constants.dart';
import 'user_controller.dart';

class CartController extends GetxController {
  late Dio _dio;
  late final CartService _cartService;

  GetStorage cartstorage = GetStorage('cart');
  RxList<LocalShoppingCart> myCartItems = <LocalShoppingCart>[].obs;
  List<ItemNotInStock> notInStockItems = [];
  RxBool loading = true.obs;
  bool cartItemsLoading = false;
  Rx<double> totalprice = 0.00.obs;
  bool rebuild = false;
  Coupon? appliedCoupon;
  bool buttonloading = false;
  bool orderbuttonloading = false;
  double height = 0;

  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _cartService = CartService(_dio);
  }

  Future<void> clearCart() async {
    await DatabaseHelper.instance.clearShopingCart();
    myCartItems.clear();
    update();
  }

  Future<void> getCartList() async {
    List<LocalShoppingCart> list = await DatabaseHelper.instance.getGroceries();
    myCartItems.value = list;
    loading.value = false;
    await loadCart();
    update();
  }

  Future<void> loadCart({List<LocalShoppingCart>? items}) async {
    cartItemsLoading = true;
    update();
    try {
      final result = await _cartService.loadCart(
        storeId: Get.find<UserController>().nearbyStore?.id,
        items: items ?? myCartItems,
      );
      if (result != null) {
        Fluttertoast.showToast(
          msg: result.message ?? 'Something went wrong',
          backgroundColor: Colors.red,
        );
        notInStockItems.clear();
        if (result.data.isNotEmpty) {
          notInStockItems.addAll(result.data);
        }
      }
    } on DioException catch (e) {
      Fluttertoast.showToast(
        msg: 'Error ${e.response?.statusCode}',
        backgroundColor: Colors.red,
      );
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Fluttertoast.showToast(
        msg: 'Error $e',
        backgroundColor: Colors.red,
      );
    } finally {
      cartItemsLoading = false;
      update();
    }
  }

  Future<List<Coupon>> getOffers() async {
    try {
      final result = await _cartService.getOffers();
      if (result.success ?? false) {
        return result.data.couponList;
      }
      Fluttertoast.showToast(
        msg: '${result.message}',
        backgroundColor: Colors.red,
      );
      return [];
    } on DioException catch (e) {
      Fluttertoast.showToast(
        msg: 'Error ${e.response?.statusCode}',
        backgroundColor: Colors.red,
      );
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Fluttertoast.showToast(
        msg: 'Error $e',
        backgroundColor: Colors.red,
      );
    }
    return [];
  }
}
