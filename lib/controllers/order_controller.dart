import 'dart:developer';
import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/model.dart';
import '../model/order/order_list_model/order_list_model.dart';
import '../model/order/order_detail_model/order_detail_model.dart';
import '../model/single_order.dart';
import '../services/order_services.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import 'account_controller.dart';
import 'user_controller.dart';

class OrderController extends GetxController {
  OrderController._();
  static final OrderController _instance = OrderController._();
  factory OrderController.instance() => _instance;

  late Dio _dio;
  late OrderServices _orderService;
  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors
          .add(AwesomeDioInterceptor(logResponseHeaders: false, logger: log));
    _orderService = OrderServices(_dio);
  }

  String? currentOrderId;

  /// { orderId, order}
  Map<String, SingleOrder?> currentOrders = {};

  var orderListModel = OrderListModel().obs;
  var orderDetailModel = OrderDetailModel().obs;
  RxBool buttonloading = false.obs;
  bool loading = false;

  Future<OrderListModel?> getOrders() async {
    try {
      final result = await _orderService.getOrderHistory();
      if (result != null) {
        if (result.success == true) {
          orderListModel.value = result;
        }
      }

      return result;
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
    return null;
  }

  Future<SingleOrder?> getOrderbyId(orderid) async {
    try {
      final result = await _orderService.getSingleOrder(orderid);
      if (result.success ?? false) {
        if (result.data != null) {
          return result.data;
        }
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
    return null;
  }

  Future<String?> createOrder({
    required List<LocalShoppingCart> itemlist,
  }) async {
    try {
      final result = await _orderService.createOrder(
        address: Get.find<AccountController>().selectedAddress.value!,
        items: itemlist,
        storeId: Get.find<UserController>().nearbyStore?.id ?? '',
        // TODO: change this after setting up payments and payment methods
        paymentMethod: 'COD',
      );
      log('created order id: ${result.data}');

      return result.data?.toString();
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
    return null;
  }

  Future<void> getCurrentOrders() async {
    loading = true;
    update();
    try {
      final result = await _orderService.getAllOngoingOrders();
      // final result = await _orderService.getSingleOrder(currentOrderId);
      if (result.success ?? false) {
        currentOrders = Map.fromEntries(result.data.map(
          (e) => MapEntry<String, SingleOrder?>(e.id ?? '', e),
        ));
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    } finally {
      loading = false;
      update();
    }
  }

  int getIndexFromStatus(String? status) {
    if (status == 'received') {
      return 0;
    }
    if (['packing'].contains(status)) {
      return 1;
    }
    if (['packed', 'delivery_agent_assigned'].contains(status)) {
      return 2;
    }
    if (['out_for_delivery', 'out_for_delivery_delayed'].contains(status)) {
      return 3;
    }
    if (status == 'delivered') {
      return 4;
    }
    return -1;
  }

  Future<void> updateCurrentOrderStatus(
      String? orderId, String? orderStatus) async {
    await getCurrentOrders();
    if (orderId != null) {
      currentOrders[orderId] =
          currentOrders[orderId]?.copyWith(status: orderStatus) ??
              await getOrderbyId(orderId);
    }
  }

  Future<bool> cancelOrder({
    required String orderId,
  }) async {
    try {
      final result = await _orderService.cancelOrder(orderId);
      if (result.success ?? false) {
        Utils.customToast(
            message: result.message ?? "Order cancelled",
            backgroundColor: AppColors.primaryColor);
        return true;
      }

      Utils.customToast(message: "Cancel failed", backgroundColor: Colors.red);
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
    }
    return false;
  }
}
