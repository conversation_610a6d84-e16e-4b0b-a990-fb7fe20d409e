import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/category_model/data.dart';
import '../model/model.dart';
import '../services/category_service.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import 'order_controller.dart';
import 'user_controller.dart';

class HomeViewController extends GetxController {
  late final Dio _dio;
  late final CategoryService _categoryService;

  @override
  void onInit() async {
    super.onInit();
    _dio = Dio(BaseOptions(baseUrl: baseURL))
      ..interceptors.add(AwesomeDioInterceptor(logResponseHeaders: false));
    _categoryService = CategoryService(_dio);
    await getHomePageViews();
  }

  int _pageIndex = 0;
  RxBool favourite = false.obs;
  bool pinned = false;
  bool isCategoryLoading = false;
  RxInt selectedindex = 0.obs;
  RxString selectedfitler = "".obs;
  List<BannerAd> bannerItems = [];
  int selectedpageitem = 0;

  List<CategoryItemModel> categoryList = [];

  Future<void> getHomePageViews() async {
    if (Get.find<UserController>().userdata.value != null) {
      await getCategoriesList();
      await getBannerAds();
      await Get.find<OrderController>().getCurrentOrders();
    }
  }

  int get pageIndex => _pageIndex;
  set pageIndex(int val) {
    _pageIndex = val;
    update();
  }

  Future<List<CategoryItemModel>> getCategoriesList() async {
    isCategoryLoading = true;
    update(['categories', 'home']);
    update();
    try {
      final category = await _categoryService.getCategoryList(
          storeId: Get.find<UserController>().nearbyStore?.id ?? '');
      if (category.success ?? false) {
        categoryList = category.data?.data ?? [];
        return categoryList;
      }
    } catch (e, st) {
      Utils.customToast(message: e.toString(), backgroundColor: Colors.red);
      log('Error: $e,\nStacktrace: $st');
    } finally {
      isCategoryLoading = false;
      update(['categories', 'home']);
    }
    return [];
  }

  Future<void> getBannerAds() async {
    try {
      final ResponseModel<List<BannerAd>> result =
          await _categoryService.getBannerAds();
      if (result.data.isNotEmpty) {
        bannerItems
          ..clear()
          ..addAll(result.data);
      }
    } catch (e, st) {
      log('Error: $e,\nStacktrace: $st');
      Utils.customToast(
        message: e.toString(),
        backgroundColor: Colors.red,
      );
    } finally {
      update(['home', 'banners']);
    }
  }
}
