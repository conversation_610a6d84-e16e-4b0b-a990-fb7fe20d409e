import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../model/google_places/autocomplete_prediction.dart';
import '../services/location_service.dart';
import '../utils/utils.dart';
import '../view/screens/map_screen/search_page.dart';

class LocationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => LocationController());
  }
}

class LocationController extends GetxController {
  late final TextEditingController searchController;
  RxList<AutocompletePrediction> predictionList =
      <AutocompletePrediction>[].obs;

  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  String pincode = '';
  bool stateloading = false;

  Future<LatLng?> getlocation() async {
    stateloading = true;
    update();
    try {
      final position = await determinePosition();
      return LatLng(position.latitude, position.longitude);
    } catch (e) {
      Utils.customToast(
        message: "Error getting location, Check your location settings",
        backgroundColor: Colors.red,
      );
      return null;
    } finally {
      stateloading = false;
      update();
    }
  }

  Future<void> searchPlace() async {
    try {
      final result = await LocationService.placesAutoComplete(
          searchController.text.trim());
      if (result.predictions?.isNotEmpty ?? false) {
        predictionList
          ..clear()
          ..addAll(result.predictions ?? <AutocompletePrediction>[]);
      }
    } catch (e, st) {
      log("Error: $e\nStacktrace: $st");
      Utils.customToast(
        message: e.toString(),
        backgroundColor: Colors.red,
      );
    }
  }
}
