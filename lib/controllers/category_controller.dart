import 'dart:developer';

import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/category_model/data.dart';
import '../model/model.dart';
import '../services/product_service.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';

class CategoryController extends GetxController {
  CategoryItemModel? selectedCategory;
  CategoryItemModel? selectedSubCategory;

  List<Item> categoryproductmodel = [];
  RxBool itemsLoading = RxBool(false);
  bool nextpageloading = false;
  RxInt totalResults = 0.obs;

  late final Dio _dio;
  late final ProductService _productService;

  @override
  void onInit() {
    super.onInit();
    _dio = Dio(BaseOptions(
      baseUrl: baseURL,
      validateStatus: (_) => true,
    ))
      ..interceptors
          .add(AwesomeDioInterceptor(logResponseHeaders: false, logger: log));
    _productService = ProductService(_dio);
  }

  void selectParentCategory(CategoryItemModel category) {
    selectedCategory = category;
    update();
    selectedSubCategory = category.subCategories.firstOrNull;
    getproducts();
  }

  // Future<List<CategoryProducts?>> getProductsByCategory(
  //     {required String categoryId,
  //     String? type,
  //     int page = 0,
  //     String? subcategoryid,
  //     String? filter}) async {
  //   final res = await CategoryService.getProductsByCategory(
  //       categoryId: categoryId,
  //       type: type,
  //       page: page,
  //       subcategoryid: subcategoryid,
  //       filter: filter);

  //   if (res != null) {
  //     if (res.success == true) {
  //       if (type == "similar") {
  //         categoryproductmodel = res.data!;
  //         return categoryproductmodel;
  //       }
  //       if (res.data!.isNotEmpty) {
  //         if (subcategorymodel[selectedindex]!.subCategoryId ==
  //             res.data!.first.subCategoryId) {
  //           if (page == 0) {
  //             categoryproductmodel = res.data!;
  //           } else {
  //             categoryproductmodel = categoryproductmodel + res.data!;
  //           }

  //           if (subcategorymodel[selectedindex]!.subCategoryId ==
  //               res.data!.first.subCategoryId) {
  //             loading = false;
  //             update();
  //           }
  //         }
  //       } else {
  //         loading = false;
  //       }
  //       nextpageloading = false;
  //       update();

  //       categorytotal.value = res.meta!.total!;

  //       update();
  //     }
  //   }

  //   return categoryproductmodel;
  // }

  // Future<List<SubCategoryData?>> getSubcategoryList({
  //   required String categoryId,
  // }) async {
  //   final res = await CategoryService.getSUbCategoryList(
  //     categoryId: categoryId,
  //   );

  //   if (res != null) {
  //     if (res.success == true) {
  //       if (selectedcategoryid == categoryId) {
  //         subcategorymodel = res.data!;
  //         subcategoryloading = false;
  //       }

  //       update();
  //     }
  //   }

  //   return subcategorymodel;
  // }

  // void dispose() {
  //   scrollController.dispose();

  //   super.dispose();
  // }
  Future<void> getproducts({
    int page = 1,
    int limit = 10,
  }) async {
    if (page == 1) {
      itemsLoading.value = true;
    } else {
      nextpageloading = true;
    }
    update();
    try {
      final ResponseModel<ItemListModel?> result =
          await _productService.getItems(
              page: page,
              categoryId: selectedSubCategory?.id ?? selectedCategory?.id);
      if ((result.success ?? false) && result.data != null) {
        if (page == 1) {
          categoryproductmodel.clear();
        }
        categoryproductmodel.addAll(result.data!.docs);
        totalResults.value = result.data!.totalCount ?? 0;
      }
    } catch (e, st) {
      log("Error: $e\nstacktrace: $st");
      Utils.customToast(
        message: e.toString(),
        backgroundColor: Colors.red,
      );
    } finally {
      if (page == 1) {
        itemsLoading.value = false;
      } else {
        nextpageloading = false;
      }
      update();
    }
  }
}
