import 'dart:io';

import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'services/firebase_services.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:get_storage/get_storage.dart';

import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (Platform.isAndroid) {
    await FlutterDisplayMode.setHighRefreshRate();
  }
  await dotenv.load(fileName: ".env");

  await FirebaseService.init();
  // InternetConnectivity().initConnectivity();
  await GetStorage.init();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const Rapsap());
  });
}

GetStorage storage = GetStorage();
