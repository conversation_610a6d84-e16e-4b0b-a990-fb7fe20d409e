import 'package:flutter/material.dart';

import '../../utils/colors.dart';

class AnimatedRisingCard extends StatefulWidget {
  final Widget child;
  final int delay;
  final Color? backgroundColor;
  final List<BoxShadow>? boxShadow;
  final BorderRadiusGeometry? borderRadius;
  final double height;
  final EdgeInsetsGeometry? padding;
  final Gradient? gradient;

  const AnimatedRisingCard({
    super.key,
    required this.child,
    required this.height,
    this.delay = 10,
    this.backgroundColor,
    this.boxShadow,
    this.borderRadius,
    this.padding,
    this.gradient,
  });

  @override
  State<AnimatedRisingCard> createState() => _AnimatedRisingCardState();
}

class _AnimatedRisingCardState extends State<AnimatedRisingCard> {
  double height = 0;

  @override
  void initState() {
    Future.delayed(const Duration(milliseconds: 10), () {
      setState(() {
        height = widget.height;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      padding: widget.padding,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: widget.gradient,
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        boxShadow: widget.boxShadow ??
            [
              BoxShadow(
                color: AppColors.kgrey,
                blurRadius: 5.0,
              ),
            ],
      ),
      duration: const Duration(milliseconds: 100),
      height: height,
      child: widget.child,
    );
  }
}
