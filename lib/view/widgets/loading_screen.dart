import 'package:flutter/material.dart';

import '../../utils/colors.dart';

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key, required this.text});
  final String text;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator.adaptive(
            strokeWidth: 5,
            backgroundColor: AppColors.kgrey,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.black),
          ),
          const SizedBox(height: 20),
          Text(
            text,
            style: const TextStyle(fontWeight: FontWeight.bold),
          )
        ],
      )),
    );
  }
}
