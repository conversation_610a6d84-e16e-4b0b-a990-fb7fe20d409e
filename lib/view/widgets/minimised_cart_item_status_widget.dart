import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../controllers/cart_controlller.dart';
import '../../services/database_helper.dart';
import '../screens/cart/cart_screen.dart';
import 'animated_rising_card.dart';

class MinimisedCartItemStatusWidget extends StatelessWidget {
  const MinimisedCartItemStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CartController>(
      builder: (cartController) {
        return Visibility(
          visible: cartController.myCartItems.isNotEmpty,
          child: AnimatedRisingCard(
            height: 70,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.86),
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  '${cartController.myCartItems.fold<int>(0, (previousValue, element) => previousValue + element.qty!)} item',
                  style: textStyle,
                ),
                const SizedBox(width: 5),
                Text('•', style: textStyle),
                const SizedBox(width: 5),
                FutureBuilder<double>(
                  future: DatabaseHelper.instance.getSubTotal(),
                  builder: (context, AsyncSnapshot<double> snapshot) {
                    return Text(
                      "\u20b9${snapshot.data != null ? snapshot.data!.round() : ""}",
                      style: GoogleFonts.inter(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        height: 26.4 / 20,
                        letterSpacing: -0.06,
                      ),
                    );
                  },
                ),
                const Spacer(),
                Directionality(
                  // to put icon on right
                  textDirection: TextDirection.rtl,
                  child: TextButton.icon(
                    onPressed: () {
                      Get.to(() => const CartScreen());
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                      textStyle: textStyle.copyWith(
                        fontSize: 18,
                        height: 23.44 / 18,
                        letterSpacing: -0.02,
                      ),
                    ),
                    icon: SvgPicture.asset(
                      'assets/svg/cart-bag.svg',
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                    label: const Text('View Cart'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  TextStyle get textStyle => const TextStyle(
    color: Colors.white,
    fontWeight: FontWeight.w700,
    fontSize: 20,
    height: 26.4 / 20,
    letterSpacing: -0.06,
  );
}
