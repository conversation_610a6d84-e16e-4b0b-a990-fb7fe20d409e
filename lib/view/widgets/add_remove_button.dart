import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

import '../../controllers/cart_controlller.dart';
import '../../model/model.dart';
import '../../services/database_helper.dart';
import '../../utils/colors.dart';
import '../../utils/enumerations.dart';
import '../../utils/utils.dart';
import '../screens/map_screen/select_location_screen.dart';

class AddRemoveButton extends StatefulWidget {
  const AddRemoveButton({super.key, required this.model});
  final Item model;

  @override
  State<AddRemoveButton> createState() => _AddRemoveButtonState();
}

class _AddRemoveButtonState extends State<AddRemoveButton> {
  double? get discount => widget.model.discount?.type == DiscountType.currency
      ? widget.model.discount?.value
      : widget.model.price.toDouble() * (widget.model.discount?.value ?? 0.0);
  double get taxInRupees => widget.model.tax.fold<double>(
        0.0,
        (prev, e) =>
            prev + (((e.value ?? 0).toDouble() / 100.0) * widget.model.price),
      );
  @override
  Widget build(BuildContext context) {
    final availableStock = widget.model.availableStock;
    return GetBuilder<CartController>(
      init: CartController(),
      builder: (cartController) {
        return FutureBuilder<int>(
          future: DatabaseHelper.instance.getQty(
            LocalShoppingCart(
              productID: widget.model.id!,
              purchaseOrderID: widget.model.purchaseOrderId!,
            ),
          ),
          builder: (context, snapshot) {
            if (snapshot.data == null) {
              return SizedBox(
                height: 30,
                width: 80,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    CustomAppShimmer(
                      child: Container(
                        height: 30,
                        width: 65,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          color: AppColors.kgrey.withOpacity(0.5),
                        ),
                      ),
                    ),
                    Shimmer.fromColors(
                      baseColor: Colors.black.withOpacity(0.1),
                      highlightColor: Colors.white,
                      child: const Text(
                        "Add",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
            if (snapshot.data == 0) {
              return InkWell(
                onTap: () async {
                  if (availableStock != 0) {
                    await DatabaseHelper.instance.addUpdate(
                      LocalShoppingCart(
                        mrp: widget.model.mrp,
                        name: widget.model.name,
                        qty: 1,
                        productID: widget.model.id!,
                        purchaseOrderID: widget.model.purchaseOrderId!,
                        price: widget.model.price,
                        imageURL: widget.model.imageUrls.firstOrNull,
                        availableStock: availableStock,
                        discount: discount,
                        tax: taxInRupees,
                      ),
                    );
                    setState(() {});
                  } else {
                    Utils.customToast(
                      message:
                          "Running low on stock.\nPlease add it to Wishlist to get a notification once the item is restocked",
                      backgroundColor: Theme.of(context).primaryColor,
                      timeInSecForIosWeb: 5,
                    );
                  }
                },
                child: Container(
                  height: 36,
                  width: 84,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: availableStock != 0
                          ? Theme.of(context).primaryColor
                          : AppColors.kgrey,
                    ),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Text(
                    "Add",
                    style: TextStyle(
                      color: availableStock == 0
                          ? AppColors.kgrey
                          : Theme.of(context).primaryColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              );
            } else {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 100),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Theme.of(context).primaryColor),
                  borderRadius: BorderRadius.circular(2),
                ),
                height: 36,
                width: 84,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Flexible(
                      child: InkWell(
                        onTap: () async {
                          await DatabaseHelper.instance.removeUpdate(
                            LocalShoppingCart(
                              productID: widget.model.id!,
                              purchaseOrderID: widget.model.purchaseOrderId!,
                            ),
                          );
                          setState(() {});
                        },
                        child: Icon(
                          Icons.remove_outlined,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Text(
                        "${snapshot.data ?? ""}",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    Flexible(
                      child: InkWell(
                        onTap: () async {
                          if (availableStock > (snapshot.data ?? 0)) {
                            await DatabaseHelper.instance.addUpdate(
                              LocalShoppingCart(
                                productID: widget.model.id!,
                                purchaseOrderID: widget.model.purchaseOrderId!,
                              ),
                            );
                            setState(() {});
                          } else {
                            Utils.customToast(
                              message:
                                  "Running low on stock.\nPlease add it to Wishlist to get a notification once the item is restocked",
                              backgroundColor: Theme.of(context).primaryColor,
                              timeInSecForIosWeb: 5,
                            );
                          }
                        },
                        child: Icon(
                          Icons.add,
                          color: availableStock > (snapshot.data ?? 0)
                              ? Theme.of(context).primaryColor
                              : AppColors.kgrey,
                        ),
                      ),
                    )
                  ],
                ),
              );
            }
          },
        );
      },
    );
  }
}
