import 'dart:async';
import 'dart:developer';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';


class InternetConnectivity {
  List<ConnectivityResult> connectionStatus = [ConnectivityResult.none];
  final Connectivity connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> connectivitySubscription;

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      log('Couldn\'t check connectivity status', error: e);
      return;
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    // if (!mounted) {
    //   return Future.value(null);
    // }

    updateConnectionStatus(result);
  }

  Future<void> updateConnectionStatus(List<ConnectivityResult> result) async {
    log("$result");
    connectionStatus = result;
    if (connectionStatus.contains(ConnectivityResult.none)) {
      InternetCheckStatus.showInternetConnectionStatus();
    } else {
      InternetCheckStatus.hideInternetStatusDialog();
    }
  }
}

class InternetCheckStatus {
  static void showInternetConnectionStatus() {
    Get.isSnackbarOpen ? Get.closeAllSnackbars() : null;
    Future.delayed(const Duration(milliseconds: 0)).then(
      (_) {
        Get.dialog(
          Dialog(
            elevation: 0,
            backgroundColor: Colors.white,
            alignment: Alignment.center,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: const OfflineAlertWidget(),
          ),
          barrierDismissible: false,
          barrierColor: Colors.black.withOpacity(0.5),
        );
      },
    );
  }

  static void hideInternetStatusDialog() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }
}

class OfflineAlertWidget extends StatelessWidget {
  const OfflineAlertWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16 * 3),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset("assets/images/offline.webp"),
          const SizedBox(height: 20),
          const Text(
            'You are offline',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16 / 2),
          Text(
            'Check your internet connection',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Colors.black.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}
