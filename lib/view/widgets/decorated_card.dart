import 'package:flutter/material.dart';

import '../../utils/colors.dart';

class DecoratedCard extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry padding;
  final Color? color;
  final double? borderRadius;

  const DecoratedCard({
    super.key,
    this.child,
    this.margin,
    this.padding = const EdgeInsets.all(16),
    this.color,
    this.borderRadius,
  });
  final Widget? child;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.kDecoratedCardBorder),
        borderRadius: BorderRadius.circular(borderRadius ?? 10),
        color: color ?? Colors.white,
      ),
      child: child,
    );
  }
}
