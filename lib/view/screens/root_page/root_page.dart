// ignore_for_file: deprecated_member_use

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:upgrader/upgrader.dart';

import '../../../controllers/account_controller.dart';
import '../../../controllers/order_controller.dart';
import '../../../main.dart';
import '../../widgets/internet_connectivity.dart';
import '../home_screen/home_screen.dart';
import '../../../controllers/cart_controlller.dart';
import '../account/delete_account.dart';

class RootPage extends StatefulWidget {
  const RootPage({
    super.key,
    this.data,
  });
  final bool? data;

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> with WidgetsBindingObserver {
  final ordercontroller = Get.find<OrderController>();
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    // InternetConnectivity().initConnectivity();

    // InternetConnectivity().connectivitySubscription = InternetConnectivity()
    //     .connectivity
    //     .onConnectivityChanged
    //     .listen(listenInternetChanges);
    showdialog();

    setState(() {});
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final currentOrderId = storage.read('currentOrderId')?.toString();
      ordercontroller
        ..currentOrderId = currentOrderId
        ..getCurrentOrders();
      // UserService.getUserConfig("home");

      // if (userController.tester.value != true) {
      //   Get.offAll(() => const LaunchingSoon());
      //   return;
      // }
    });

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // InternetConnectivity().connectivitySubscription.cancel();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      ordercontroller.getCurrentOrders();
      // InternetConnectivity().initConnectivity();
    }
  }

  void listenInternetChanges(List<ConnectivityResult> event) {
    if (mounted) {
      setState(() {});
    }

    InternetConnectivity().updateConnectionStatus(event).then((value) {
      showdialog();
    });
  }

  Future<void> showdialog() async {
    // if (widget.data == true) {
    //   Future.delayed(Duration(milliseconds: 200), (() async {
    //     // await deleteddialog(context);

    //     // setState(() {});
    //   }));
    // }

    // final UserController userController = Get.find();

    if (widget.data == true) {
      Future.delayed(const Duration(milliseconds: 200), (() async {
        await deletestatusdialog(context);

        // setState(() {});
      }));
    }
  }

  final ScrollController scrollController = ScrollController();

  final AccountController accountController =
      Get.put<AccountController>(AccountController());

  final CartController cartController =
      Get.put<CartController>(CartController());

  @override
  Widget build(BuildContext context) {
    // deletestatusdialog(context);

    return WillPopScope(
      onWillPop: () async {
        SystemNavigator.pop(animated: true);
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: UpgradeAlert(
          child: const HomeScreen(),
        ),
      ),
    );
  }

  Widget getSvgIcon(String icon, Color color) => SvgPicture.asset(
        icon,
        color: color,
        semanticsLabel: 'Svg',
      );
}
