// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../login/widgets/button.dart';

class SubscriptionComingSoon extends StatelessWidget {
  const SubscriptionComingSoon({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        foregroundColor: Theme.of(context).primaryColor,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              "assets/svg/subscribtion.svg",
              fit: BoxFit.cover,
              color: Colors.black.withOpacity(0.3),
              height: 125,
            ),
            const SizedBox(height: 50),
            const Text(
              "Subscription Coming soon !",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.w700),
            ),
            SubmitButton(
                text: 'Come back later',
                onpressed: () {
                  Get.back();
                }).paddingAll(24),
            const SizedBox(
              height: 50,
            )
          ],
        ),
      ),
    );
  }
}
