import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../controllers/location_controller.dart';
import '../../../main.dart';
import '../../../model/user_model/user_model.dart';
import '../../../controllers/account_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../services/firebase_services.dart';
import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../login/login_screen.dart';
import '../map_screen/allow_location_screen.dart';
import '../root_page/root_page.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final UserController _userController = Get.find<UserController>();
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        // FocusManager.instance.primaryFocus?.unfocus();
        // FirebaseService.firebaseAnalytics.logAppOpen();
        if (storage.read('userdata') == null) {
          Get.off(() => const LoginScreen());
        } else {
          check();
        }
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        constraints: const BoxConstraints.expand(),
        decoration: BoxDecoration(
          color: AppColors.splashBackgroundColor,
          image: const DecorationImage(image: AssetImage(splashbackground)),
        ),
        child: Center(
          child: SvgPicture.asset(logo),
        ),
      ),
    );
  }

  Future<void> check() async {
    try {
      _userController.userdata.value =
          UserData.fromJson(storage.read('userdata'));
      const encoder = JsonEncoder.withIndent(' ');
      log('logged in user found: ${encoder.convert(_userController.userdata.value)}');
      FirebaseService.firebaseAnalytics.setUserId(
        id: _userController.userdata.value?.id.toString(),
      );

      final accountController = Get.find<AccountController>();
      await accountController.getaddress(isOnSplashScreen: true);

      final selectedAddressId = storage.read<String>('selectedAddressId');
      final coordinates = storage
          .read<List>('coordinates')
          ?.map(
            (e) => double.tryParse(e.toString()) ?? 0.0,
          )
          .toList();
      if (selectedAddressId != null) {
        log('Selected address id found: $selectedAddressId');
        accountController.selectedAddress.value =
            accountController.addressModel.value.data?.firstWhereOrNull(
          (element) => element.id == selectedAddressId,
        );
        if (accountController.selectedAddress.value?.geoLocation?.coordinates !=
            null) {
          await getStoreAndProceed(accountController
              .selectedAddress.value?.geoLocation?.coordinates);
        }
      } else if (coordinates != null) {
        log('Coordinates found: $coordinates');
        _userController.coordinates = coordinates;
        await getStoreAndProceed(coordinates);
      } else {
        Get.off(
          () => const AllowLocationScreen(),
          binding: LocationBinding(),
        );
      }
    } catch (e, st) {
      log('Error: $e,\nStackTrace: $st');
      Get.off(() => const LoginScreen());
    }
  }

  Future<void> getStoreAndProceed(List<double>? coordinates) async {
    final result = await _userController.getNearbyStore(coordinates);
    if (result) {
      Get.offAll(() => const RootPage());
    } else {
      Get.off(
        () => const AllowLocationScreen(),
        binding: LocationBinding(),
      );
    }
  }
}
