import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';

import 'widgets/button.dart';
import '../../widgets/widgets.dart';
import '../../../controllers/user_controller.dart';
import '../../widgets/keyboard_hider.dart';

class PersonalDetailsScreen extends GetView<UserController> {
  PersonalDetailsScreen({
    super.key,
    // this.userid,
    this.phonenumber,
  });

  /// user id is never received from server
  // final int? userid;

  final String? phonenumber;
  final UserController usrcontroller = Get.find();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  // final TextEditingController _pwdController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: const Color(0xffF1F2F2),
        appBar: AppBar(
          backgroundColor: const Color(0xffF1F2F2),
          toolbarHeight: 100,
          titleSpacing: 16,
          elevation: 0,
          title: const Text(
            'Personal Details',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          automaticallyImplyLeading: false,
          foregroundColor: Theme.of(context).primaryColor,
        ),
        body: KeyboardHider(
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          const SizedBox(height: 10),
                          FieldText(
                              hint: 'Enter Name',
                              label: 'Name',
                              txtcontroller: _nameController),
                          const SizedBox(height: 10),
                          FieldText(
                              hint: 'Enter Email ',
                              label: 'Email',
                              txtcontroller: _emailController),
                          const SizedBox(height: 30),
                          const SizedBox(height: 20),
                        ],
                      )),
                ),
              ),
              Align(
                  alignment: Alignment.bottomCenter,
                  child: Obx(
                    () => SizedBox(
                      height: 56,
                      child: usrcontroller.buttonloading.value
                          ? SizedBox(
                              height: 56,
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: null,
                                child: SizedBox(
                                  height: 20,
                                  child: LoadingIndicator(
                                    colors: [
                                      Theme.of(context).primaryColor,
                                      Colors.black,
                                    ],
                                    indicatorType: Indicator.cubeTransition,
                                  ),
                                ),
                              ),
                            )
                          : SubmitButton(
                              text: 'Continue',
                              onpressed: () {
                                if (_formKey.currentState!.validate()) {
                                  if (_emailController.text.trim().isNotEmpty &&
                                      _nameController.text.trim().isNotEmpty) {
                                    controller
                                        .updateUserDetails(
                                            email: _emailController.text.trim(),
                                            name: _nameController.text.trim())
                                        .then(
                                      (value) {
                                        if (value) {
                                          Get.back();
                                        }
                                      },
                                    );
                                  } else {
                                    Fluttertoast.showToast(
                                      msg: 'enter all valid details',
                                      backgroundColor: Colors.red,
                                    );
                                  }
                                }
                              },
                            ),
                    ).paddingAll(16),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}

class FieldText extends GetView<UserController> {
  const FieldText({
    super.key,
    this.obscure = false,
    required this.label,
    required this.hint,
    required this.txtcontroller,
  });
  final String label;
  final String hint;
  final bool obscure;
  final TextEditingController txtcontroller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style:
              const TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
        ),
        const SizedBox(height: 10),
        SizedBox(
          child: TextFormField(
            controller: txtcontroller,
            obscureText: obscure,

            decoration: InputDecoration(
                hintStyle: const TextStyle(color: Colors.black),
                isDense: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.all(14),
                filled: true,
                focusedBorder: OutlineInputBorder(
                    borderSide:
                        const BorderSide(color: Colors.black, width: 1.0),
                    borderRadius: BorderRadius.circular(2)),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide.none,
                    borderRadius: BorderRadius.circular(2)),
                border: OutlineInputBorder(
                    borderSide:
                        const BorderSide(color: Colors.black, width: 1.0),
                    borderRadius: BorderRadius.circular(2)),
                focusedErrorBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Colors.red.shade300, width: 1),
                    borderRadius: BorderRadius.circular(2)),
                errorBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Colors.red.shade300, width: 1),
                    borderRadius: BorderRadius.circular(2)),
                hintText: hint,
                suffix: label == 'Password'
                    ? GestureDetector(
                        onTap: () {
                          controller.showpassword.value =
                              !controller.showpassword.value;
                        },
                        child: Obx(() => Text(
                              controller.showpassword.value ? 'Show' : 'Hide',
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500),
                            )))
                    : null),
            // The validator receives the text that the user has entered.
            validator: (value) {
              return GetUtils.isBlank(value)!
                  ? 'cannot be empty'
                  : label == 'Email'
                      ? GetUtils.isEmail(value!)
                          ? null
                          : 'Enter valid email'
                      : label == 'Name'
                          ? value!.length < 4
                              ? 'Enter valid name'
                              : null
                          : label == 'Password'
                              ? value!.length < 6
                                  ? 'Min 6 characters required'
                                  : null
                              : null;
            },
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}
