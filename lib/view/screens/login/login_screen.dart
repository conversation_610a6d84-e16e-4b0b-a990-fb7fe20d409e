// ignore_for_file: deprecated_member_use

// import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:carousel_slider/carousel_slider.dart' as cs;
import 'package:pinput/pinput.dart';

import '../../../controllers/user_controller.dart';
import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../../widgets/widgets.dart';
import '../../../controllers/countdown_timer_controller.dart';
import 'widgets/button.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  // static get vsync => null;
  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // getx controllers
  final carouselController = cs.CarouselSliderController();
  final UserController userController = Get.find<UserController>();

  // states
  final pinController = TextEditingController();
  final FocusNode otpCtrlFocusNode = FocusNode();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextStyle get hintStyle => TextStyle(
    color: AppColors.kgrey,
    fontWeight: FontWeight.w500,
    fontSize: 14,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black,
      body: KeyboardHider(
        child: Column(
          children: [
            // background image
            Container(
              constraints: BoxConstraints.expand(height: Get.height * 0.3),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(loginhead),
                  fit: BoxFit.cover,
                ),
              ),
              child: Center(child: SvgPicture.asset(loginheadtitle)),
            ),
            Expanded(
              child: cs.CarouselSlider(
                items: [
                  Stack(
                    children: [
                      SvgPicture.asset(
                        'assets/svg/mainbag.svg',
                        fit: BoxFit.fill,
                      ),
                      ListView(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        shrinkWrap: true,
                        children: [
                          const SizedBox(height: 20),
                          AnimatedContainer(
                            height:
                                MediaQuery.of(context).viewInsets.bottom == 0.0
                                    ? 90
                                    : 50,
                            duration: const Duration(milliseconds: 200),
                            child: Center(
                              child: Container(
                                width: 90,
                                padding: const EdgeInsets.all(5),
                                child: SvgPicture.asset(
                                  'assets/svg/hangerbag.svg',
                                ),
                              ),
                            ),
                          ),
                          const Text(
                            'Welcome',
                            style: TextStyle(
                              height: 2,
                              color: Colors.black,
                              fontWeight: FontWeight.w700,
                              fontSize: 26,
                              letterSpacing: -1.5,
                            ),
                          ),
                          const Text(
                            "Enter your whatsapp number and get started ",
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 30),
                          Form(
                            key: _formKey,
                            child: GetBuilder<UserController>(
                              builder: (controller) {
                                return TextFormField(
                                  onChanged: (value) async {
                                    // _usrController.buttonloading.value = true;
                                    // _usrController.update();
                                    // if (_usrController.phoneController.value
                                    //         .text.length ==
                                    //     10) {
                                    //   FocusScope.of(context).unfocus();
                                    //   try {
                                    //     await _usrController.sendOtp({
                                    //       "mobile": _usrController
                                    //           .phoneController.value.text
                                    //     });
                                    //   } catch (e) {
                                    //     debugPrint(e.toString());
                                    //   } finally {
                                    //     _usrController.buttonloading.value =
                                    //         false;
                                    //   }
                                    //   controller.animateToPage(1);
                                    //   otpCtrlFocusNode.requestFocus();
                                    // }
                                  },
                                  enabled: !userController.buttonloading.value,
                                  onTap:
                                      userController.buttonloading.value
                                          ? null
                                          : () {
                                            // _usrController
                                            //   ..loading = false
                                            //   ..update();
                                            pinController.clear();
                                            // if (_usrController.phoneController.value
                                            //     .text.isEmpty) {
                                            //   if (_usrController
                                            //       .numberSelected.value) {
                                            //     _usrController.getmobilenumber(
                                            //         controller, otpCtrlFocusNode);
                                            //   }
                                            // }
                                          },
                                  controller: userController.phoneController,
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(10),
                                  ],
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.white,
                                    focusedBorder: const OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Colors.black,
                                      ),
                                      borderRadius: BorderRadius.zero,
                                    ),
                                    enabledBorder: const OutlineInputBorder(
                                      borderSide: BorderSide.none,
                                      borderRadius: BorderRadius.zero,
                                    ),
                                    hintText: 'Phone Number',
                                    hintStyle: hintStyle,
                                    prefix: Text(
                                      '+91 ',
                                      style: hintStyle.copyWith(
                                        color: Colors.black,
                                      ),
                                    ),
                                    prefixIcon: Padding(
                                      padding: const EdgeInsets.all(12),
                                      child: SvgPicture.asset(
                                        "assets/svg/WhatsApp-Icon.svg",
                                        color: Colors.black,
                                        height: 10,
                                        width: 10,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 20),
                          SizedBox(
                            width: double.infinity,
                            child: ValueListenableBuilder(
                              valueListenable: userController.phoneController,
                              builder: (_, value, __) {
                                return GetBuilder<UserController>(
                                  builder: (_) {
                                    return SubmitButton(
                                      loading: userController.loading,
                                      onpressed:
                                          value.text.trim().isNotEmpty &&
                                                  value.text.length == 10
                                              ? () async {
                                                FocusScope.of(
                                                  context,
                                                ).unfocus();
                                                await userController
                                                    .sendOtp(
                                                      userController
                                                          .phoneController
                                                          .value
                                                          .text
                                                          .trim(),
                                                    )
                                                    .then((value) async {
                                                      if (value) {
                                                        await carouselController
                                                            .animateToPage(1);
                                                        pinController.clear();
                                                        otpCtrlFocusNode
                                                            .requestFocus();
                                                      }
                                                    });
                                              }
                                              : null,
                                      text: 'Get OTP',
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Column(
                          //   children: [
                          //     Row(
                          //       mainAxisAlignment:
                          //           MainAxisAlignment.spaceBetween,
                          //       children: [
                          //         Flexible(
                          //           flex: 2,
                          //           child: SvgPicture.asset(
                          //             'assets/svg/Horizontalsepleft.svg',
                          //           ),
                          //         ),
                          //         const Flexible(
                          //           child: Padding(
                          //             padding:
                          //                 EdgeInsets.symmetric(horizontal: 6.0),
                          //             child: Text(
                          //               'OR',
                          //               style: TextStyle(
                          //                 color: Color(0xffC5C8CD),
                          //               ),
                          //             ),
                          //           ),
                          //         ),
                          //         Flexible(
                          //           flex: 2,
                          //           child: SvgPicture.asset(
                          //               'assets/svg/Horizontalsepright.svg'),
                          //         )
                          //       ],
                          //     ),
                          //     const SizedBox(height: 20),
                          //     Row(
                          //       mainAxisAlignment: MainAxisAlignment.center,
                          //       children: [
                          //         InkWell(
                          //           onTap: () async {
                          //             // await Authentication.signInWithGoogle(
                          //             //     context: context);
                          //           },
                          //           child: CircleAvatar(
                          //             radius: 30,
                          //             backgroundColor: Colors.white,
                          //             child: SvgPicture.asset(
                          //                     "assets/svg/google.svg")
                          //                 .paddingAll(8),
                          //           ),
                          //         ),
                          //         InkWell(
                          //           onTap: () async {
                          //             // await Authentication.signInWithGoogle(
                          //             //     context: context);
                          //           },
                          //           child: CircleAvatar(
                          //             radius: 30,
                          //             backgroundColor: Colors.white,
                          //             child: const Icon(
                          //               Icons.apple,
                          //               size: 31,
                          //             ).paddingAll(8),
                          //           ),
                          //         )
                          //         // InkWell(
                          //         //   onTap: (() {
                          //         //     Get.to(() => RegisterScreen(
                          //         //           type: "signup",
                          //         //         ));
                          //         //   }),
                          //         //   child: CircleAvatar(
                          //         //     radius: 30,
                          //         //     backgroundColor: Colors.white,
                          //         //     child: SvgPicture.asset(
                          //         //         "assets/svg/email.svg"),
                          //         //   ),
                          //         // )
                          //       ],
                          //     ),
                          //     const SizedBox(height: 20),
                          //   ],
                          // ),
                          // Center(
                          //   child: TextButton(
                          //     onPressed: () {
                          //       storage.read('coordinates') == null
                          //           ? Get.to(
                          //               () => const AllowLocationScreen(),
                          //               binding: LocationBinding(),
                          //             )
                          //           : Get.to(() => const RootPage());
                          //       // processSkipExplore();
                          //     },
                          //     child: const Text(
                          //       'Skip Login & Explore',
                          //       style: TextStyle(
                          //         fontSize: 14,
                          //         fontWeight: FontWeight.w600,
                          //         color: Colors.kblack,
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          const SizedBox(height: 20),
                          Center(
                            child: RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                text: 'By continuing, you will agree to the\n',
                                style: TextStyle(
                                  color: const Color(
                                    0xff333333,
                                  ).withOpacity(0.8),
                                  fontWeight: FontWeight.w400,
                                  height: 1.3,
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: 'Terms and Conditions ',
                                    recognizer:
                                        TapGestureRecognizer()
                                          ..onTap = () async {
                                            if (!await launchUrl(
                                              Uri.parse(
                                                'https://rapsap.com/terms.html',
                                              ),
                                            )) {
                                              throw "Could not launch https://rapsap.com/terms.html";
                                            }
                                          },
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff5074F3),
                                    ),
                                  ),
                                  const TextSpan(text: '&'),
                                  TextSpan(
                                    text: ' Privacy Policy',
                                    recognizer:
                                        TapGestureRecognizer()
                                          ..onTap = () async {
                                            if (!await launchUrl(
                                              Uri.parse(
                                                'https://rapsap.com/terms.html',
                                              ),
                                            )) {
                                              throw "Could not launch https://https://rapsap.com/privacy.html";
                                            }
                                          },
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff5074F3),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  WillPopScope(
                    onWillPop: () async {
                      carouselController.animateToPage(0);
                      return false;
                    },
                    child: Stack(
                      children: [
                        SvgPicture.asset(
                          'assets/svg/mainbag.svg',
                          fit: BoxFit.fill,
                        ),
                        ListView(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          children: [
                            const SizedBox(height: 30),
                            AnimatedContainer(
                              alignment: Alignment.topCenter,
                              height:
                                  MediaQuery.of(context).viewInsets.bottom > 0.0
                                      ? 50
                                      : 90,
                              duration: const Duration(milliseconds: 100),
                              child: Center(
                                child: SizedBox(
                                  width: 90,
                                  child: SvgPicture.asset(
                                    'assets/svg/hangerbag.svg',
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 25),
                            Column(
                              children: [
                                Center(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Enter Verification Code',
                                        style: TextStyle(
                                          fontSize: 24,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w700,
                                          letterSpacing: -1.5,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Row(
                                        children: [
                                          const Flexible(
                                            child: Text(
                                              'We have sent SMS to:  ',
                                              style: TextStyle(
                                                color: Colors.black,
                                                letterSpacing: -0.005,
                                                fontWeight: FontWeight.w400,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: Text(
                                              '+91 ${userController.phoneController.text}',
                                              style: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.black,
                                                fontWeight: FontWeight.w700,
                                                letterSpacing: -0.005,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 20),
                                      Center(
                                        child: Pinput(
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly,
                                          ],
                                          autofocus: true,
                                          length: 6,
                                          errorPinTheme: PinTheme(
                                            width: context.width * 0.1,
                                            height: context.width * 0.1,
                                            margin: EdgeInsets.only(
                                              right: context.width * 0.03,
                                            ),
                                            textStyle: const TextStyle(
                                              fontSize: 20,
                                              color: Color.fromRGBO(
                                                30,
                                                60,
                                                87,
                                                1,
                                              ),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color: Colors.red,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                          ),
                                          focusedPinTheme: PinTheme(
                                            width: context.width * 0.1,
                                            height: context.width * 0.1,
                                            margin: EdgeInsets.only(
                                              right: context.width * 0.03,
                                            ),
                                            textStyle: const TextStyle(
                                              fontSize: 20,
                                              color: Color.fromRGBO(
                                                30,
                                                60,
                                                87,
                                                1,
                                              ),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                          ),
                                          submittedPinTheme: PinTheme(
                                            width: context.width * 0.1,
                                            height: context.width * 0.1,
                                            margin: EdgeInsets.only(
                                              right: context.width * 0.03,
                                            ),
                                            textStyle: const TextStyle(
                                              fontSize: 20,
                                              color: Color.fromRGBO(
                                                30,
                                                60,
                                                87,
                                                1,
                                              ),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                          ),
                                          keyboardType: TextInputType.number,
                                          controller: pinController,
                                          defaultPinTheme: defaultPinTheme,
                                          pinputAutovalidateMode:
                                              PinputAutovalidateMode.onSubmit,
                                          textInputAction: TextInputAction.next,
                                          showCursor: true,
                                          onChanged: (value) {
                                            otpCtrlFocusNode.requestFocus();
                                          },
                                          focusNode: otpCtrlFocusNode,
                                          onCompleted: (value) {
                                            SystemChannels.textInput
                                                .invokeMethod('TextInput.hide');

                                            userController.verifyOtp(
                                              context,
                                              phone:
                                                  userController
                                                      .phoneController
                                                      .value
                                                      .text
                                                      .trim(),
                                              otp: pinController.text.trim(),
                                            );
                                          },
                                        ),
                                      ),
                                      GetBuilder<CountdownTimerController>(
                                        init: CountdownTimerController(),
                                        initState: (_) {},
                                        builder: (countdownController) {
                                          // Use a proper variable name
                                          return Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Visibility(
                                                visible:
                                                    countdownController
                                                        .sCount <=
                                                    0,
                                                replacement: Text(
                                                  'Resend in ${countdownController.sCount} ',
                                                ),
                                                child: TextButton(
                                                  onPressed: () {
                                                    userController
                                                        .sendOtp(
                                                          userController
                                                              .phoneController
                                                              .value
                                                              .text
                                                              .trim(),
                                                        )
                                                        .then((value) {
                                                          countdownController
                                                            ..sCount = 30
                                                            ..stateTimerStart()
                                                            ..update();
                                                        });
                                                  },
                                                  child: const Text(
                                                    'Resend OTP',
                                                    style: TextStyle(
                                                      color: Color(0xff5074f3),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  FocusScope.of(
                                                    context,
                                                  ).unfocus();
                                                  carouselController
                                                      .previousPage(
                                                        duration:
                                                            const Duration(
                                                              milliseconds: 500,
                                                            ),
                                                        curve: Curves.easeInOut,
                                                      );
                                                },
                                                child: const Text(
                                                  'Change Phone Number',
                                                  style: TextStyle(
                                                    color: Color(0xFF4f4f4f),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                      const SizedBox(height: 20),
                                      GetBuilder<UserController>(
                                        builder: (building) {
                                          return building.loading
                                              ? SizedBox(
                                                height: 56,
                                                width: double.infinity,
                                                child: ElevatedButton(
                                                  onPressed: null,
                                                  child: SizedBox(
                                                    height: 20,
                                                    child: LoadingIndicator(
                                                      colors: [
                                                        Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                        Colors.black,
                                                      ],
                                                      indicatorType:
                                                          Indicator
                                                              .cubeTransition,
                                                    ),
                                                  ),
                                                ),
                                              )
                                              : SubmitButton(
                                                text: 'Continue',
                                                onpressed:
                                                    building.loading
                                                        ? null
                                                        : () {
                                                          userController.verifyOtp(
                                                            context,
                                                            phone:
                                                                userController
                                                                    .phoneController
                                                                    .text
                                                                    .trim(),
                                                            otp:
                                                                pinController
                                                                    .text,
                                                          );
                                                        },
                                              );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
                carouselController: carouselController,
                options: cs.CarouselOptions(
                  height: 600,
                  viewportFraction: 1,
                  initialPage: 0,
                  enableInfiniteScroll: true,
                  reverse: false,
                  autoPlay: false,
                  enlargeCenterPage: true,
                  scrollPhysics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  final PinTheme defaultPinTheme = PinTheme(
    width: Get.width * 0.1,
    height: Get.width * 0.1,
    margin: EdgeInsets.only(right: Get.width * 0.03),
    textStyle: const TextStyle(
      fontSize: 20,
      color: Color.fromRGBO(30, 60, 87, 1),
      fontWeight: FontWeight.w600,
    ),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(4),
    ),
  );
}

// class Authentication {
//   static Future<User?> signInWithGoogle({required BuildContext context}) async {
//     FirebaseAuth auth = FirebaseAuth.instance;
//     User? guser;
//     final usercontroller = Get.find<UserController>();

//     GoogleSignIn googleSignIn = GoogleSignIn()..signOut();
//     if (auth.currentUser != null) {
//       auth.signOut();
//       googleSignIn.signOut();
//     }

//     final GoogleSignInAccount? googleSignInAccount =
//         await googleSignIn.signIn();

//     if (googleSignInAccount != null) {
//       final GoogleSignInAuthentication googleSignInAuthentication =
//           await googleSignInAccount.authentication;

//       final AuthCredential credential = GoogleAuthProvider.credential(
//         accessToken: googleSignInAuthentication.accessToken,
//         idToken: googleSignInAuthentication.idToken,
//       );

//       if (kDebugMode) {
//         print("credential $credential");
//       }

//       try {
//         Get.to(() => const LoadingScreen(text: 'Signing in'));

//         final UserCredential userCredential =
//             await auth.signInWithCredential(credential);
//         if (kDebugMode) {
//           print("userCredential.user ${userCredential.user}");
//         }

//         guser = userCredential.user;
//         var token = await guser!.getIdToken();
//         log("token $token");

//         var params = {
//           'id_token': token,
//         };

//         usercontroller.googleSigninVerify(params);
//       } on FirebaseAuthException catch (e) {
//         if (e.code == 'account-exists-with-different-credential') {
//           // handle the error here
//         } else if (e.code == 'invalid-credential') {
//           // handle the error here
//         } else {}
//       } catch (e) {
//         // handle the error here
//       }
//     }

//     return guser;
//   }
// }
