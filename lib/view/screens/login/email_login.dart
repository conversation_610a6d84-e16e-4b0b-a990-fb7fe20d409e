// // ignore_for_file: no_leading_underscores_for_local_identifiers

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import '../../../controllers/user_controller.dart';
// import '../../widgets/keyboard_hider.dart';
// import '../../widgets/loading_screen.dart';
// import '../../../utils/constants.dart';
// import 'widgets/button.dart';
// import 'register_screen.dart';

// class EmailLogin extends StatelessWidget {
//   EmailLogin({super.key});
//   final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

//   @override
//   Widget build(BuildContext context) {
//     final UserController _usrcontroller = Get.find<UserController>();

//     final TextEditingController _emailController = TextEditingController();
//     final TextEditingController _pwdController = TextEditingController();
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(
//           'Log in',
//           style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
//         ),
//         foregroundColor: kblue,
//         backgroundColor: Get.theme.scaffoldBackgroundColor,
//       ),
//       body: KeyboardHider(
//         child: SingleChildScrollView(
//           child: Padding(
//             padding: const EdgeInsets.all(20),
//             child: Form(
//               key: _formKey,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const SizedBox(height: 20),
//                   InputTextField(
//                       hint: 'Enter Email',
//                       label: 'Email',
//                       txtcontroller: _emailController),
//                   const SizedBox(height: 10),
//                   Obx(() => InputTextField(
//                       hint: 'Enter Password',
//                       label: 'Password',
//                       obscure: _usrcontroller.showpassword.value ? true : false,
//                       txtcontroller: _pwdController)),

//                   //Forgot Password
//                   // TextButton(
//                   //     onPressed: () {
//                   //       Get.to(() => ForgotPasswordScreen());
//                   //     },
//                   //     child: const Text(
//                   //       'Forgot Password?',
//                   //       style: TextStyle(
//                   //           color: kblue, fontWeight: FontWeight.bold),
//                   //     )),
//                   const SizedBox(height: 10),

//                   SubmitButton(
//                       text: 'Continue',
//                       onpressed: () {
//                         if (_formKey.currentState!.validate()) {
//                           Get.to(() => const LoadingScreen(
//                                 text: 'Loggin in',
//                               ));
//                           _usrcontroller.fetchUserWithEmail({
//                             'email': _emailController.text,
//                             'password': _pwdController.text
//                           });
//                           // _usrcontroller.loading.value = false;
//                         }
//                       })
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
