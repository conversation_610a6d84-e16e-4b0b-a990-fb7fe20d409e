// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import 'widgets/button.dart';
// import '../../widgets/keyboard_hider.dart';
// import '../../../utils/constants.dart';
// import 'register_screen.dart';

// class ForgotPasswordScreen extends StatelessWidget {
//   ForgotPasswordScreen({super.key});
//   final TextEditingController _emailController = TextEditingController();
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(
//           'Forgot Password',
//           style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
//         ),
//         foregroundColor: kblue,
//         backgroundColor: Get.theme.scaffoldBackgroundColor,
//       ),
//       body: KeyboardHider(
//         child: SingleChildScrollView(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               const SizedBox(height: 20),
//               const Text(
//                 'Enter your email and we’ll send a link on your \nemail to reset your password.',
//                 style: TextStyle(
//                     color: Color(0xFF556F80),
//                     fontSize: 16,
//                     fontWeight: FontWeight.w400),
//               ),
//               const SizedBox(height: 20),
//               InputTextField(
//                   label: 'Email',
//                   hint: 'Enter email',
//                   txtcontroller: _emailController),
//               const SizedBox(height: 30),
//               SubmitButton(text: 'Send Link', onpressed: () {})
//             ],
//           ).paddingAll(20),
//         ),
//       ),
//     );
//   }
// }
