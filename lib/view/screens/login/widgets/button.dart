import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../../../utils/colors.dart';
import '../../../widgets/button_animation.dart';

class SubmitButton extends StatelessWidget {
  const SubmitButton({
    super.key,
    this.loading = false,
    this.side,
    required this.text,
    required this.onpressed,
    this.txtcolor = Colors.white,
    this.height = 56,
    this.bgcolor,
    this.boldness = FontWeight.w700,
    this.textsize = 16,
    this.padding,
  });
  final String text;
  final bool loading;
  final double height;
  final VoidCallback? onpressed;
  final Color? bgcolor;
  final Color txtcolor;
  final BoxBorder? side;
  final double textsize;
  final FontWeight boldness;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: onpressed == null ? 0.5 : 1,
      child: ButtonAnimation(
        onpress: loading ? null : onpressed,
        animationWidget: Container(
          decoration: BoxDecoration(
            border: side,
            borderRadius: BorderRadius.circular(2),
            color: onpressed == null || loading
                ? AppColors.kgrey
                : bgcolor ?? AppColors.ctaBackgroundColor,
          ),
          padding: padding ?? const EdgeInsets.all(8),
          height: height,
          child: loading
              ? Container(
                  height: 20,
                  width: 20,
                  alignment: Alignment.center,
                  child: LoadingIndicator(
                    colors: [
                      Theme.of(context).primaryColor,
                      Colors.black,
                    ],
                    indicatorType: Indicator.cubeTransition,
                  ),
                )
              : Center(
                  child: Text(
                    text,
                    style: TextStyle(
                      color: txtcolor,
                      fontSize: textsize,
                      fontWeight: boldness,
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}
