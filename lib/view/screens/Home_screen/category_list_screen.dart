import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../controllers/home_view_controller.dart';
import '../../../utils/constants.dart';
import '../../widgets/custom_shimmer.dart';
import 'widgets/parent_category_card.dart';

class CategoryListScreen extends StatelessWidget {
  const CategoryListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        foregroundColor: Colors.black,
        primary: true,
        scrolledUnderElevation: 0,
        title: Text(
          'Shop by category',
          style: GoogleFonts.dmSans(
            fontWeight: FontWeight.w700,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              backgroundColor: Colors.white,
              color: Theme.of(context).primaryColor,
              onRefresh: () async {
                await Get.find<HomeViewController>().getCategoriesList();
              },
              child: SingleChildScrollView(
                child: GetBuilder<HomeViewController>(
                  id: 'categories',
                  builder: (controller) {
                    final categoryList = controller.categoryList;
                    return Visibility(
                      visible: categoryList.isNotEmpty ||
                          controller.isCategoryLoading,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children: controller.isCategoryLoading
                              ? categoryList
                                  .map(
                                    (_) => CategoryCardShimmer(
                                      height: 138,
                                      width: (Get.width - 20 - 16 - 16) / 3,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  )
                                  .toList()
                              : categoryList
                                  .toList()
                                  .asMap()
                                  .entries
                                  .map((ele) {
                                  final categoryColor =
                                      categorycolorlist.elementAt(
                                          ele.key % categorycolorlist.length);
                                  return ParentCategoryCard(
                                    category: ele.value,
                                    cardHeight: 138,
                                    cardWidth: (Get.width - 20 - 16 - 16) / 3,
                                    backgroundColor: categoryColor.bg,
                                    textColor: categoryColor.txt,
                                  );
                                }).toList(),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
