import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../controllers/order_controller.dart';
import '../../../controllers/wishlist_controller.dart';
import '../../../utils/colors.dart';
import '../tracking_order/minimised_order_tracking_widget.dart';
import '../account/account_screen.dart';
import '../../../controllers/account_controller.dart';
import '../../widgets/widgets.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/home_view_controller.dart';
import '../../../controllers/product_view_controller.dart';
import '../../../utils/utils.dart';
import '../../../controllers/user_controller.dart';
import '../../../services/database_helper.dart';
import '../../../utils/constants.dart';
import 'category_list_screen.dart';
import 'widgets/banner_carousel.dart';
import 'widgets/parent_category_card.dart';
import 'widgets/custom_search_bar.dart';
import 'widgets/home_footer.dart';
import 'widgets/home_location_widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    DatabaseHelper.instance.getGroceries();
    Get.isRegistered<AccountController>()
        ? Get.find<AccountController>()
        : Get.put(AccountController());

    return HomeScreenWidget();
  }
}

class HomeScreenWidget extends StatefulWidget {
  // ignore: prefer_const_constructors_in_immutables
  HomeScreenWidget({super.key});

  @override
  State<HomeScreenWidget> createState() => _HomeScreenWidgetState();
}

class _HomeScreenWidgetState extends State<HomeScreenWidget> {
  final HomeViewController controller = Get.find<HomeViewController>();
  final ProductViewController productViewController =
      Get.find<ProductViewController>();
  final CartController cartController = Get.find<CartController>();
  final WishlistController wishlistcontroller = Get.find<WishlistController>();

  // final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.wait([controller.getBannerAds(), controller.getCategoriesList()]);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: KeyboardHider(
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [_buildHomeContent(context), _buildMinimisedWidgets()],
          ),
        ),
      ),
    );
  }

  Widget _buildHomeContent(BuildContext context) {
    return RefreshIndicator.adaptive(
      onRefresh: () {
        return Future.wait([
          wishlistcontroller.getWishlist(),
          controller.getHomePageViews(),
        ]);
      },
      backgroundColor: Colors.white,
      color: Theme.of(context).primaryColor,
      child: CustomScrollView(
        // primary: false,
        scrollBehavior: const CupertinoScrollBehavior(),

        slivers: [
          _buildAppBar(),
          const SliverToBoxAdapter(child: BannerCarousel()),

          // section title
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Shop by Category",
                    style: TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
                  ),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        Get.to(() => const CategoryListScreen());
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset("assets/svg/carbon_next-filled.svg"),
                          const Text("View all"),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          _buildCategoriesSection(),

          // other sections
          // ...List.generate(
          //   // TODO: get section wise products from server when available
          //   // controller.homepageDatalist.length,
          //   homepageDataMockList.length,
          //   (cindex) {
          //     return _buildMockSection(cindex);
          //   },
          // ),
          SliverToBoxAdapter(child: _buildBottomBanner()),
          const HomeFooter(),
          const SliverPadding(padding: EdgeInsets.symmetric(vertical: 100)),
        ],
      ),
    );
  }

  GetBuilder<HomeViewController> _buildBottomBanner() {
    return GetBuilder<HomeViewController>(
      id: 'banners',
      builder: (homeViewController) {
        final bannerItem = homeViewController.bannerItems.firstWhereOrNull(
          (element) =>
              element.linkPage == 'home' && element.position == 'bottom',
        );
        return Visibility(
          visible: bannerItem?.imageUrl != null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  "Featured brand",
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
                ),
              ),
              Container(
                margin: const EdgeInsets.all(16),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: CachedNetworkImage(
                    imageUrl: bannerItem?.imageUrl ?? '',
                    fit: BoxFit.fill,
                    errorWidget: (context, url, error) {
                      log(
                        "Bottom banner error: $error, url: ${bannerItem?.imageUrl}",
                      );
                      return Image.asset(
                        'assets/images/placeholder.webp',
                        fit: BoxFit.fill,
                        width: double.infinity,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // SliverToBoxAdapter _buildMockSection(int cindex) {
  //   return SliverToBoxAdapter(
  //     child: Padding(
  //       padding: const EdgeInsets.symmetric(vertical: 16),
  //       child: Container(
  //         decoration: BoxDecoration(
  //           color: homepageDataMockList[cindex].backgroundImageUrl == null
  //               ? kcyanFaded
  //               : null,
  //           image: homepageDataMockList[cindex].backgroundImageUrl == null
  //               ? null
  //               : DecorationImage(
  //                   image: CachedNetworkImageProvider(
  //                     homepageDataMockList[cindex].backgroundImageUrl ?? "",
  //                     headers: {
  //                       HttpHeaders.authorizationHeader:
  //                           'bearer ${Get.find<UserController>().userdata.value?.token}'
  //                     },
  //                     errorListener: (p0) => debugPrint(p0.toString()),
  //                   ),
  //                   fit: BoxFit.cover,
  //                 ),
  //         ),
  //         child: Column(
  //           children: [
  //             const SizedBox(height: 20),

  //             // category section title
  //             Padding(
  //               padding: const EdgeInsets.symmetric(
  //                 horizontal: 16,
  //               ),
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   Text(
  //                     /*"${controller.*/ "${homepageDataMockList[cindex].category.toString().capitalize}",
  //                     style: const TextStyle(
  //                       fontSize: 22,
  //                       fontWeight: FontWeight.w700,
  //                     ),
  //                   ),
  //                   Material(
  //                     color: Colors.transparent,
  //                     child: InkWell(
  //                       onTap: () {},
  //                       child: Column(
  //                         mainAxisSize: MainAxisSize.min,
  //                         children: [
  //                           SvgPicture.asset(
  //                             "assets/svg/carbon_next-filled.svg",
  //                           ),
  //                           const Text("View all"),
  //                         ],
  //                       ),
  //                     ),
  //                   )
  //                 ],
  //               ),
  //             ),

  //             const SizedBox(height: 20),
  //             SizedBox(
  //               height: 260,
  //               child:
  //                   // data from api
  //                   // controller.homepageDatalist.isNotEmpty

  //                   // mock data
  //                   homepageDataMockList.isNotEmpty
  //                       ? Column(
  //                           children: [
  //                             Flexible(
  //                               child: ListView.separated(
  //                                 shrinkWrap: true,
  //                                 scrollDirection: Axis.horizontal,
  //                                 // data from api
  //                                 // itemCount: controller
  //                                 //     .homepageDatalist[
  //                                 //         cindex]
  //                                 //     .products!
  //                                 //     .length,

  //                                 // mock data
  //                                 itemCount: homepageDataMockList[cindex]
  //                                     .products!
  //                                     .length,
  //                                 itemBuilder: (context, index) {
  //                                   // data from api
  //                                   // final model = controller
  //                                   //     .homepageDatalist[
  //                                   //         cindex]
  //                                   //     .products![index];

  //                                   // mock data
  //                                   // final model =
  //                                   //     homepageDataMockList[
  //                                   //                 cindex]
  //                                   //             .products![
  //                                   //         index];
  //                                   return const Center(
  //                                     child: Text('unavailable'),
  //                                   );
  //                                   // return CustomCardWidget(
  //                                   //   index: index,
  //                                   //   model: model,
  //                                   // );
  //                                 },
  //                                 separatorBuilder: (context, index) =>
  //                                     const SizedBox(
  //                                   width: 10,
  //                                 ),
  //                               ),
  //                             ),
  //                           ],
  //                         )
  //                       : ListView.separated(
  //                           scrollDirection: Axis.horizontal,
  //                           itemCount: 4,
  //                           itemBuilder: (context, index) {
  //                             return ProductCardShimmer(index: index);
  //                           },
  //                           separatorBuilder: (context, index) =>
  //                               const SizedBox(width: 8),
  //                         ),
  //             ),
  //             const SizedBox(height: 10),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  GetBuilder<HomeViewController> _buildCategoriesSection() {
    return GetBuilder<HomeViewController>(
      id: 'categories',
      builder: (_) {
        final categoryList = controller.categoryList.take(6);
        return SliverToBoxAdapter(
          child: Visibility(
            visible: categoryList.isNotEmpty || controller.isCategoryLoading,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Wrap(
                spacing: 10,
                runSpacing: 10,
                children:
                    controller.isCategoryLoading
                        ? categoryList
                            .map(
                              (_) => CategoryCardShimmer(
                                height: 138,
                                width: (Get.width - 20 - 16 - 16) / 3,
                                borderRadius: BorderRadius.circular(6),
                              ),
                            )
                            .toList()
                        : categoryList.toList().asMap().entries.map((ele) {
                          final categoryColor = categorycolorlist.elementAt(
                            ele.key % categorycolorlist.length,
                          );
                          return ParentCategoryCard(
                            category: ele.value,
                            cardHeight: 138,
                            cardWidth: (Get.width - 20 - 16 - 16) / 3,
                            backgroundColor: categoryColor.bg,
                            textColor: categoryColor.txt,
                          );
                        }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  GetBuilder<HomeViewController> _buildAppBar() {
    return GetBuilder<HomeViewController>(
      id: "scroll",
      builder: (_) {
        return SliverAppBar(
          pinned: true,
          // stretch: true,
          snap: false,
          floating: true,
          automaticallyImplyLeading: false,
          elevation: 0,
          actions: [
            InkWell(
              onTap: () {
                Get.to(() => AccountScreen());
              },
              child: SvgPicture.asset(
                "assets/svg/profile-icon-1.svg",
                colorFilter: ColorFilter.mode(
                  AppColors.homeAppBarIconColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
            SizedBox(width: Utils.getScreenWidthByPercentage(1)),
            const SizedBox(width: 16),
          ],
          backgroundColor: AppColors.kHomeAppBarBG,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          title: const HomeLocationWidget(),
          bottom: const PreferredSize(
            preferredSize: Size.fromHeight(70),
            child: CustomSearchBar(),
          ),
        );
      },
    );
  }

  Column _buildMinimisedWidgets() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GetBuilder<OrderController>(
          builder: (orderController) {
            return Visibility(
              visible: orderController.currentOrders.isNotEmpty,
              child: const MinimisedOrderTrackingWidget(),
            );
          },
        ),
        const MinimisedCartItemStatusWidget(),
      ],
    );
  }
}

Widget getSvgIcon(String icon, [String? semanticsLabel = "Acme Logo"]) =>
    SvgPicture.asset(icon, semanticsLabel: semanticsLabel);


// List<HomePageCategoryData> homepageDataMockList = [
  // HomePageCategoryData(
  //   category: "Daily deals",
  //   categoryId: '1',
  //   products: [
  //     Product(
  //       category: "Daily deals",
  //       categoryId: '1',
  //       mrp: 15,
  //       price: 10,
  //       costPrice: 12,
  //       product: "Apple keeps a doctor away",
  //       productId: '1',
  //       // variantId: '1',
  //       subCategoryId: 1,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //     Product(
  //       category: "Daily deals",
  //       categoryId: '1',
  //       mrp: 12,
  //       price: 11,
  //       costPrice: 10,
  //       product: "product 2",
  //       productId: '2',
  //       // variantId: '2',
  //       subCategoryId: 1,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 0,
  //     ),
  //     Product(
  //       category: "Daily deals",
  //       categoryId: '1',
  //       mrp: 12,
  //       price: 12,
  //       costPrice: 10,
  //       product: "product 3",
  //       productId: '3',
  //       // variantId: '3',
  //       subCategoryId: 1,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //   ],
  // ),
  // HomePageCategoryData(
  //   category: "Festive deals",
  //   categoryId: '2',
  //   backgroundImageUrl: "https://picsum.photos/200",
  //   products: [
  //     Product(
  //       category: "Festive deals",
  //       categoryId: '2',
  //       mrp: 15,
  //       price: 10,
  //       costPrice: 12,
  //       product: "Apple keeps a doctor away",
  //       productId: '4',
  //       // variantId: '4',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //     Product(
  //       category: "Festive deals",
  //       categoryId: '2',
  //       mrp: 12,
  //       price: 12,
  //       costPrice: 10,
  //       product: "product 2",
  //       productId: '5',
  //       // variantId: '5',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //     Product(
  //       category: "Festive deals",
  //       categoryId: '2',
  //       mrp: 12,
  //       price: 12,
  //       costPrice: 10,
  //       product: "product 3",
  //       productId: '6',
  //       // variantId: '6',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //   ],
  // ),
  // HomePageCategoryData(
  //   category: "Healthy vegies",
  //   categoryId: '3',
  //   products: [
  //     Product(
  //       category: "Healthy vegies",
  //       categoryId: '3',
  //       mrp: 150,
  //       price: 110,
  //       costPrice: 12,
  //       product: "Apple keeps a doctor away",
  //       productId: '7',
  //       // variantId: '7',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //     Product(
  //       category: "Healthy vegies",
  //       categoryId: '3',
  //       mrp: 150,
  //       price: 110,
  //       costPrice: 12,
  //       product: "Apple keeps a doctor away",
  //       productId: '8',
  //       // variantId: '8',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //     Product(
  //       category: "Healthy vegies",
  //       categoryId: '3',
  //       mrp: 150,
  //       price: 110,
  //       costPrice: 12,
  //       product: "Apple keeps a doctor away",
  //       productId: '9',
  //       // variantId: '9',
  //       subCategoryId: 111,
  //       subCategoryName: "subcategoryname",
  //       images:
  //           "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTExtoLVhMIfPRj_8d5RQKF2qjwUbuYL2tZTg&usqp=CAU",
  //       // weight: 1,
  //       stock: 5,
  //     ),
  //   ],
  // ),
// ];
