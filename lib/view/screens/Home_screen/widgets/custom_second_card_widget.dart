// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:google_fonts/google_fonts.dart';

// class CustomSecondCardWidget extends StatelessWidget {
//   const CustomSecondCardWidget({super.key, required this.index});
//   final int index;
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         index == 0
//             ? const SizedBox(
//                 width: 24,
//               )
//             : const SizedBox(),
//         Material(
//           elevation: 3,
//           shadowColor: Colors.black,
//           child: SizedBox(
//             width: 285,
//             height: 260,
//             child: Container(
//               color: Colors.white,
//               child: Padding(
//                 padding: const EdgeInsets.only(bottom: 10, right: 10),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Image.asset("assets/images/combo-badge.png"),
//                         SvgPicture.asset("assets/svg/heart-icon.svg")
//                       ],
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Image.asset("assets/images/apple.png"),
//                         const Icon(
//                           Icons.add,
//                           size: 40,
//                           color: Colors.black,
//                         ),
//                         Image.asset("assets/images/apple.png"),
//                       ],
//                     ),
//                     const SizedBox(height: 2),
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 15),
//                       child: Align(
//                         alignment: Alignment.topLeft,
//                         child: Row(
//                           children: [
//                             Text(
//                               "Apple",
//                               style: GoogleFonts.dmSans(
//                                 fontSize: 16,
//                                 color: Colors.black,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                             const Icon(
//                               Icons.add,
//                               color: Colors.black,
//                             ),
//                             Text(
//                               "Apple",
//                               style: GoogleFonts.dmSans(
//                                 fontSize: 16,
//                                 color: Colors.black,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     const SizedBox(height: 3),
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 15),
//                       child: Align(
//                         alignment: Alignment.topLeft,
//                         child: Text(
//                           "1kg + 1kg",
//                           style: GoogleFonts.dmSans(
//                             fontSize: 13,
//                             color: Colors.grey,
//                             fontWeight: FontWeight.w400,
//                           ),
//                         ),
//                       ),
//                     ),
//                     const SizedBox(height: 3),
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 15),
//                       child: Align(
//                         alignment: Alignment.topLeft,
//                         child: Row(
//                           children: [
//                             Text(
//                               "₹364",
//                               style: GoogleFonts.dmSans(
//                                 fontSize: 16,
//                                 color: Colors.black,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                             const SizedBox(width: 5),
//                             Text(
//                               "₹520",
//                               style: GoogleFonts.dmSans(
//                                 fontSize: 14,
//                                 color: Colors.grey,
//                                 fontWeight: FontWeight.w400,
//                                 decoration: TextDecoration.lineThrough,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     const SizedBox(height: 4),
//                     Align(
//                       alignment: Alignment.bottomCenter,
//                       child: Container(
//                         width: 120,
//                         height: 35,
//                         decoration: BoxDecoration(
//                           border: Border.all(color: Colors.black, width: 1.2),
//                         ),
//                         child: Center(
//                             child: Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             SvgPicture.asset("assets/svg/add-to-cart-icon.svg"),
//                             const SizedBox(width: 10),
//                             Text(
//                               "Add",
//                               style: GoogleFonts.dmSans(
//                                 fontSize: 14,
//                                 color: Colors.black,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ],
//                         )),
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
