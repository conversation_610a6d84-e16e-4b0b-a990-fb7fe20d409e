import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../controllers/account_controller.dart';
import '../../../../controllers/location_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/utils.dart';
import '../../map_screen/allow_location_screen.dart';

class HomeLocationWidget extends StatelessWidget {
  const HomeLocationWidget({super.key});

  TextStyle get locationTitleStyle => GoogleFonts.dmSans(
        fontWeight: FontWeight.w700,
        fontSize: 14,
        color: Colors.black,
      );
  TextStyle get locationSubTitleStyle => GoogleFonts.dmSans(
        fontWeight: FontWeight.w400,
        fontSize: 12,
        color: Colors.black,
      );

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.to(
          () => const AllowLocationScreen(),
          binding: LocationBinding(),
        );
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          SvgPicture.asset(
            "assets/svg/location.svg",
            colorFilter: ColorFilter.mode(
              AppColors.homeAppBarIconColor,
              BlendMode.srcIn,
            ),
          ),
          SizedBox(
            width: Utils.getScreenWidthByPercentage(1),
          ),
          Flexible(
            flex: 2,
            child: GetBuilder<UserController>(
              builder: (usercontroller) {
                final AccountController accountController = Get.find();

                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: accountController.selectedAddress.value != null,
                      replacement: Text(
                        usercontroller.nearbyStore?.name ?? '',
                        overflow: TextOverflow.fade,
                        style: locationTitleStyle,
                      ),
                      child: Text(
                        // accountController.selectedAddress.value?.type ?? '',
                        'Deliver to',
                        overflow: TextOverflow.fade,
                        style: locationTitleStyle,
                      ),
                    ),
                    Visibility(
                      visible: accountController.selectedAddress.value != null,
                      replacement: Text(
                        usercontroller.nearbyStore?.location?.address
                                ?.toJson()
                                .values
                                .join(', ') ??
                            '',
                        overflow: TextOverflow.ellipsis,
                        style: locationSubTitleStyle,
                      ),
                      child: Text(
                        accountController.selectedAddress.value?.toString() ??
                            '',
                        overflow: TextOverflow.ellipsis,
                        style: locationSubTitleStyle,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
