import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/home_view_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../utils/colors.dart';
import '../../map_screen/select_location_screen.dart';

class BannerCarousel extends StatelessWidget {
  const BannerCarousel({super.key});

  @override
  Widget build(BuildContext context) {
    final carouselController = CarouselSliderControllerImpl();
    return Container(
      color: AppColors.kHomeAppBarBG,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      margin: const EdgeInsets.only(bottom: 20),
      child: <PERSON>ack(
        children: [
          GetBuilder<HomeViewController>(
            id: 'banners',
            builder: (controller) {
              final bannerList = controller.bannerItems.where(
                (element) =>
                    element.linkPage == 'home' && element.position == 'top',
              );
              return CarouselSlider.builder(
                carouselController: carouselController,
                itemCount: bannerList.length,
                itemBuilder: (
                  BuildContext context,
                  int itemIndex,
                  int pageViewIndex,
                ) {
                  if (bannerList.isEmpty) {
                    return const SizedBox();
                  }
                  double scale = 1;
                  return StatefulBuilder(
                    builder: (context, setState) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Transform.scale(
                          scale: scale,
                          child:
                              bannerList.elementAtOrNull(itemIndex)?.imageUrl ==
                                      null
                                  ? Container(
                                    height: 207,
                                    width: double.infinity,
                                    color: AppColors.kgrey,
                                  )
                                  : CachedNetworkImage(
                                    height: 207,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                    imageUrl:
                                        bannerList
                                            .elementAtOrNull(itemIndex)
                                            ?.imageUrl ??
                                        '',
                                    placeholder:
                                        (context, url) => Center(
                                          child: CustomAppShimmer(
                                            child: Container(
                                              color: Colors.grey,
                                              height: 180,
                                            ),
                                          ),
                                        ),
                                    errorWidget:
                                        (context, url, error) => Container(
                                          height: 207,
                                          width: double.infinity,
                                          color: AppColors.kgrey,
                                        ),
                                  ),
                        ),
                      );
                    },
                  );
                },
                options: CarouselOptions(
                  scrollPhysics: const BouncingScrollPhysics(),
                  enlargeCenterPage: true,
                  onPageChanged: (index, reason) {
                    controller
                      ..selectedpageitem = index
                      ..update(['banners']);
                  },
                  initialPage: 0,
                  height: 180,
                  viewportFraction: 1,
                  padEnds: false,
                  clipBehavior: Clip.antiAlias,
                  autoPlay: bannerList.length > 1,
                ),
              );
            },
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 20,
            child: GetBuilder<HomeViewController>(
              id: 'banners',
              builder: (controller) {
                final bannerList = controller.bannerItems.where(
                  (element) =>
                      element.linkPage == 'home' && element.position == 'top',
                );
                return Visibility(
                  visible: bannerList.length > 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      bannerList.length,
                      (index) => Container(
                        height: 8,
                        width: 8,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        decoration: ShapeDecoration(
                          shape: const CircleBorder(),
                          color:
                              index == controller.selectedpageitem
                                  ? Colors.white
                                  : const Color(0xff556f80),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
