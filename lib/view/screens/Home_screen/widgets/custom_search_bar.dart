import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../controllers/home_view_controller.dart';
import '../../../../utils/colors.dart';
import '../../search_screen/search_screen.dart';

class CustomSearchBar extends StatelessWidget {
  const CustomSearchBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: AppBar(
        toolbarHeight: 70,
        automaticallyImplyLeading: false,
        backgroundColor: Get.find<HomeViewController>().pinned
            ? Colors.transparent
            : Colors.white,
        elevation: 0,
        titleSpacing: 0,
        // scrolledUnderElevation: 2,
        flexibleSpace: Container(
          color: AppColors.kHomeAppBarBG,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          width: double.infinity,
          child: Container(
            color: Colors.transparent,
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: SizedBox(
                    height: 45,
                    child: TextField(
                      readOnly: true,
                      onTap: () {
                        Get.to(
                          () => const SearchScreen(),
                          transition: Transition.downToUp,
                        );
                      },
                      decoration: InputDecoration(
                        isDense: true,
                        filled: true,
                        fillColor: Colors.white, //Colors.transparent,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.all(16),
                          child: SvgPicture.asset("assets/svg/search-icon.svg"),
                        ),
                        // suffixIcon: Padding(
                        //   padding: const EdgeInsets.all(15),
                        //   child: getSvgIcon("assets/svg/voice.svg"),

                        hintText: "Search for products...",
                        hintStyle: const TextStyle(fontSize: 15),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: const BorderSide(
                            color: Colors.transparent, // Color(0xffC5C8CD),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: const BorderSide(
                            width: 1,
                            color: Colors.transparent, //Color(0xffC5C8CD),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
