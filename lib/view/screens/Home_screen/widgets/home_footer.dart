import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../utils/colors.dart';

class HomeFooter extends StatelessWidget {
  const HomeFooter({
    super.key,
  });

  TextStyle get style => GoogleFonts.dmSans(
        fontWeight: FontWeight.w500,
        fontSize: 12,
        color: AppColors.kgreyDark,
      );

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(
              vertical: 16,
            ),
            padding: const EdgeInsets.symmetric(
              vertical: 30,
              horizontal: 23,
            ),
            color: AppColors.kgrey.withOpacity(0.4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset("assets/svg/farmers_produce.svg"),
                    const SizedBox(width: 23),
                    Flexible(
                      child: Text(
                        "High quality produce supplied direct from the farmers to your home / shop / restaurant.",
                        style: style,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    SvgPicture.asset("assets/svg/packed_box.svg"),
                    const SizedBox(width: 23),
                    Flexible(
                      child: Text(
                        "Produce is neatly sorted and packed at the warehouse.",
                        style: style,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    SvgPicture.asset("assets/svg/best_price.svg"),
                    const SizedBox(width: 23),
                    Flexible(
                      child: Text(
                        "Best price promised, competitive to the market prices.",
                        style: style,
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            "* covid safety protocol followed at every step",
            style: style.copyWith(color: Colors.black),
          ),
        ],
      ),
    );
  }
}
