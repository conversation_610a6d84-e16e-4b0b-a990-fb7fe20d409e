import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../controllers/user_controller.dart';
import '../../../../model/database_models/database_models.dart';
import '../../../../controllers/cart_controlller.dart';
import '../../../../model/item_list_model/item_list_model.dart';
import '../../../../services/database_helper.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/enumerations.dart';
import '../../../../utils/utils.dart';
import '../../product_screen/product_screen.dart';

class CustomCardWidget extends StatelessWidget {
  const CustomCardWidget({super.key, required this.model, required this.index});
  final Item model;
  final int index;

  double get discountVal =>
      ((100 * (model.mrp.toDouble() - model.price.toDouble())) /
              model.mrp.toDouble())
          .floorToDouble();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Padding(
          padding:
              index == 0 ? const EdgeInsets.only(left: 16) : EdgeInsets.zero,
          child: Column(
            children: [
              SizedBox(
                width: 140,
                height: 250,
                child: Stack(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.to(() => ProductScreen(item: model));
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(2),
                        child: Container(
                          height: 232,
                          decoration: const BoxDecoration(color: Colors.white),
                          child: Stack(
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(10),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const SizedBox(height: 2),
                                    SizedBox(
                                      height: 110,
                                      child:
                                          model.imageUrls.isEmpty
                                              ? Image.asset(
                                                "assets/images/error-image.webp",
                                              )
                                              : CachedNetworkImage(
                                                imageUrl:
                                                    model
                                                        .imageUrls
                                                        .firstOrNull ??
                                                    '',
                                                fit: BoxFit.cover,
                                                errorWidget:
                                                    (
                                                      context,
                                                      error,
                                                      stackTrace,
                                                    ) => Image.asset(
                                                      "assets/images/error-image.webp",
                                                    ),
                                              ),
                                    ),
                                    const SizedBox(height: 5),
                                    SizedBox(
                                      height: 36,
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          "${model.name.toString().capitalizeFirst}",
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                          textAlign: TextAlign.start,
                                          style: const TextStyle(
                                            height: 1,
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                    // Align(
                                    //   alignment: Alignment.topLeft,
                                    //   child: Text(
                                    //     model.weight != null
                                    //         ? getweight(
                                    //             model.weight?.toString() ?? "",
                                    //           )
                                    //         : "",
                                    //     style: const TextStyle(
                                    //         fontSize: 12,
                                    //         color: Colors.grey,
                                    //         fontWeight: FontWeight.w400,
                                    //         letterSpacing: -0.1),
                                    //   ),
                                    // ),
                                    const SizedBox(height: 7),
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Row(
                                        children: [
                                          FittedBox(
                                            child: Text(
                                              "\u20b9${model.price.round()}",
                                              style: GoogleFonts.inter(
                                                fontSize: 16,
                                                color: Colors.black,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 5),
                                          Text(
                                            model.price == model.mrp ||
                                                    model.mrp == 0
                                                ? "---"
                                                : "\u20b9${model.mrp.round()}",
                                            style: GoogleFonts.inter(
                                              fontSize: 12,
                                              color: Colors.black.withOpacity(
                                                0.5,
                                              ),
                                              fontWeight: FontWeight.w400,
                                              decoration:
                                                  TextDecoration.lineThrough,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                  ],
                                ),
                              ),
                              discountVal == 0
                                  ? const SizedBox(width: 32, height: 44)
                                  : Align(
                                    alignment: Alignment.topRight,
                                    child: SizedBox(
                                      width: 32,
                                      height: 44,
                                      child: Stack(
                                        alignment: Alignment.topRight,
                                        children: [
                                          SvgPicture.asset(
                                            'assets/svg/offertag.svg',
                                            colorFilter: const ColorFilter.mode(
                                              Color(0xffFF5454),
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                          Center(
                                            child: Text(
                                              "${discountVal.round()}% \n OFF",
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 12,
                                                fontWeight: FontWeight.w700,
                                                letterSpacing: -0.5,
                                              ),
                                              textAlign: TextAlign.center,
                                            ).paddingOnly(bottom: 10),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: StatefulBuilder(
                        builder: (context, setState) {
                          // DatabaseHelper.instance.getGroceries();

                          return GetBuilder<CartController>(
                            builder: (cartController) {
                              return FutureBuilder<int>(
                                future: DatabaseHelper.instance.getQty(
                                  LocalShoppingCart(
                                    productID: model.id!,
                                    purchaseOrderID: model.purchaseOrderId!,
                                  ),
                                ),
                                builder: (context, snapshot) {
                                  if (snapshot.data == 0) {
                                    return Center(
                                      child: InkWell(
                                        onTap: () async {
                                          if (model.availableStock != 0) {
                                            final discount =
                                                model.discount?.type ==
                                                        DiscountType.currency
                                                    ? model.discount?.value
                                                    : model.price *
                                                        (model
                                                                .discount
                                                                ?.value ??
                                                            0.0) /
                                                        100;
                                            final taxInRupees = model.tax
                                                .fold<double>(
                                                  0.0,
                                                  (prev, e) =>
                                                      prev +
                                                      (((e.value ?? 0)
                                                                  .toDouble() /
                                                              100.0) *
                                                          model.price),
                                                );
                                            await DatabaseHelper.instance
                                                .addUpdate(
                                                  LocalShoppingCart(
                                                    mrp: model.mrp,
                                                    name: model.name,
                                                    qty: 1,
                                                    productID: model.id!,
                                                    purchaseOrderID:
                                                        model.purchaseOrderId!,
                                                    price: model.price,
                                                    imageURL:
                                                        model
                                                            .imageUrls
                                                            .firstOrNull,
                                                    availableStock:
                                                        model.availableStock,
                                                    discount: discount,
                                                    tax: taxInRupees,
                                                  ),
                                                );
                                            setState(() {});
                                          } else {
                                            Utils.customToast(
                                              message:
                                                  "Running low on stock.\nPlease add it to Wishlist to get a notification once the item is restocked",
                                              backgroundColor:
                                                  Theme.of(
                                                    context,
                                                  ).primaryColor,
                                              timeInSecForIosWeb: 5,
                                            );
                                          }
                                        },
                                        child: Container(
                                          height: 36,
                                          width: 84,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            border: Border.all(
                                              color:
                                                  model.availableStock == 0
                                                      ? AppColors.kgrey
                                                      : Theme.of(
                                                        context,
                                                      ).primaryColor,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              2,
                                            ),
                                          ),
                                          child: Text(
                                            "Add",
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                              color:
                                                  model.availableStock == 0
                                                      ? AppColors.kgrey
                                                      : Theme.of(
                                                        context,
                                                      ).primaryColor,
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                  return Center(
                                    child: AnimatedContainer(
                                      duration: const Duration(
                                        milliseconds: 100,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        border: Border.all(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                      height: 36,
                                      width: 84,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                await DatabaseHelper.instance
                                                    .removeUpdate(
                                                      LocalShoppingCart(
                                                        productID: model.id!,
                                                        purchaseOrderID:
                                                            model
                                                                .purchaseOrderId!,
                                                      ),
                                                    );
                                                setState(() {});
                                              },
                                              child: Icon(
                                                Icons.remove_outlined,
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).primaryColor,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: Text(
                                              "${snapshot.data ?? ""}",
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).primaryColor,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                if (model.availableStock >
                                                    (snapshot.data ?? 0)) {
                                                  await DatabaseHelper.instance
                                                      .addUpdate(
                                                        LocalShoppingCart(
                                                          productID: model.id!,
                                                          purchaseOrderID:
                                                              model
                                                                  .purchaseOrderId!,
                                                        ),
                                                      );
                                                  setState(() {});
                                                } else {
                                                  Utils.customToast(
                                                    message:
                                                        "Running low on stock.\nPlease add it to Wishlist to get a notification once the item is restocked",
                                                    backgroundColor:
                                                        Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                    timeInSecForIosWeb: 5,
                                                  );
                                                }
                                              },
                                              child: Icon(
                                                Icons.add,
                                                color:
                                                    model.availableStock >
                                                            (snapshot.data ?? 0)
                                                        ? Theme.of(
                                                          context,
                                                        ).primaryColor
                                                        : AppColors.kgrey,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
