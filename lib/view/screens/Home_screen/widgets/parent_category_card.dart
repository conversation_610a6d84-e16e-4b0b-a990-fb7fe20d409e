import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/category_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../model/category_model/data.dart';
import '../../../../utils/colors.dart';
import '../../../widgets/button_animation.dart';
import '../../category_pages/category_screen.dart';

class ParentCategoryCard extends StatelessWidget {
  const ParentCategoryCard({
    super.key,
    required this.category,
    this.cardWidth,
    this.cardHeight,
    this.textColor,
    this.backgroundColor,
  });
  final CategoryItemModel category;
  final double? cardWidth;
  final double? cardHeight;
  final Color? textColor;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return ButtonAnimation(
      onpress: () {
        final CategoryController categoryController = Get.find();
        categoryController.categoryproductmodel.clear();

        Get.to(() => CategoryScreen(initialCategory: category));
      },
      animationWidget: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Container(
          height: cardHeight,
          width: cardWidth,
          color: backgroundColor ?? AppColors.kParentCategoryCardBG,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(4),
                child: Text(
                  "${category.name?.toString().capitalize}".capitalizeFirst!,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 15,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    color: textColor ?? const Color(0xff7A5649),
                  ),
                ),
              ),
              Expanded(
                child: CachedNetworkImage(
                  imageUrl: category.imageUrl ?? '',
                  errorListener:
                      (error) => log(
                        "$error:\nid: ${category.id}\nname: ${category.name}\n${category.imageUrl ?? ''}",
                        name: 'parent-category',
                      ),
                  errorWidget:
                      (context, url, error) =>
                          Image.asset('assets/images/error-image.webp'),
                  fit: BoxFit.fitWidth,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
