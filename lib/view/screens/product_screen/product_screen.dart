// ignore_for_file: deprecated_member_use

import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/category_controller.dart';
import '../../../controllers/wishlist_controller.dart';
import '../../../model/model.dart';
import '../../../services/database_helper.dart';
import '../../../utils/colors.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/product_view_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../utils/enumerations.dart';
import '../../widgets/minimised_cart_item_status_widget.dart';
import '../../widgets/decorated_card.dart';

class ProductScreen extends StatefulWidget {
  const ProductScreen({super.key, required this.item});

  final Item item;

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  final controller = Get.find<ProductViewController>();
  final categorycontroller = Get.find<CategoryController>();
  final cartController = Get.find<CartController>();
  final UserController userController = Get.find<UserController>();
  final WishlistController wishlistcontroller = Get.find();

  double? get discount =>
      widget.item.discount?.type == DiscountType.currency
          ? widget.item.discount?.value
          : widget.item.price.toDouble() * (widget.item.discount?.value ?? 0.0);
  double get taxInRupees => widget.item.tax.fold<double>(
    0.0,
    (prev, e) =>
        prev + (((e.value ?? 0).toDouble() / 100.0) * widget.item.price),
  );

  @override
  void initState() {
    super.initState();
    controller.favourite =
        wishlistcontroller.wislistItems.isEmpty
            ? false
            : wishlistcontroller.wislistItems.any(
              (element) => element.id == widget.item.id,
            );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        scrolledUnderElevation: 0,
      ),
      extendBodyBehindAppBar: false,
      body: Stack(
        children: [
          ListView(
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _productImageCard(widget.item.imageUrls),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      '${widget.item.name.toString().capitalize}',
                      style: const TextStyle(
                        fontSize: 24,
                        height: 34 / 24,
                        letterSpacing: -1,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),

                  // heart button
                  GetBuilder<ProductViewController>(
                    builder: (controller) {
                      return InkWell(
                        onTap: () async {
                          controller
                            ..favourite = !controller.favourite
                            ..update();

                          final findindex = wishlistcontroller.wislistItems
                              .indexWhere(
                                (element) => element.id == widget.item.id,
                              );
                          if (findindex >= 0) {
                            wishlistcontroller
                              ..wislistItems.removeAt(findindex)
                              ..removeFromwishlist(widget.item.id ?? '')
                              ..update();
                          } else {
                            if (controller.favourite) {
                              wishlistcontroller
                                ..wislistItems.add(widget.item)
                                ..addToWishlist(widget.item.id ?? '')
                                ..update();
                            }
                          }

                          // wishlistcontroller.getwishlistitems();
                        },
                        child: Visibility(
                          visible: controller.favourite,
                          replacement: const Icon(Icons.favorite_outline_sharp),
                          child: const Icon(Icons.favorite, color: Colors.red),
                        ),
                      );
                    },
                  ),
                ],
              ).paddingOnly(left: 16, top: 20, right: 16),
              const SizedBox(height: 11),

              Builder(
                builder: (context) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  "${widget.item.weight ?? ''} ${widget.item.unit ?? ''}",
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16,
                                    height: 20.83 / 16,
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      "\u20b9${widget.item.price.toDouble().round()}",
                                      style: GoogleFonts.inter(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 16,
                                        height: 20.83 / 16,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    const SizedBox(width: 11),
                                    Text(
                                      widget.item.mrp == widget.item.price
                                          ? ""
                                          : "\u20b9${widget.item.mrp.toDouble().round()}",
                                      style: GoogleFonts.inter(
                                        fontSize: 12,
                                        height: 15.62 / 12,
                                        decoration: TextDecoration.lineThrough,
                                        fontStyle: FontStyle.italic,
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.kgrey,
                                      ),
                                    ),
                                    const SizedBox(width: 11),
                                    Visibility(
                                      visible: controller.currentdiscount > 0,
                                      child: Container(
                                        height: 20,
                                        width: 55,
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius: BorderRadius.circular(
                                            2,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            "${controller.currentdiscount.round()}% OFF",
                                            style: const TextStyle(
                                              fontSize: 11,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              StatefulBuilder(
                                builder: (context, setState) {
                                  return FutureBuilder<int>(
                                    future: DatabaseHelper.instance.getQty(
                                      LocalShoppingCart(
                                        productID: widget.item.id!,
                                        purchaseOrderID:
                                            widget.item.purchaseOrderId!,
                                      ),
                                    ),
                                    builder: (context, snapshot) {
                                      bool canBeAdded =
                                          widget.item.availableStock > 0;
                                      bool canBeIncremented =
                                          (snapshot.data ?? 0) <
                                          widget.item.availableStock;
                                      return snapshot.data == 0
                                          ? InkWell(
                                            onTap:
                                                !canBeAdded
                                                    ? null
                                                    : () async {
                                                      await DatabaseHelper
                                                          .instance
                                                          .addUpdate(
                                                            LocalShoppingCart(
                                                              mrp:
                                                                  widget
                                                                      .item
                                                                      .mrp,
                                                              name:
                                                                  widget
                                                                      .item
                                                                      .name,
                                                              qty: 1,
                                                              productID:
                                                                  widget
                                                                      .item
                                                                      .id!,
                                                              purchaseOrderID:
                                                                  widget
                                                                      .item
                                                                      .purchaseOrderId!,
                                                              price:
                                                                  widget
                                                                      .item
                                                                      .price,
                                                              imageURL:
                                                                  widget
                                                                      .item
                                                                      .imageUrls
                                                                      .firstOrNull,
                                                              availableStock:
                                                                  widget
                                                                      .item
                                                                      .availableStock,
                                                              discount:
                                                                  discount,
                                                              tax: taxInRupees,
                                                            ),
                                                          );
                                                      setState(() {});
                                                    },
                                            child: Container(
                                              height: 36,
                                              width: 84,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                border: Border.all(
                                                  color:
                                                      Theme.of(
                                                        context,
                                                      ).primaryColor,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                              child: Text(
                                                'Add',
                                                style: TextStyle(
                                                  color:
                                                      !canBeAdded
                                                          ? AppColors.kgrey
                                                          : Theme.of(
                                                            context,
                                                          ).primaryColor,
                                                  fontWeight: FontWeight.w700,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ),
                                          )
                                          : AnimatedContainer(
                                            duration: const Duration(
                                              milliseconds: 100,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).primaryColor,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                            ),
                                            height: 36,
                                            width: 84,
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceAround,
                                              children: [
                                                Flexible(
                                                  child: InkWell(
                                                    onTap: () async {
                                                      await DatabaseHelper
                                                          .instance
                                                          .removeUpdate(
                                                            LocalShoppingCart(
                                                              productID:
                                                                  widget
                                                                      .item
                                                                      .id!,
                                                              purchaseOrderID:
                                                                  widget
                                                                      .item
                                                                      .purchaseOrderId!,
                                                            ),
                                                          );
                                                      setState(() {});
                                                    },
                                                    child: Icon(
                                                      Icons.remove_outlined,
                                                      color:
                                                          Theme.of(
                                                            context,
                                                          ).primaryColor,
                                                    ),
                                                  ),
                                                ),
                                                Text(
                                                  "${snapshot.data ?? ""}",
                                                  maxLines: 1,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w700,
                                                    color:
                                                        Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                  ),
                                                ),
                                                Flexible(
                                                  child: InkWell(
                                                    onTap:
                                                        !canBeIncremented
                                                            ? null
                                                            : () async {
                                                              await DatabaseHelper.instance.addUpdate(
                                                                LocalShoppingCart(
                                                                  productID:
                                                                      widget
                                                                          .item
                                                                          .id!,
                                                                  purchaseOrderID:
                                                                      widget
                                                                          .item
                                                                          .purchaseOrderId!,
                                                                ),
                                                              );
                                                              setState(() {});
                                                            },
                                                    child: Icon(
                                                      Icons.add,
                                                      color:
                                                          !canBeIncremented
                                                              ? AppColors.kgrey
                                                              : Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ).paddingSymmetric(horizontal: 16, vertical: 15),
                    ],
                  );
                },
              ),

              Visibility(
                visible: widget.item.description != null,
                child: DecoratedCard(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Product Detail",
                        style: TextStyle(
                          fontSize: 14,
                          height: 18 / 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 16 / 2),
                      Text(
                        widget.item.description ?? "",
                        style: TextStyle(
                          fontSize: 14,
                          height: 21 / 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kgreyDark,
                        ),
                      ),
                      const SizedBox(height: 16 / 2),
                      Text(
                        "Brand: ${widget.item.brand?.name}",
                        style: TextStyle(
                          fontSize: 14,
                          height: 21 / 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kgreyDark,
                        ),
                      ),
                      Text(
                        "Manufacturer: ${widget.item.manufacturer?.name}",
                        style: TextStyle(
                          fontSize: 14,
                          height: 21 / 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kgreyDark,
                        ),
                      ),
                      Text(
                        "Returnable: ${widget.item.returnable ?? false ? "Yes" : "No"}",
                        style: TextStyle(
                          fontSize: 14,
                          height: 21 / 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kgreyDark,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // const SizedBox(height: 38),
              DecoratedCard(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Disclaimer",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        height: 18 / 14,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16 / 2),
                    Text(
                      "Every effort is made to maintain the accuracy of all information. However, actual product packaging and material may contain more and/or different information. the color of the product displayed in the image may differ from the received in the order. it is recommended not to sole rely on the details presented",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 21 / 14,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                  ],
                ),
              ),
              // const SizedBox(height: 38),
              // const Text(
              //   "Frequently bought together",
              //   style: TextStyle(
              //     fontSize: 24,
              //     fontWeight: FontWeight.w700,
              //     color: Color(0xff181725),
              //   ),
              // ).paddingSymmetric(horizontal: 16),
              // const SizedBox(height: 20),
              // FutureBuilder<List<CategoryProducts?>>(
              //   future: categorycontroller.getProductsByCategory(
              //     type: "similar",
              //     categoryId: widget.item.categoryId!,
              //     subcategoryid:
              //         widget.item.subCategoryId.toString(),
              //   ),
              //   builder: (context, snapshot) {
              //     if (snapshot.connectionState ==
              //         ConnectionState.waiting) {
              //       return SizedBox(
              //         height: 200,
              //         child: ListView.builder(
              //           shrinkWrap: true,
              //           scrollDirection: Axis.horizontal,
              //           itemCount: 4,
              //           itemBuilder:
              //               (BuildContext context, int index) {
              //             return Shimmer.fromColors(
              //               baseColor: Colors.grey[200]!,
              //               highlightColor: Colors.grey[100]!,
              //               child: Container(
              //                 height: 200,
              //                 width: 140,
              //                 color: kgrey,
              //               ).paddingAll(5),
              //             );
              //           },
              //         ).paddingSymmetric(horizontal: 16),
              //       );
              //     }
              //     if (snapshot.data == null) {
              //       return const SizedBox.shrink();
              //     }
              //     return SizedBox(
              //       height: 400,
              //       child: ListView.separated(
              //         shrinkWrap: true,
              //         padding: const EdgeInsets.symmetric(
              //           horizontal: 16,
              //         ),
              //         scrollDirection: Axis.horizontal,
              //         itemCount: snapshot.data
              //                 ?.where((element) =>
              //                     element!.productId !=
              //                     widget.item.productId)
              //                 .length ??
              //             0,
              //         itemBuilder: (BuildContext context, int index) {
              //           final model = snapshot.data
              //                   ?.where((element) =>
              //                       element!.productId !=
              //                       widget.item.productId)
              //                   .toList() ??
              //               [];
              //           return _similarProductCard(
              //             index,
              //             model[index],
              //           );
              //         },
              //         separatorBuilder: (_, __) =>
              //             const SizedBox(width: 5),
              //       ),
              //     );
              //   },
              // ),
              const SizedBox(height: 40),
            ],
          ),
          const Align(
            alignment: Alignment.bottomCenter,
            child: MinimisedCartItemStatusWidget(),
          ),
        ],
      ),
    );
  }

  // Widget _similarProductCard(index, CategoryProducts? model) {
  //   // print("model=${model!.toJson()}"),;
  //   var discountVal = model?.mrp == null || model?.mrp == 0.0
  //       ? 0.0
  //       : ((100 * ((model?.mrp ?? 0.0) - (model?.price ?? 0.0))) /
  //               (model?.mrp ?? 0.0))
  //           .floorToDouble();
  //   return Column(
  //     children: [
  //       SizedBox(
  //         width: 150,
  //         // height: 261,
  //         child: Stack(
  //           clipBehavior: Clip.none,
  //           children: [
  //             InkWell(
  //               onTap: () {
  //                 Get.close(1);
  //                 showBarModalBottomSheet(
  //                   barrierColor: AppColors.kblack.withOpacity(0.2),
  //                   context: Get.context!,
  //                   shape: RoundedRectangleBorder(
  //                       borderRadius: BorderRadius.circular(10)),
  //                   builder: (context) {
  //                     return SizedBox(
  //                       height: Get.height * 0.8,
  //                       // child: ProductScreen(
  //                       //   item: model!,
  //                       //   // variantId: model.variantId?.toString(),
  //                       // ),
  //                     );
  //                   },
  //                 );
  //               },
  //               child: ClipRRect(
  //                 borderRadius: BorderRadius.circular(2),
  //                 child: SizedBox(
  //                   height: 232,
  //                   child: Stack(
  //                     children: [
  //                       Container(
  //                         color: Colors.white,
  //                         padding: const EdgeInsets.all(10),
  //                         child: Column(
  //                           crossAxisAlignment: CrossAxisAlignment.center,
  //                           children: [
  //                             if (model?.images?.isNotEmpty ?? false)
  //                               SizedBox(
  //                                 height: 110,
  //                                 child: CachedNetworkImage(
  //                                   imageUrl: "${model?.images?.first.url}",
  //                                    httpHeaders: {
  //   HttpHeaders
  //           .authorizationHeader:
  //       'bearer ${userController.userdata.value?.token}'
  // },
  //                                   fit: BoxFit.cover,
  //                                   errorWidget: (context, error, stackTrace) =>
  //                                       Image.asset(
  //                                           "assets/images/error-image.webp"),
  //                                 ),
  //                               )
  //                             else
  //                               SizedBox(
  //                                 height: 110,
  //                                 child: Image.asset(
  //                                   "assets/images/error-image.webp",
  //                                   fit: BoxFit.cover,
  //                                 ),
  //                               ),
  //                             const SizedBox(height: 5),
  //                             SizedBox(
  //                               height: 36,
  //                               child: Align(
  //                                 alignment: Alignment.centerLeft,
  //                                 child: Text(
  //                                   "${model?.name?.toString().capitalizeFirst}",
  //                                   overflow: TextOverflow.ellipsis,
  //                                   maxLines: 2,
  //                                   textAlign: TextAlign.start,
  //                                   style: const TextStyle(
  //                                     height: 1,
  //                                     fontSize: 16,
  //                                     color: AppColors.kblack,
  //                                     fontWeight: FontWeight.w500,
  //                                   ),
  //                                 ),
  //                               ),
  //                             ),
  //                             Align(
  //                               alignment: Alignment.topLeft,
  //                               child: Text(
  //                                 model?.weight != null
  //                                     ? getweight(
  //                                         model?.weight?.toString() ?? "0.0")
  //                                     : "---",
  //                                 style: const TextStyle(
  //                                   fontSize: 12,
  //                                   color: kgrey,
  //                                   fontWeight: FontWeight.w400,
  //                                   letterSpacing: -0.1,
  //                                 ),
  //                               ),
  //                             ),
  //                             const SizedBox(height: 12),
  //                             Align(
  //                               alignment: Alignment.topLeft,
  //                               child: Row(
  //                                 children: [
  //                                   FittedBox(
  //                                     child: Text(
  //                                       "₹${model?.price.round()}",
  //                                       style: const TextStyle(
  //                                         fontSize: 16,
  //                                         color: AppColors.kblack,
  //                                         height: 20.83 / 16,
  //                                         letterSpacing: -0.01,
  //                                         fontWeight: FontWeight.w500,
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const SizedBox(width: 5),
  //                                   Text(
  //                                     model?.price == model?.mrp ||
  //                                             model?.mrp == 0.0
  //                                         ? ""
  //                                         : "₹${model?.mrp.round()}",
  //                                     style: TextStyle(
  //                                       fontSize: 12,
  //                                       color: AppColors.kblack.withOpacity(0.5),
  //                                       fontWeight: FontWeight.w400,
  //                                       height: 15.6 / 12,
  //                                       decoration: TextDecoration.lineThrough,
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                             ),
  //                             const SizedBox(height: 8),
  //                           ],
  //                         ),
  //                       ),
  //                       Visibility(
  //                         visible: discountVal != 0,
  //                         replacement: const SizedBox(
  //                           width: 32,
  //                           height: 44,
  //                         ),
  //                         child: Align(
  //                           alignment: Alignment.topRight,
  //                           child: SizedBox(
  //                             width: 32,
  //                             height: 44,
  //                             child: Stack(
  //                               alignment: Alignment.topRight,
  //                               children: [
  //                                 SvgPicture.asset(
  //                                   'assets/svg/offertag.svg',
  //                                   colorFilter: const ColorFilter.mode(
  //                                     Color(0xffFF5454),
  //                                     BlendMode.srcIn,
  //                                   ),
  //                                 ),
  //                                 Center(
  //                                   child: Text(
  //                                     "${discountVal.round()}% \n OFF",
  //                                     style: const TextStyle(
  //                                       color: Colors.white,
  //                                       fontSize: 12,
  //                                       fontWeight: FontWeight.w700,
  //                                       letterSpacing: -0.5,
  //                                     ),
  //                                     textAlign: TextAlign.center,
  //                                   ).paddingOnly(bottom: 10),
  //                                 ),
  //                               ],
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //             Positioned(
  //               bottom: -18,
  //               left: 0,
  //               right: 0,
  //               child: StatefulBuilder(
  //                 builder: (context, setState) {
  //                   // DatabaseHelper.instance.getGroceries();
  //                   return FutureBuilder<int>(
  //                     future: DatabaseHelper.instance.getQty(
  //                       LocalShoppingCart(
  //                         // variantID: model!.variantId?.toString(),
  //                         productID: model!.productId!,
  //                       ),
  //                     ),
  //                     builder: (context, snapshot) {
  //                       if (snapshot.data == 0) {
  //                         return InkWell(
  //                           onTap: () async {
  //                             await DatabaseHelper.instance.addUpdate(
  //                               LocalShoppingCart(
  //                                 mrp: model.mrp,
  //                                 name: model.name,
  //                                 qty: 1,
  //                                 productID: model.productId!,
  //                                 price: model.price,
  //                                 // variantID: model.variantId?.toString(),
  //                                 imageURL: (model.images?.isEmpty ?? true)
  //                                     ? ""
  //                                     : (model.images?.first.url ?? ''),
  //                                 weight: double.parse(
  //                                   model.weight.toString(),
  //                                 ),
  //                               ),
  //                             );
  //                             // final eventItem = AnalyticsEventItem(
  //                             //   itemId: model.productId.toString(),
  //                             //   itemName: model.name,
  //                             //   itemCategory: model.category,
  //                             //   itemVariant: model.variantName,
  //                             //   price: model.price,
  //                             //   quantity: 1,
  //                             // );
  //                             // await FirebaseService.firebaseAnalytics
  //                             //     .logAddToCart(items: [eventItem]);
  //                             setState(() {
  //                               // update = 1;
  //                             });
  //                           },
  //                           child: Center(
  //                             child: Container(
  //                               height: 36,
  //                               width: 84,
  //                               alignment: Alignment.center,
  //                               decoration: BoxDecoration(
  //                                 color: Colors.white,
  //                                 border: Border.all(color: kblue),
  //                                 borderRadius: BorderRadius.circular(2),
  //                               ),
  //                               child: const Text(
  //                                 "Add",
  //                                 style: TextStyle(
  //                                   fontWeight: FontWeight.w700,
  //                                   fontSize: 16,
  //                                   height: 20.8 / 16,
  //                                   color: kblue,
  //                                 ),
  //                               ),
  //                             ),
  //                           ),
  //                         );
  //                       } else {
  //                         return Center(
  //                           child: AnimatedContainer(
  //                             duration: const Duration(milliseconds: 100),
  //                             decoration: BoxDecoration(
  //                               color: Colors.white,
  //                               border: Border.all(color: kblue),
  //                               borderRadius: BorderRadius.circular(2),
  //                             ),
  //                             height: 36,
  //                             width: 84,
  //                             child: Row(
  //                               mainAxisAlignment:
  //                                   MainAxisAlignment.spaceAround,
  //                               children: [
  //                                 Flexible(
  //                                   child: InkWell(
  //                                     onTap: () async {
  //                                       await DatabaseHelper.instance
  //                                           .removeUpdate(
  //                                         LocalShoppingCart(
  //                                           mrp: model.mrp,
  //                                           name: model.name,
  //                                           qty: 1,
  //                                           productID: model.productId!,
  //                                           price: model.price,
  //                                           // variantID:model.variantId?.toString(),
  //                                           imageURL: model.images?.isEmpty ??
  //                                                   true
  //                                               ? ""
  //                                               : model.images?.first.url ?? '',
  //                                           weight: double.parse(
  //                                             model.weight?.toString() ?? "0",
  //                                           ),
  //                                         ),
  //                                       );
  //                                       setState(() {
  //                                         // update = 1;
  //                                       });
  //                                     },
  //                                     child: const Icon(
  //                                       Icons.remove_outlined,
  //                                       color: kblue,
  //                                     ),
  //                                   ),
  //                                 ),
  //                                 GetBuilder<CartController>(
  //                                   init: CartController(),
  //                                   initState: (_) {},
  //                                   builder: (cartController) {
  //                                     return Flexible(
  //                                       child: Text(
  //                                         "${snapshot.data ?? ""}",
  //                                         style: const TextStyle(
  //                                           fontSize: 14,
  //                                           fontWeight: FontWeight.w700,
  //                                           color: kblue,
  //                                         ),
  //                                       ),
  //                                     );
  //                                   },
  //                                 ),
  //                                 Flexible(
  //                                   child: InkWell(
  //                                     onTap: () async {
  //                                       await DatabaseHelper.instance.addUpdate(
  //                                         LocalShoppingCart(
  //                                           mrp: model.mrp,
  //                                           name: model.name,
  //                                           qty: 1,
  //                                           productID: model.productId!,
  //                                           price: model.price,
  //                                           // variantID:model.variantId?.toString(),
  //                                           imageURL: model.images?.isEmpty ??
  //                                                   true
  //                                               ? ""
  //                                               : model.images?.first.url ?? "",
  //                                           weight: double.parse(
  //                                             model.weight?.toString() ?? "0",
  //                                           ),
  //                                         ),
  //                                       );
  //                                       setState(() {
  //                                         // update = 1;
  //                                       });
  //                                     },
  //                                     child: const Icon(
  //                                       Icons.add,
  //                                       color: kblue,
  //                                     ),
  //                                   ),
  //                                 )
  //                               ],
  //                             ),
  //                           ),
  //                         );
  //                       }
  //                     },
  //                   );
  //                 },
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Container _productImageCard(List<String> images) {
    return Container(
      height: Get.height * 0.35 + 30,
      decoration: const BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          GetBuilder<ProductViewController>(
            builder: (controller) {
              return CarouselSlider.builder(
                itemCount: images.isEmpty ? 1 : images.length,
                itemBuilder: (
                  BuildContext context,
                  int itemIndex,
                  int pageViewIndex,
                ) {
                  return CachedNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl:
                        images.elementAtOrNull(itemIndex)?.toString() ?? '',
                    errorListener: (value) {
                      log('Image failed to load: $value');
                    },
                    placeholder:
                        (context, url) => Center(
                          child: CircularProgressIndicator.adaptive(
                            valueColor: AlwaysStoppedAnimation(
                              Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    errorWidget:
                        (context, url, error) =>
                            Image.asset("assets/images/error-image.webp"),
                  );
                },
                options: CarouselOptions(
                  scrollPhysics:
                      (images.length) == 1 || images.isEmpty
                          ? const NeverScrollableScrollPhysics()
                          : const ScrollPhysics(),
                  initialPage: controller.carouselIndex,
                  onPageChanged: (index, reason) {
                    controller
                      ..carouselIndex = index
                      ..update();
                  },
                  height: Get.height * 0.35,
                  viewportFraction: 1,
                  padEnds: false,
                  clipBehavior: Clip.none,
                  autoPlay: false,
                ),
              );
            },
          ),
          GetBuilder<ProductViewController>(
            builder: (controller) {
              return Visibility(
                visible: images.length > 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    images.length,
                    (index) => Container(
                      height: 7,
                      width: 7,
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      decoration: ShapeDecoration(
                        shape: const CircleBorder(),
                        color:
                            index == controller.carouselIndex
                                ? Theme.of(
                                  context,
                                ).primaryColor.withOpacity(0.7)
                                : Colors.grey[300]!,
                      ),
                    ),
                  ),
                ),
              );
            },
          ).paddingSymmetric(horizontal: 16),
        ],
      ),
    );
  }

  Widget getSvgIcon(String icon) =>
      SvgPicture.asset(icon, semanticsLabel: 'Acme Logo');
}

Future<void> showProductScreenSheet({required Item item}) async {
  await showBarModalBottomSheet(
    barrierColor: Colors.black.withOpacity(0.2),
    context: Get.context!,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    builder: (context) {
      return SizedBox(
        height: Get.height * 0.8,
        child: ProductScreen(item: item),
      );
    },
  );
}

class ProductScreenShimmer extends StatelessWidget {
  const ProductScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[200]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(height: 40, width: double.infinity, color: AppColors.kgrey),
          const SizedBox(height: 10),
          Container(
            height: 260,
            width: double.infinity,
            color: AppColors.kgrey,
          ),
          const SizedBox(height: 20),
          Container(
            height: 50,
            width: Get.height * 0.25,
            color: AppColors.kgrey,
          ),
          const SizedBox(height: 20),
          Container(
            height: 20,
            width: Get.height * 0.1,
            color: AppColors.kgrey,
          ),
          const SizedBox(height: 30),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: Container(height: 50, color: AppColors.kgrey)),
              const SizedBox(width: 10),
              Expanded(child: Container(height: 50, color: AppColors.kgrey)),
            ],
          ),
          const SizedBox(height: 50),
          const Divider(thickness: 2, color: Colors.grey),
          const SizedBox(height: 30),
          Container(height: 20, width: 50, color: AppColors.kgrey),
          const SizedBox(height: 10),
          Container(
            height: 100,
            width: double.infinity,
            color: AppColors.kgrey,
          ),
        ],
      ),
    );
  }
}
