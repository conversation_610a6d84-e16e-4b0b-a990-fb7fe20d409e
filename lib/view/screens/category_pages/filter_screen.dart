import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FilterScreen extends StatelessWidget {
  const FilterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: 0,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(65),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 20, right: 10, bottom: 3),
                        child: Icon<PERSON>utton(
                          onPressed: () {
                            Get.back();
                          },
                          icon: SizedBox(
                            height: 20,
                            child: Icon(
                              Icons.arrow_back,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ),
                      const Text(
                        'Filters',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Divider(
                thickness: 4,
                color: Colors.black.withOpacity(0.06),
                height: 0,
              )
            ],
          ),
        ),
      ),
      body: const Column(
        children: [
          Column(
            children: [],
          ),
          Column(
            children: [],
          )
        ],
      ),
    );
  }
}
