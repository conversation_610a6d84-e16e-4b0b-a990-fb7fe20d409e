// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:marquee/marquee.dart';
import 'package:shimmer/shimmer.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_grid_view.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_tile.dart';

import '../../../controllers/home_view_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../utils/colors.dart';
import '../map_screen/select_location_screen.dart';
import '../search_screen/search_screen.dart';
import '../../../controllers/category_controller.dart';
import '../../../model/category_model/data.dart';
import '../../widgets/minimised_cart_item_status_widget.dart';
import '../../widgets/widgets.dart';
import 'category_card.dart';

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({super.key, required this.initialCategory});
  final CategoryItemModel initialCategory;

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with TickerProviderStateMixin {
  int page = 1;
  late final AnimationController _controller = AnimationController(
    lowerBound: 0.8,
    upperBound: 1,
    reverseDuration: const Duration(milliseconds: 100),
    duration: const Duration(seconds: 2),
    vsync: this,
  )..repeat(reverse: true);
  late final Animation<double> _animation = CurvedAnimation(
    parent: _controller,
    curve: Curves.easeInBack,
  );
  final CategoryController categoryController = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    scrollController.addListener(() {
      final nextPageTrigger =
          scrollController.position.maxScrollExtent -
          80; // 80 being appbar's height
      if (scrollController.position.pixels > nextPageTrigger &&
          !categoryController.nextpageloading &&
          categoryController.totalResults.value >
              categoryController.categoryproductmodel.length) {
        categoryController.getproducts(page: page++);
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      categoryController.selectParentCategory(widget.initialCategory);
    });
    // getdata();
    // setState(() {});
    // scrollController.addListener(() {
    //   var nextPageTrigger =
    //       scrollController.position.maxScrollExtent - Get.height * 0.3;
    //   if (scrollController.position.pixels > nextPageTrigger &&
    //       categoryController.nextpageloading == false &&
    //       categoryController.categorytotal.value >
    //           categoryController.categoryproductmodel.length) {
    //     categoryController.nextpageloading = true;
    //     categoryController.update();

    //     getproducts();
    //   }
    // });

    super.initState();
  }

  @override
  void dispose() {
    categoryController
      ..selectedCategory = null
      ..selectedSubCategory = null
      ..categoryproductmodel.clear()
      ..update();

    scrollController.dispose();
    _controller.dispose();

    super.dispose();
  }

  final HomeViewController homeViewController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 80,
        foregroundColor: Colors.black,
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        title: SizedBox(
          height: 30,
          width: 300,
          child: GetBuilder<CategoryController>(
            builder: (categoryController) {
              return PopupMenuButton<CategoryItemModel>(
                onSelected: (value) {
                  categoryController.selectParentCategory(value);
                },
                initialValue: categoryController.selectedCategory,
                position: PopupMenuPosition.under,
                constraints: const BoxConstraints(minWidth: 200, maxWidth: 300),
                padding: EdgeInsets.zero,
                elevation: 12,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                icon: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(
                      child:
                          (categoryController.selectedCategory?.name?.length ??
                                      0) >
                                  17
                              ? Marquee(
                                blankSpace: 30,
                                textDirection: TextDirection.ltr,
                                text:
                                    categoryController.selectedCategory?.name ??
                                    "",
                                velocity: 40,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                ),
                              )
                              : Text(
                                categoryController.selectedCategory?.name ?? "",
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(top: 3),
                      child: Icon(Icons.keyboard_arrow_down_sharp),
                    ),
                  ],
                ),
                color: Colors.white,
                itemBuilder:
                    (ctx) => List.generate(
                      homeViewController.categoryList.length,
                      (index) {
                        return buildPopupMenuItem(
                          homeViewController.categoryList.elementAt(index),
                        );
                      },
                    ),
              );
            },
          ),
        ),
        actions: [
          GestureDetector(
            child: IconButton(
              onPressed: () {
                Get.to(const SearchScreen());
              },
              icon: const Icon(CupertinoIcons.search),
            ).paddingAll(16),
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GetBuilder<CategoryController>(
                builder: (categoryController) {
                  return Visibility(
                    visible:
                        categoryController
                            .selectedCategory
                            ?.subCategories
                            .isNotEmpty ??
                        false,
                    child: Container(
                      height: 52,
                      margin: const EdgeInsets.symmetric(vertical: 16),
                      child: ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        shrinkWrap: true,
                        itemExtent: 133,
                        itemCount:
                            categoryController
                                .selectedCategory
                                ?.subCategories
                                .length,
                        semanticChildCount:
                            categoryController
                                .selectedCategory
                                ?.subCategories
                                .length,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          final model = categoryController
                              .selectedCategory
                              ?.subCategories
                              .elementAtOrNull(index);
                          return GestureDetector(
                            onTap: () async {
                              categoryController
                                ..selectedSubCategory = model
                                ..getproducts()
                                ..update();
                            },
                            child: Container(
                              margin: const EdgeInsets.symmetric(horizontal: 8),
                              decoration: ShapeDecoration(
                                shape: RoundedRectangleBorder(
                                  side:
                                      model ==
                                              categoryController
                                                  .selectedSubCategory
                                          ? BorderSide.none
                                          : const BorderSide(
                                            color: Color(0xffdcdcdc),
                                          ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                color:
                                    model ==
                                            categoryController
                                                .selectedSubCategory
                                        ? const Color(0xffA5FFEF)
                                        : Colors.white,
                              ),
                              padding: const EdgeInsets.fromLTRB(6, 6, 12, 6),
                              height: 52,
                              // width: 117,
                              child: Row(
                                children: [
                                  SizedBox(
                                    height: 40,
                                    width: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: model?.imageUrl ?? '',
                                      placeholder: (context, url) {
                                        return CircularProgressIndicator.adaptive(
                                          valueColor: AlwaysStoppedAnimation(
                                            Theme.of(context).primaryColor,
                                          ),
                                        );
                                      },
                                      fit: BoxFit.fitHeight,
                                      errorWidget: (context, url, error) {
                                        return Image.asset(
                                          'assets/images/placeholder.webp',
                                          fit: BoxFit.cover,
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      model?.name?.capitalize ?? '',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.dmSans(
                                        color: Colors.black,
                                        fontWeight:
                                            categoryController
                                                        .selectedSubCategory ==
                                                    model
                                                ? FontWeight.w700
                                                : FontWeight.w500,
                                        fontSize: 10,
                                        height: 1,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
              Expanded(
                child: GetBuilder<CategoryController>(
                  builder: (categoryController) {
                    return Column(
                      children: [
                        Expanded(
                          child:
                              categoryController.itemsLoading.value
                                  ? FadeTransition(
                                    opacity: _animation,
                                    child: const CategoryShimmer(),
                                  )
                                  : Visibility(
                                    visible:
                                        categoryController
                                            .categoryproductmodel
                                            .isNotEmpty,
                                    replacement: const Center(
                                      child: Text(
                                        'No items found for this category',
                                      ),
                                    ),
                                    child: ListView(
                                      controller: scrollController,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                      ),
                                      children: [
                                        // categoryController.subcategorymodel[categoryController.selectedindex]!
                                        //             .bannerimage ==
                                        //         null
                                        //     ? const SizedBox.shrink()
                                        //     : CachedNetworkImage(
                                        //         fadeInDuration: const Duration(
                                        //             milliseconds: 100),
                                        //         imageUrl: categoryController
                                        //             .subcategorymodel[
                                        //                 categoryController.selectedindex]!
                                        //             .bannerimage,
                                        //          httpHeaders: {
                                        //             HttpHeaders.authorizationHeader:'bearer ${userController.userdata.value?.token}'
                                        //          },
                                        //         placeholder: (context, url) =>
                                        //             const SizedBox(),
                                        //         errorWidget:
                                        //             (context, url, error) =>
                                        //                 const SizedBox(),
                                        //       ).paddingAll(1),
                                        StaggeredGridView.countBuilder(
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount:
                                              categoryController
                                                  .categoryproductmodel
                                                  .length +
                                              1,
                                          crossAxisCount: 2,
                                          crossAxisSpacing: 9,
                                          mainAxisSpacing: 8,
                                          staggeredTileBuilder: (index) {
                                            if (index ==
                                                categoryController
                                                    .categoryproductmodel
                                                    .length) {
                                              return const StaggeredTile.fit(2);
                                            } else {
                                              return const StaggeredTile.fit(1);
                                            }
                                          },
                                          itemBuilder: (context, index) {
                                            if (index ==
                                                categoryController
                                                    .categoryproductmodel
                                                    .length) {
                                              return categoryController
                                                      .nextpageloading
                                                  ? Column(
                                                    children: [
                                                      const SizedBox(
                                                        height: 50,
                                                      ),
                                                      CircularProgressIndicator.adaptive(
                                                        valueColor:
                                                            AlwaysStoppedAnimation(
                                                              Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                            ),
                                                      ),
                                                      const SizedBox(
                                                        height: 50,
                                                      ),
                                                    ],
                                                  )
                                                  : const SizedBox(height: 100);
                                            } else {
                                              final model =
                                                  categoryController
                                                      .categoryproductmodel[index];

                                              return CategoryCard(model: model);
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
          const Align(
            alignment: Alignment.bottomCenter,
            child: MinimisedCartItemStatusWidget(),
          ),
        ],
      ),
    );
  }

  PopupMenuItem<CategoryItemModel> buildPopupMenuItem(CategoryItemModel data) {
    return PopupMenuItem<CategoryItemModel>(
      value: data,
      onTap: () {
        categoryController
          ..selectedCategory = data
          ..update();
      },
      padding: EdgeInsets.zero,
      child: SizedBox(
        height: 40,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Flexible(
                child: Text(
                  data.name?.capitalize ?? '---',
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color:
                        categoryController.selectedCategory == data
                            ? Theme.of(context).primaryColor
                            : Colors.black,
                    fontSize:
                        categoryController.selectedCategory == data ? 16 : 14,
                    fontWeight:
                        categoryController.selectedCategory == data
                            ? FontWeight.w700
                            : FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CategoryShimmer extends StatelessWidget {
  const CategoryShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        mainAxisExtent: 250,
        crossAxisCount: 2,
      ),
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xffE1E1E1), width: 0.4),
          ),
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomAppShimmer(
                child: Container(
                  height: 110,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: AppColors.kgrey.withOpacity(0.5),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              CustomAppShimmer(
                child: Container(
                  height: 20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: AppColors.kgrey.withOpacity(0.5),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              CustomAppShimmer(
                child: Container(
                  height: 10,
                  width: 30,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: AppColors.kgrey.withOpacity(0.5),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Column(
                    children: [
                      CustomAppShimmer(
                        child: Container(
                          height: 10,
                          width: 30,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            color: AppColors.kgrey.withOpacity(0.5),
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),
                      CustomAppShimmer(
                        child: Container(
                          height: 20,
                          width: 30,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            color: AppColors.kgrey.withOpacity(0.5),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  SizedBox(
                    height: 30,
                    width: 80,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CustomAppShimmer(
                          child: Container(
                            height: 30,
                            width: 65,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: AppColors.kgrey.withOpacity(0.5),
                            ),
                          ),
                        ),
                        Shimmer.fromColors(
                          baseColor: Colors.black.withOpacity(0.1),
                          highlightColor: Colors.white,
                          child: const Text(
                            "Add",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

// class AnimatedSubCategory extends StatefulWidget {
//   final Widget child;

//   final double height;

//   const AnimatedSubCategory(
//       {super.key, required this.child, required this.height});

//   @override
//   State<AnimatedSubCategory> createState() => _AnimatedSubCategoryState();
// }

// class _AnimatedSubCategoryState extends State<AnimatedSubCategory> {
//   double height = 0;

//   @override
//   void initState() {
//     Future.delayed(const Duration(milliseconds: 10), () {
//       setState(() {
//         height = widget.height;
//       });
//     });

//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return AnimatedContainer(
//       width: double.infinity,
//       decoration:
//           BoxDecoration(color: Colors.white.withOpacity(0.9), boxShadow: const [
//         BoxShadow(
//           color: kgrey,
//           blurRadius: 5.0,
//         )
//       ]),
//       duration: const Duration(milliseconds: 100),
//       height: height,
//       child: widget.child,
//     );
//   }
// }
