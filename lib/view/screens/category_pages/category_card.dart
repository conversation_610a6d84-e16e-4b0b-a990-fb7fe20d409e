import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/cart_controlller.dart';
import '../../../controllers/user_controller.dart';
import '../../../model/database_models/database_models.dart';
import '../../../model/item_list_model/item_list_model.dart';
import '../../../services/database_helper.dart';
import '../../../utils/colors.dart';
import '../../../utils/enumerations.dart';
import '../map_screen/select_location_screen.dart';
import '../product_screen/product_screen.dart';

class CategoryCard extends StatelessWidget {
  const CategoryCard({super.key, required this.model});

  final Item model;
  double get discountVal =>
      model.mrp == 0.0
          ? 0.0
          : ((100 * (model.mrp - model.price)) / model.mrp).floorToDouble();
  double? get discountInRupees =>
      model.discount?.type == DiscountType.currency
          ? model.discount?.value
          : model.price.toDouble() * ((model.discount?.value ?? 0.0) / 100);
  int get availableStock => model.availableStock;
  double get taxInRupees => model.tax.fold<double>(
    0.0,
    (prev, e) => prev + (((e.value ?? 0).toDouble() / 100.0) * model.price),
  );

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          model.availableStock < 1
              ? null
              : () async {
                Get.to(() => ProductScreen(item: model));
              },
      child: Container(
        height: Get.height * 0.35,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: const Color(0xffE1E1E1), width: 0.4),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Stack(
            children: [
              // product tile
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  //image
                  Expanded(
                    child:
                        model.imageUrls.isEmpty
                            ? Image.asset('assets/images/error-image.webp')
                            : CachedNetworkImage(
                              placeholder: (context, url) {
                                return Image.asset(
                                  'assets/images/error-image.webp',
                                );
                              },
                              errorWidget:
                                  (context, url, error) => Image.asset(
                                    'assets/images/error-image.webp',
                                  ),
                              fit: BoxFit.cover,
                              imageUrl: model.imageUrls.firstOrNull ?? "",
                            ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                        horizontal: 6,
                      ),
                      decoration: BoxDecoration(color: AppColors.kHomeAppBarBG),
                      child: Column(
                        children: [
                          // product name
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              model.name!.capitalize!,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              textAlign: TextAlign.start,
                              style: const TextStyle(
                                height: 1,
                                fontSize: 16,
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),

                          // product weight
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${model.weight ?? ''} ${model.unit ?? ''}",
                              style: GoogleFonts.dmSans(
                                fontSize: 12,
                                letterSpacing: -0.01,
                              ),
                            ),
                          ),
                          const Spacer(),

                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // prices
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    FittedBox(
                                      child: Text(
                                        '\u20b9${model.price == 0 ? model.mrp.toInt() : model.price.toInt()}',
                                        style: GoogleFonts.inter(
                                          height: 1,
                                          fontSize: 16,
                                          color: Colors.black,
                                          letterSpacing: -0.01,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    Visibility(
                                      visible:
                                          model.price != 0 &&
                                          model.mrp != 0 &&
                                          model.price != model.mrp,
                                      child: Text(
                                        '\u20b9${model.mrp.round()}',
                                        style: GoogleFonts.inter(
                                          fontSize: 12,
                                          color: Colors.black.withOpacity(0.5),
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 5),

                              // add/remove button
                              StatefulBuilder(
                                builder: (context, setState) {
                                  return GetBuilder<CartController>(
                                    init: CartController(),
                                    builder: (cartController) {
                                      return FutureBuilder<int>(
                                        future: DatabaseHelper.instance.getQty(
                                          LocalShoppingCart(
                                            productID: model.id!,
                                            purchaseOrderID:
                                                model.purchaseOrderId ?? '',
                                          ),
                                        ),
                                        builder: (context, snapshot) {
                                          if (snapshot.data == null) {
                                            return addButtonShimmer();
                                          }
                                          bool canBeIncremented =
                                              (snapshot.data ?? 0) <
                                              availableStock;
                                          return snapshot.data == 0
                                              // ADD button
                                              ? InkWell(
                                                onTap:
                                                    availableStock < 1
                                                        ? null
                                                        : () async {
                                                          await DatabaseHelper.instance.addUpdate(
                                                            LocalShoppingCart(
                                                              mrp: model.mrp,
                                                              name: model.name,
                                                              qty: 1,
                                                              productID:
                                                                  model.id!,
                                                              purchaseOrderID:
                                                                  model
                                                                      .purchaseOrderId!,
                                                              price:
                                                                  model.price,
                                                              imageURL:
                                                                  model
                                                                      .imageUrls
                                                                      .firstOrNull,
                                                              availableStock:
                                                                  availableStock,
                                                              discount:
                                                                  discountInRupees,
                                                              tax: taxInRupees,
                                                            ),
                                                          );
                                                          setState(() {});
                                                        },
                                                child: Container(
                                                  height: 36,
                                                  width: 84,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    border: Border.all(
                                                      color:
                                                          Theme.of(
                                                            context,
                                                          ).primaryColor,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          2,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    'Add',
                                                    style: TextStyle(
                                                      color:
                                                          Theme.of(
                                                            context,
                                                          ).primaryColor,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 16,
                                                    ),
                                                  ),
                                                ),
                                              )
                                              // counter button
                                              : Container(
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  border: Border.all(
                                                    color:
                                                        Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(2),
                                                ),
                                                height: 36,
                                                width: 84,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  children: [
                                                    // decrement
                                                    Flexible(
                                                      child: InkWell(
                                                        onTap: () async {
                                                          await DatabaseHelper
                                                              .instance
                                                              .removeUpdate(
                                                                LocalShoppingCart(
                                                                  productID:
                                                                      model.id!,
                                                                  purchaseOrderID:
                                                                      model
                                                                          .purchaseOrderId!,
                                                                ),
                                                              );
                                                          setState(() {});
                                                        },
                                                        child: Icon(
                                                          Icons.remove_outlined,
                                                          color:
                                                              Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                        ),
                                                      ),
                                                    ),

                                                    // quantity
                                                    Flexible(
                                                      child: Text(
                                                        "${snapshot.data ?? 0}",
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          color:
                                                              Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                        ),
                                                      ),
                                                    ),

                                                    // increment
                                                    Flexible(
                                                      child: InkWell(
                                                        onTap:
                                                            !canBeIncremented
                                                                ? null
                                                                : () async {
                                                                  await DatabaseHelper.instance.addUpdate(
                                                                    LocalShoppingCart(
                                                                      productID:
                                                                          model
                                                                              .id!,
                                                                      purchaseOrderID:
                                                                          model
                                                                              .purchaseOrderId!,
                                                                    ),
                                                                  );
                                                                  setState(
                                                                    () {},
                                                                  );
                                                                },
                                                        child: Icon(
                                                          Icons.add,
                                                          size: 17,
                                                          color:
                                                              Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                        },
                                      );
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              // discount badge
              Visibility(
                visible: discountVal != 0,
                replacement: const SizedBox(width: 32, height: 44),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: SizedBox(
                    width: 32,
                    height: 44,
                    child: Stack(
                      alignment: Alignment.topLeft,
                      children: [
                        SvgPicture.asset(
                          'assets/svg/offertag.svg',
                          colorFilter: const ColorFilter.mode(
                            Color(0xffFF5454),
                            BlendMode.srcIn,
                          ),
                        ),
                        Center(
                          child: Text(
                            "${discountVal.round()}% \n OFF",
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              letterSpacing: -0.5,
                            ),
                            textAlign: TextAlign.center,
                          ).paddingOnly(bottom: 10),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Visibility(
                visible: availableStock < 1,
                child: Container(
                  color: Colors.white.withOpacity(0.6),
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          offset: const Offset(0, 0),
                          color: Colors.black.withOpacity(0.4),
                          blurRadius: 15,
                          spreadRadius: 0,
                        ),
                      ],
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    padding: const EdgeInsets.all(4),
                    child: Text(
                      'Out of Stock',
                      style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.w700,
                        letterSpacing: -0.015,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  SizedBox addButtonShimmer() {
    return SizedBox(
      height: 30,
      width: 80,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomAppShimmer(
            child: Container(
              height: 30,
              width: 65,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: AppColors.kgrey.withOpacity(0.5),
              ),
            ),
          ),
          Shimmer.fromColors(
            baseColor: Colors.black.withOpacity(0.1),
            highlightColor: Colors.white,
            child: const Text(
              "Add",
              style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
