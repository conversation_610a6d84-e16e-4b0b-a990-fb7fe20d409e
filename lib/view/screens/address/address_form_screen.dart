import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:group_button/group_button.dart';
import 'package:shimmer/shimmer.dart';

import '../../../model/address_model.dart';
import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';
import '../login/widgets/button.dart';
import '../map_screen/search_page.dart';
import '../map_screen/select_location_screen.dart';
import '../../../controllers/account_controller.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/user_controller.dart';
import 'widgets/input_box.dart';

enum AddressFormMode { edit, add }

class AddressFormScreen extends StatefulWidget {
  final AddressFormMode mode;
  final Address? address;

  const AddressFormScreen({
    super.key,
    this.address,
    required this.mode,
  }) : assert((mode == AddressFormMode.edit && address != null) ||
            (mode == AddressFormMode.add && address == null));

  @override
  State<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen> {
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.find<AccountController>();

  final _addressFormKey = GlobalKey<FormState>();
  UserController usertCTRL = Get.find();

  String? name;
  String? phone;
  String? type;
  String? line1;
  String? line2;
  String? landmark;
  String? state;
  String? city;
  String? pincode;
  double? longitude;
  double? latitude;
  final gcontroller = GroupButtonController(
    selectedIndex: 0,
  );
  Placemark? locationAddress;

  bool loading = false;

  void getCurrentLatLong() async {
    setState(() {
      loading = true;
    });
    final position = await determinePosition();
    longitude = position.longitude;
    latitude = position.latitude;
    if (latitude != null && longitude != null) {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude!, longitude!);
      locationAddress = placemarks.first;
    }
    setState(() {
      loading = false;
    });
  }

  @override
  void initState() {
    super.initState();

    if (AddressFormMode.add == widget.mode) {
      type = "Home";
      name = userController.userdata.value?.name;
      phone = userController.userdata.value?.mobile;
      getCurrentLatLong();
    } else if (AddressFormMode.edit == widget.mode && widget.address != null) {
      name = widget.address?.name ?? userController.userdata.value?.name;
      phone = widget.address?.phone ?? userController.userdata.value?.mobile;
      type = widget.address!.type;
      line1 = widget.address!.line1;
      line2 = widget.address!.line2;
      landmark = widget.address!.landmark;
      state = widget.address!.state;
      city = widget.address!.city;
      pincode = widget.address!.pincode;
      longitude = widget.address!.geoLocation?.coordinates?[0];
      latitude = widget.address!.geoLocation?.coordinates?[1];
      if (latitude != null && longitude != null) {
        placemarkFromCoordinates(latitude!, longitude!).then(
          (placemarks) {
            setState(() {
              locationAddress = placemarks.first;
            });
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RapsapAppBar(
        "${AddressFormMode.edit == widget.mode ? 'Edit' : 'Enter'} Address Details",
      ),
      body: ListView(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        children: [
          Visibility(
            visible: latitude != null && longitude != null && !loading,
            replacement: Shimmer.fromColors(
              baseColor: Colors.grey[200]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xffeaeaea)),
                  color: Colors.grey,
                ),
                height: 70,
              ),
            ),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xffeaeaea)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Your Current Location',
                    style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                        letterSpacing: -0.015),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/svg/active.svg',
                        colorFilter: ColorFilter.mode(
                            Theme.of(context).primaryColor, BlendMode.srcIn),
                      ),
                      const SizedBox(width: 5),
                      Flexible(
                        child: Text(
                          [
                            "${locationAddress?.name}",
                            "${locationAddress?.subLocality}",
                            '${locationAddress?.thoroughfare}',
                            "${locationAddress?.administrativeArea}",
                            "${locationAddress?.postalCode}"
                          ].join(', '),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.dmSans(
                              fontSize: 14, letterSpacing: -0.01, height: 1.2),
                        ),
                      ),
                      const SizedBox(width: 5),
                      InkWell(
                        onTap: () async {
                          final result = await Get.to<(LatLng, Placemark?)>(
                              () => SelectLocationScreen(
                                    latLng: LatLng(latitude!, longitude!),
                                  ));
                          log(result.toString());
                          if (result != null) {
                            latitude = result.$1.latitude;
                            longitude = result.$1.longitude;
                            if (result.$2 != null) {
                              locationAddress = result.$2;
                            }
                            setState(() {});
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: AppColors.secondaryBtnBorderColor),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Change',
                            style: TextStyle(
                              color: AppColors.secondaryBtnForegroundColor,
                              fontSize: 14,
                              height: 16.94 / 14,
                              letterSpacing: -1,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Divider(
            height: 1,
            thickness: 1,
            color: Color(0xffeaeaea),
          ),
          const SizedBox(height: 16),
          Form(
            key: _addressFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                InputBox(
                  initial: line1 ?? '',
                  lebel: 'Home details',
                  hint: 'Enter House No, Building Name',
                  keyboardType: TextInputType.streetAddress,
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {line1 = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: line2 ?? '',
                  lebel: 'Street, Area Details',
                  hint: 'Road Name. Area, Colony.',
                  keyboardType: TextInputType.streetAddress,
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {line2 = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: landmark ?? '',
                  lebel: 'Landmark',
                  hint: 'Enter LandMark',
                  keyboardType: TextInputType.text,
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {landmark = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: city ?? '',
                  lebel: 'City',
                  hint: 'Enter City',
                  keyboardType: TextInputType.text,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(30),
                  ],
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {city = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: state ?? '',
                  lebel: 'State',
                  hint: 'Enter state',
                  keyboardType: TextInputType.text,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(30),
                  ],
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {state = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: pincode ?? '',
                  lebel: 'Pincode',
                  hint: 'Enter Pincode',
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(6),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  validator: (value) {
                    return value?.isEmpty ?? false
                        ? "required"
                        : (value?.length ?? 0) < 6
                            ? "invalid pincode"
                            : null;
                  },
                  onChanged: (value) => {pincode = value},
                  onSaved: (value) => {pincode = value},
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Save as",
                            style: TextStyle(
                              height: 21 / 14,
                              letterSpacing: -0.015,
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: 10),
                          GroupButton(
                            controller: gcontroller,
                            isRadio: true,
                            options: GroupButtonOptions(
                              spacing: 7,
                              // buttonWidth: 55,
                              buttonHeight: 35,
                              direction: Axis.horizontal,
                              selectedTextStyle: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 11,
                                color: Colors.black,
                                height: 21 / 11,
                                letterSpacing: -0.015,
                              ),
                              unselectedTextStyle: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 11,
                                color: Colors.black,
                                height: 21 / 11,
                                letterSpacing: -0.015,
                              ),
                              selectedColor: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.2),
                              unselectedColor: Colors.white,
                              selectedBorderColor: Colors.transparent,
                              unselectedBorderColor: AppColors.kgrey,
                              textPadding: const EdgeInsets.all(16 / 2),
                              borderRadius: BorderRadius.circular(16),
                              selectedShadow: <BoxShadow>[
                                const BoxShadow(color: Colors.transparent)
                              ],
                              unselectedShadow: <BoxShadow>[
                                const BoxShadow(color: Colors.transparent)
                              ],
                            ),
                            onSelected: (val, index, isSelected) {
                              switch (index) {
                                case 0:
                                  type = "Home";
                                  break;
                                case 1:
                                  type = "Office";
                                  break;
                                case 2:
                                  type = "Family & Friends";
                                  break;
                                default:
                                  type = type ?? "Home";
                                  break;
                              }
                            },
                            buttons: const [
                              "Home",
                              "Office",
                              "Family & Friends"
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                InputBox(
                  initial: name ?? '',
                  lebel: 'Name',
                  hint: 'Enter Name',
                  keyboardType: TextInputType.name,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(40),
                  ],
                  validator: (value) {
                    return value?.isEmpty ?? false ? "required" : null;
                  },
                  onSaved: (value) => {name = value},
                ),
                const SizedBox(height: 8),
                InputBox(
                  initial: phone ?? '',
                  lebel: 'Phone',
                  hint: 'Enter Phone Number',
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  validator: (value) {
                    return value?.isEmpty ?? false
                        ? "required"
                        : validateMobile(value!);
                  },
                  onSaved: (value) => {phone = value},
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          GetBuilder<AccountController>(
            builder: (_) => SizedBox(
              width: double.infinity,
              child: SubmitButton(
                onpressed: () async {
                  checkServicableFn();
                },
                loading: accountController.loading.value,
                text: "Save Address",
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> checkServicableFn() async {
    FocusScope.of(context).unfocus();
    if (_addressFormKey.currentState?.validate() ?? false) {
      _addressFormKey.currentState?.save(); //onSaved is called!
      accountController
        ..loading.value = true
        ..update();

      if (await userController
          .getNearbyStore([longitude ?? 0.0, latitude ?? 0.0])) {
        if (AddressFormMode.add == widget.mode) {
          await accountController
              .createAddress(
            name: name,
            phone: phone,
            type: type,
            line1: line1,
            line2: line2,
            landmark: landmark,
            state: state,
            city: city,
            pincode: pincode,
            latitude: latitude,
            longitude: longitude,
          )
              .then(
            (value) {
              Get.find<CartController>().update(['total']);
            },
          );
        } else if (AddressFormMode.edit == widget.mode) {
          await accountController.createAddress(
            id: widget.address?.id,
            name: name,
            phone: phone,
            type: type,
            line1: line1,
            line2: line2,
            landmark: landmark,
            state: state,
            city: city,
            pincode: pincode,
            latitude: latitude,
            longitude: longitude,
          );
        }
      }

      accountController.loading.value = false;
      accountController.update();
    }
  }
}

String? validateMobile(String value) {
  String patttern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
  RegExp regExp = RegExp(patttern);
  if (value.isEmpty || value.length > 10) {
    return 'Please enter mobile number';
  } else if (!regExp.hasMatch(value)) {
    return 'Please enter valid mobile number';
  }
  return null;
}
