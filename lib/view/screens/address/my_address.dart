import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shimmer/shimmer.dart';

import '../../../model/model.dart';
import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';
import '../../widgets/decorated_card.dart';
import '../../../controllers/account_controller.dart';
import '../../../controllers/user_controller.dart';
import 'address_form_screen.dart';

Address? selectedAddress;

class MyAddressScreen extends StatefulWidget {
  const MyAddressScreen({super.key});

  @override
  State<MyAddressScreen> createState() => _MyAddressScreenState();
}

class _MyAddressScreenState extends State<MyAddressScreen> {
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.find<AccountController>();

  @override
  void initState() {
    accountController.getaddress();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RapsapAppBar("My Addresses"),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                shrinkWrap: true,
                children: [
                  Obx(
                    () => Visibility(
                      visible: accountController
                              .addressModel.value.data?.isNotEmpty ??
                          false,
                      child: Text(
                        "Saved Addresses",
                        style: GoogleFonts.inter(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          height: 18.2 / 14,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Obx(
                    () => Visibility(
                      visible: !accountController.loading.value,
                      replacement: Shimmer.fromColors(
                        baseColor: Colors.grey[200]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          height: 80,
                          margin: const EdgeInsets.symmetric(horizontal: 24),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: AppColors.kDecoratedCardBorder),
                            borderRadius: BorderRadius.circular(10),
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      child: Visibility(
                        visible: accountController
                                .addressModel.value.data?.isNotEmpty ??
                            false,
                        replacement: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Image.asset(
                                  "assets/images/no_address_illustration.webp"),
                            ),
                            const SizedBox(height: 16 / 2),
                            Text(
                              "No Address details added",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                height: 18.2 / 14,
                                color: Colors.black.withOpacity(0.6),
                              ),
                            )
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: accountController.addressModel.value.data
                                  ?.map(
                                    (address) =>
                                        AddressItemCard(address: address),
                                  )
                                  .toList() ??
                              [],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // add address CTA
            SizedBox(
              height: 56,
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Get.to(
                          () => const AddressFormScreen(
                            mode: AddressFormMode.add,
                          ),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).primaryColor,
                        side: BorderSide(color: Theme.of(context).primaryColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(2),
                        ),
                        textStyle: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          height: 20.8 / 16,
                        ),
                      ),
                      child: const Text("Add New Address"),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddressItemCard extends StatelessWidget {
  const AddressItemCard({
    super.key,
    required this.address,
  });
  final Address address;
  @override
  Widget build(BuildContext context) {
    return DecoratedCard(
      margin: const EdgeInsets.symmetric(vertical: 16 / 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16 / 2,
            ),
            decoration: BoxDecoration(
              color: AppColors.kHomeAppBarBG,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              address.type ?? "---",
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                height: 21 / 14,
                letterSpacing: -0.015,
              ),
            ),
          ),
          const SizedBox(height: 16 / 2),
          Text(
            "${address.line1}, ${address.line2}, ${address.landmark?.capitalize}, ${address.city?.capitalize}, ${address.state?.capitalize}, ${address.pincode} ${address.phone}",
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 18.2 / 14,
              color: AppColors.kgreyDark,
            ),
          ),
          const SizedBox(height: 16 / 2),
          Row(
            children: [
              InkWell(
                onTap: () {
                  Get.to(
                    () => AddressFormScreen(
                      mode: AddressFormMode.edit,
                      address: address,
                    ),
                  );
                },
                child: Text(
                  "Edit",
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: () {
                  Get.dialog(
                    Center(
                      child: Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16 * 3,
                        ),
                        child: Material(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          child: GetBuilder<AccountController>(
                            builder: (controller) {
                              return Padding(
                                padding: const EdgeInsets.all(24),
                                child: Visibility(
                                  visible: !controller.loading.value,
                                  replacement: Center(
                                    child: LoadingIndicator(
                                      indicatorType: Indicator.cubeTransition,
                                      colors: [
                                        Theme.of(context).primaryColor,
                                        Colors.black,
                                      ],
                                      backgroundColor: Colors.white,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Text(
                                        "Delete Address?",
                                        style: TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontSize: 18,
                                          height: 23.44 / 18,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(height: 16 / 2),
                                      Text(
                                        "Are you sure you want to delete this address?",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                          height: 18.23 / 14,
                                          color: AppColors.kgreyDark,
                                        ),
                                      ),
                                      const SizedBox(height: 16 * 2),
                                      SizedBox(
                                        height: 45,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            Expanded(
                                              child: OutlinedButton(
                                                onPressed: () {
                                                  Get.back();
                                                },
                                                style: OutlinedButton.styleFrom(
                                                  foregroundColor: Colors.black,
                                                  side: const BorderSide(
                                                      color: Colors.black),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                ),
                                                child: const Text('Cancel'),
                                              ),
                                            ),
                                            const SizedBox(width: 24),
                                            Expanded(
                                              child: ElevatedButton(
                                                onPressed: () async {
                                                  if (address.id != null) {
                                                    await controller
                                                        .deleteAddress(
                                                            address.id!)
                                                        .then(
                                                            (_) => Get.close(1))
                                                        .onError((error,
                                                                stackTrace) =>
                                                            null);
                                                  }
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  foregroundColor: Colors.white,
                                                  backgroundColor: Colors.red,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                ),
                                                child: const Text('Delete'),
                                              ),
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text(
                  "Delete",
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
