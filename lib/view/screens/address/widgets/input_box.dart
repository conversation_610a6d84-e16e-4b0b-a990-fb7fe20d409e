import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InputBox extends StatelessWidget {
  final String hint;
  final String lebel;
  final String initial;
  final ValueSetter onSaved;
  final ValueSetter? onChanged;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final List<TextInputFormatter>? inputFormatters;

  const InputBox({
    super.key,
    required this.hint,
    required this.lebel,
    this.onChanged,
    required this.onSaved,
    required this.initial,
    required this.validator,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.none,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          lebel,
          style: const TextStyle(
            height: 21 / 14,
            letterSpacing: -0.015,
            color: Colors.black,
            fontSize: 14,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          initialValue: initial,
          maxLines: 1,
          keyboardType: keyboardType,
          textCapitalization: textCapitalization,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          decoration: InputDecoration(
            isDense: true,
            focusedBorder: const OutlineInputBorder(
              borderSide: BorderSide(color: Colors.black),
              borderRadius: BorderRadius.zero,
            ),
            enabledBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xffeaeaea)),
                borderRadius: BorderRadius.zero),
            border: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xffeaeaea)),
                borderRadius: BorderRadius.zero),
            errorBorder: const OutlineInputBorder(
              borderSide: BorderSide(color: Colors.redAccent),
              borderRadius: BorderRadius.zero,
            ),
            hintText: hint,
          ),
          onSaved: onSaved,
          validator: validator,
        ),
      ],
    );
  }
}
