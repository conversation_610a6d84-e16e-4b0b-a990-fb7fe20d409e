// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../controllers/account_controller.dart';
import '../../../main.dart';
import '../../../model/address_model.dart';
import '../../widgets/decorated_card.dart';
import '../../../controllers/user_controller.dart';
import 'address_form_screen.dart';

class AddressSelectionWidget extends StatelessWidget {
  AddressSelectionWidget({super.key});
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.put(AccountController());

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 2 * 16,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Select Address',
              textAlign: TextAlign.start,
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w700,
                fontSize: 24,
              ),
            ),
            const SizedBox(height: 18),
            InkWell(
              splashColor: Colors.black,
              onTap: () {
                Get.to(
                  () => const AddressFormScreen(mode: AddressFormMode.add),
                );
              },
              borderRadius: BorderRadius.circular(10),
              child: DecoratedCard(
                child: Text(
                  "Add New Address",
                  style: GoogleFonts.inter(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              "Saved Address",
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 6),
            Expanded(
              child: GetBuilder<AccountController>(
                builder: (controller) {
                  return Visibility(
                    visible:
                        accountController.addressModel.value.data?.isNotEmpty ??
                            false,
                    replacement: const Text("No Addresses found"),
                    child: ListView(
                      shrinkWrap: true,
                      children: accountController.addressModel.value.data
                              ?.map(
                                (e) => DecoratedCard(
                                  padding: EdgeInsets.zero,
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 6),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () async {
                                        accountController
                                            .selectedAddress.value = e;
                                        await storage.write(
                                            'selectedAddressId', e.id);
                                        await storage.write('coordinates',
                                            e.geoLocation?.coordinates);

                                        Get.back<Address>(result: e);
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                          16,
                                          16,
                                          16 / 2,
                                          16,
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              flex: 4,
                                              child: Text(
                                                "${e.line1}, ${e.line2}, ${e.city}, ${e.state}, ${e.pincode}",
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14,
                                                  color: Color(0xff515151),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 16,
                                                vertical: 16 / 2,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Theme.of(context)
                                                    .primaryColor
                                                    .withOpacity(0.2),
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                              ),
                                              child: Text(
                                                "${e.type}",
                                                textAlign: TextAlign.center,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14,
                                                  height: 21 / 14,
                                                  letterSpacing: -0.015,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
