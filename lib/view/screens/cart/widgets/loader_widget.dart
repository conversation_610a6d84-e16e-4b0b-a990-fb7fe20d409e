import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';

class LoaderWidget extends StatelessWidget {
  const LoaderWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20,
      width: 40,
      alignment: Alignment.center,
      child: LoadingIndicator(
        colors: [
          Theme.of(context).primaryColor,
          Colors.black,
        ],
        indicatorType: Indicator.cubeTransition,
      ),
    );
  }
}
