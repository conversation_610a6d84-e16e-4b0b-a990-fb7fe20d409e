import 'package:flutter/material.dart';

class MiddleoutGradientDividerH extends StatelessWidget {
  final Color color;
  const MiddleoutGradientDividerH(
    this.color, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 1,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            colors: [
              color.withOpacity(0.05),
              color.withOpacity(0.4),
              color.withOpacity(0.05),
            ],
            stops: const [0, 0.5, 1],
          ),
        ),
      ),
    );
  }
}
