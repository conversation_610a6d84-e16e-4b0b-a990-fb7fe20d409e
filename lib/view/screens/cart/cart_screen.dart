import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/order_controller.dart';
import '../../../model/database_models/database_models.dart';
import '../../../utils/colors.dart';
import '../../widgets/animated_rising_card.dart';
import '../address/address_form_screen.dart';
import '../payment/payment_methods_screen.dart';
import '../tracking_order/tracking_order.dart';
import '../../widgets/decorated_card.dart';
import '../../../controllers/account_controller.dart';
import '../../../controllers/cart_controlller.dart';
import '../address/address_selection_widget.dart';
import '../login/widgets/button.dart';
import '../../../controllers/user_controller.dart';
import '../../../services/database_helper.dart';
import '../../widgets/rapsap_appbar.dart';
import '../login/login_screen.dart';
import 'widgets/loader_widget.dart';
import 'widgets/middleout_gradient_divider_horizontal.dart';
import 'apply_coupon_page.dart';
import 'empty_cart_widget.dart';
import 'price_row_widget.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());
  final CartController controller = Get.find<CartController>();
  final UserController userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    controller.getCartList().then((_) => controller.loadCart());
    accountController.getaddress();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: RapsapAppBar("My Cart"),
      body: Obx(
        () => Visibility(
          visible: controller.myCartItems.isNotEmpty,
          replacement: const EmptyCartWidget(),
          child: Stack(
            children: [
              _buildCartContent(context),
              _buildPlaceOrderWidget(context),
            ],
          ),
        ),
      ),
    );
  }

  Column _buildCartContent(BuildContext context) {
    return Column(
      children: [
        _buildDiscountPreview(),
        Expanded(
          child: ListView(
            children: [
              _buildCartItemList(),
              _buildMissingSuggestion(),
              const SizedBox(height: 16),
              _buildCouponWidget(context),
              const SizedBox(height: 16),
              _buildPriceDetailsWidget(),
              const SizedBox(height: 16),
              _buildCancellationPolicy(),
              const SizedBox(height: 16 * 2),
            ],
          ),
        ),
      ],
    );
  }

  Visibility _buildCancellationPolicy() {
    return Visibility(
      visible: controller.myCartItems.isNotEmpty,
      child: Padding(
        padding: EdgeInsets.only(bottom: Get.height * 0.15),
        child: DecoratedCard(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Cancellation policy",
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16 / 2),
              Text(
                "Orders cannot be cancelled once packed for delivery. Incase of unexpected delays, refund will be provided , if applicable",
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  color: AppColors.kgreyDark,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Visibility _buildPriceDetailsWidget() {
    return Visibility(
      visible: controller.myCartItems.isNotEmpty,
      child: DecoratedCard(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Price Details',
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
                ),
                const SizedBox(height: 20),
                FutureBuilder<double>(
                  future: DatabaseHelper.instance.getSubTotal(),
                  builder: (context, snapshot) {
                    return Obx(
                      () => PriceRowsWidget(
                        amount: '\u20b9${snapshot.data ?? ""}',
                        title:
                            'Price ( ${controller.myCartItems.length} ) Items',
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                StreamBuilder<List<LocalOrder>>(
                  stream: DatabaseHelper.instance.getDbOrder1().asStream(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData &&
                        (snapshot.data?.isNotEmpty ?? false)) {
                      LocalOrder order = snapshot.data![0];
                      return Column(
                        children: [
                          PriceRowsWidget(
                            title: 'Discount',
                            amount: ((order.discount ?? 0) * -1).toString(),
                            secondColor: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(height: 16),
                          GetBuilder<CartController>(
                            builder: (_) {
                              return PriceRowsWidget(
                                title: 'Coupon Discount',
                                amount:
                                    '-${controller.appliedCoupon?.offerAmount?.toDouble() ?? 0.0}',
                                secondColor: Theme.of(context).primaryColor,
                              );
                            },
                          ),
                          const SizedBox(height: 16),
                          PriceRowsWidget(
                            amount: 'Free',
                            title: 'Delivery fee',
                            secondColor: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(height: 16),
                          GetBuilder<CartController>(
                            builder: (_) {
                              final totalTax = controller.myCartItems.fold(
                                0.0,
                                (prev, e) => prev + (e.tax ?? 0.0),
                              );
                              return PriceRowsWidget(
                                amount: "\u20b9${totalTax.toPrecision(2)}",
                                title: 'Taxes',
                                secondColor: Theme.of(context).primaryColor,
                              );
                            },
                          ),
                          const SizedBox(height: 20),
                          MiddleoutGradientDividerH(AppColors.kgrey),
                          const SizedBox(height: 16 / 2),
                        ],
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  },
                ),

                // total amount
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Amount',
                      style: GoogleFonts.roboto(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    GetBuilder<CartController>(
                      builder: (_) {
                        return Text(
                          '\u20b9${controller.myCartItems.fold(0.0, (prev, e) {
                            return prev + ((e.price ?? 0.0) - (e.discount ?? 0.0) + (e.tax ?? 0.0)) * (e.qty?.toDouble() ?? 0.0);
                          }).toPrecision(2)}',
                          style: GoogleFonts.roboto(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  GetBuilder<CartController> _buildCouponWidget(BuildContext context) {
    return GetBuilder<CartController>(
      builder: (_) {
        return DecoratedCard(
          padding:
              controller.appliedCoupon == null
                  ? EdgeInsets.zero
                  : const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Material(
            color: Colors.transparent,
            child: Visibility(
              visible: controller.appliedCoupon == null,
              replacement: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SvgPicture.asset('assets/svg/promocode.svg', width: 14),
                      const SizedBox(width: 12),
                      Text(
                        'Applied Coupon',
                        style: GoogleFonts.dmSans(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 27),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(
                        width: 14,
                        child: Icon(
                          Icons.check_circle,
                          color: Color(0xff03A47B),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Flexible(
                        child: Text(
                          'You Saved Rs ${controller.appliedCoupon?.offerAmount} more on this order with this coupon',
                          style: GoogleFonts.dmSans(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: const Color(0xff03A47B),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DottedBorder(
                        padding: EdgeInsets.zero,
                        color: Theme.of(context).primaryColor,
                        strokeWidth: 1,
                        borderType: BorderType.Rect,
                        child: Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColor.withOpacity(0.2),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            controller.appliedCoupon?.code?.toUpperCase() ??
                                '---',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              height: 17.6 / 16,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          controller
                            ..appliedCoupon = null
                            ..update();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                        child: const Text('Remove'),
                      ),
                    ],
                  ),
                ],
              ),
              child: ListTile(
                title: Text(
                  'Apply Coupon',
                  style: GoogleFonts.dmSans(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                minTileHeight: 40,
                leading: SvgPicture.asset(
                  'assets/svg/promocode.svg',
                  width: 14,
                ),
                trailing: SvgPicture.asset('assets/svg/forwardangle.svg'),
                minVerticalPadding: 0,
                contentPadding: const EdgeInsets.all(16),
                dense: true,
                horizontalTitleGap: 12,
                minLeadingWidth: 0,
                onTap: () {
                  Get.to(() => const ApplyCouponsScreen());
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Visibility _buildCartItemList() {
    return Visibility(
      visible: !controller.loading.value,
      replacement: Center(child: _cartCardShimmer().paddingAll(16)),
      child: Visibility(
        visible: controller.myCartItems.isNotEmpty,
        child: ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: controller.myCartItems.length,
          itemBuilder: (context, index) {
            return cartItemCard(index);
          },
          separatorBuilder:
              (BuildContext context, int index) =>
                  MiddleoutGradientDividerH(AppColors.kgrey),
        ),
      ),
    );
  }

  Visibility _buildPlaceOrderWidget(BuildContext context) {
    return Visibility(
      visible: controller.myCartItems.isNotEmpty,
      child: GetBuilder<CartController>(
        id: 'total',
        builder: (_) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: GetBuilder<AccountController>(
              builder: (_) {
                return Visibility(
                  visible:
                      accountController.addressModel.value.data?.isNotEmpty ??
                      false,
                  replacement: addAddressButton(),
                  child: Visibility(
                    visible: accountController.selectedAddress.value != null,
                    replacement: selectAddressButton(context),
                    child: placeOrderWidget(context),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Visibility _buildMissingSuggestion() {
    return Visibility(
      visible: controller.myCartItems.isNotEmpty,
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "Missed  an item?",
              style: TextStyle(
                letterSpacing: -0.04,
                fontWeight: FontWeight.w400,
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: () => Get.back(),
              child: Text(
                "Continue Shopping",
                style: TextStyle(
                  letterSpacing: -0.04,
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container placeOrderWidget(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 14,
            spreadRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible:
                !(controller.myCartItems.isEmpty ||
                    accountController.selectedAddress.value == null),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                SvgPicture.asset("assets/svg/location_cart.svg"),
                const SizedBox(width: 12),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Deliver to",
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: AppColors.kgreyDark,
                          height: 18.23 / 14,
                        ),
                      ),
                      Obx(
                        () => Text(
                          "${accountController.selectedAddress.value?.line1}, ${accountController.selectedAddress.value?.line2}, ${accountController.selectedAddress.value?.landmark}, ${accountController.selectedAddress.value?.city}, ${accountController.selectedAddress.value?.state}, ${accountController.selectedAddress.value?.pincode}, ${accountController.selectedAddress.value?.phone}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: Colors.black,
                            height: 18.23 / 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton(
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => AddressSelectionWidget(),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.secondaryBtnForegroundColor,
                    elevation: 0,
                    side: BorderSide(color: AppColors.secondaryBtnBorderColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.zero,
                    textStyle: TextStyle(
                      color: AppColors.secondaryBtnForegroundColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      height: 18 / 14,
                    ),
                  ),
                  child: const Text("Change"),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Flexible(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    Get.to(() => const PaymentMethodsScreen());
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "Pay using",
                            style: TextStyle(
                              color: AppColors.kgreyDark,
                              fontSize: 14,
                              height: 18.23 / 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: AppColors.kgreyDark,
                          ),
                        ],
                      ),
                      const Text(
                        "Google Pay UPI",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          height: 18.23 / 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16 * 2.5),
                Expanded(
                  child: GetBuilder<CartController>(
                    builder: (_) {
                      return payNowButton();
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      // width: double.infinity,
    );
  }

  Widget selectAddressButton(BuildContext context) {
    return SubmitButton(
      textsize: 80,
      bgcolor: AppColors.ctaBackgroundColor,
      text: 'Add Address Details',
      onpressed: () {
        // Get.bottomSheet(
        //   AddressSelectionWidget(),
        // );
        showModalBottomSheet(
          context: context,
          builder: (context) => AddressSelectionWidget(),
          isScrollControlled: true,
          useSafeArea: true,
        );
        // Get.bottomSheet(
        //   const AddressAddEditPage(
        //       mode: AddEditAddress.add),
        //   // isScrollControlled: true,
        //   ignoreSafeArea: false,
        // );
      },
    ).paddingSymmetric(horizontal: 16, vertical: 8).paddingOnly(bottom: 15);
  }

  AnimatedRisingCard loginButton() {
    return AnimatedRisingCard(
      // delay: 200,
      height: 80,
      // delay: 200,
      child: SubmitButton(
        textsize: 16,
        bgcolor: Theme.of(context).primaryColor,
        text: 'Login to Continue',
        onpressed: () {
          Get.to(() => const LoginScreen(), fullscreenDialog: true);
        },
        padding: const EdgeInsets.all(16),
      ),
    );
  }

  AnimatedRisingCard addAddressButton() {
    return AnimatedRisingCard(
      height: 80,
      child: SubmitButton(
        textsize: 16,
        bgcolor: AppColors.ctaBackgroundColor,
        text: 'Add Address Details',
        onpressed: () {
          Get.to(() => const AddressFormScreen(mode: AddressFormMode.add));
        },
      ).paddingSymmetric(horizontal: 16, vertical: 8).paddingOnly(bottom: 15),
    );
  }

  Widget payNowButton() {
    return Visibility(
      visible: !controller.buttonloading,
      replacement: const LoaderWidget(),
      child: MaterialButton(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16 / 2),
        textColor: Colors.white,
        color: AppColors.ctaBackgroundColor,
        onPressed: () async {
          HapticFeedback.vibrate();
          createOrderNavigate();
        },
        child: Row(
          // mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GetBuilder<CartController>(
                    builder: (_) {
                      return Text(
                        '\u20b9${controller.myCartItems.fold<double>(0.0, (prev, e) {
                          return prev + ((e.price ?? 0.0) - (e.discount ?? 0.0) + (e.tax ?? 0.0)) * (e.qty?.toDouble() ?? 0.0);
                        }).toPrecision(2)}',
                        maxLines: 1,
                        style: GoogleFonts.inter(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 5),
                  Text(
                    'Total Amount',
                    maxLines: 1,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      height: 13.02 / 10,
                      letterSpacing: -0.02,
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(width: 16 * 2),
            const Flexible(
              child: FittedBox(
                fit: BoxFit.fitWidth,
                child: Text(
                  "Place Order",
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 18,
                    height: 23.44 / 18,
                    letterSpacing: -0.02,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountPreview() {
    return Visibility(
      visible: controller.myCartItems.isNotEmpty,
      child: StreamBuilder<List<LocalOrder>>(
        stream: DatabaseHelper.instance.getDbOrder1().asStream(),
        builder: (context, snapshot) {
          return Visibility(
            visible:
                !((snapshot.data?.first.discount ?? 0.0) == 0.0 ||
                    snapshot.data?.first.discount == null),
            child: Container(
              color: const Color(0xffE1E8FF),
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 26),
              child: Text(
                'You will save \u20b9 ${snapshot.data?.first.discount ?? ""} on this order',
                maxLines: 1,
                style: GoogleFonts.roboto(
                  color: Theme.of(context).primaryColor,
                  fontSize: 20,
                  letterSpacing: -0.4,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void createOrderNavigate() async {
    // List<LocalOrder> odrList = await DatabaseHelper.instance.getDbOrder();
    // LocalOrder order = odrList[0];
    // if (getGrandTotal(order) <
    //     double.parse(configModel.minOrderValue?.toString() ?? "0.0")) {
    //   // EasyLoading.dismiss();
    //   controller
    //     ..buttonloading = false
    //     ..update();
    //   Utils.customToast(
    //     backgroundColor: Theme.of(context).primaryColor,
    //     gravity: ToastGravity.TOP,
    //     message:
    //         'Min. order amount should be greater than \u20b9${configModel.minOrderValue.toString()}',
    //   );
    // } else {
    List<LocalShoppingCart> itemlist =
        await DatabaseHelper.instance.getGroceries();
    OrderController orderController = Get.find<OrderController>();
    await controller.loadCart();
    if (controller.notInStockItems.isEmpty) {
      controller
        ..buttonloading = true
        ..update();
      try {
        final result = await orderController.createOrder(itemlist: itemlist);
        if (result != null) {
          await controller.clearCart();
          Get
            ..back()
            // TODO: Temporarily adding track order screen instead of payment screen
            ..to(() => TrackOrderScreen(orderId: result));
        }
      } catch (e, st) {
        log('Error: $e,\nStackTrace: $st');
      } finally {
        controller
          ..buttonloading = false
          ..update();
      }
    }
  }

  Container cartItemCard(index) {
    // ignore: unused_local_variable

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: index == 0,
            child: const Text(
              'Review Products',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 20,
                color: Colors.black,
              ),
            ).paddingSymmetric(vertical: 20),
          ),
          GetBuilder<CartController>(
            builder: (_) {
              bool isNotInStock = controller.notInStockItems.any(
                (element) =>
                    element.itemId == controller.myCartItems[index].productID,
              );
              final quantity = controller.myCartItems[index].qty ?? 0;
              final availableStock =
                  controller.notInStockItems
                      .firstWhereOrNull(
                        (element) =>
                            element.itemId ==
                            controller.myCartItems[index].productID,
                      )
                      ?.availableStock ??
                  (controller.myCartItems[index].availableStock ?? 0);
              bool canBeIncremented = quantity < availableStock;
              return Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 6),
                foregroundDecoration:
                    !isNotInStock
                        ? null
                        : const BoxDecoration(
                          color: Colors.grey,
                          backgroundBlendMode: BlendMode.saturation,
                        ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Visibility(
                      visible: isNotInStock,
                      child: Text(
                        'This item is no longer available',
                        style: GoogleFonts.dmSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        CachedNetworkImage(
                          imageUrl:
                              controller.myCartItems[index].imageURL ?? "",
                          errorListener: (value) {
                            debugPrint(value.toString());
                          },
                          errorWidget:
                              (context, url, error) =>
                                  Image.asset("assets/images/error-image.webp"),
                          placeholder:
                              (context, url) =>
                                  CircularProgressIndicator.adaptive(
                                    valueColor: AlwaysStoppedAnimation(
                                      Theme.of(context).primaryColor,
                                    ),
                                  ),
                          fit: BoxFit.cover,
                          height: 60,
                          width: 60,
                        ),
                        const SizedBox(width: 10),

                        // name and weight
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                controller
                                        .myCartItems[index]
                                        .name
                                        ?.capitalize ??
                                    "---",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black,
                                  height: 18 / 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              // TODO: add weight as string to LocalShoppingCart then implement below
                              // Row(
                              //   children: [
                              //     Text(
                              //       controller.myCartItems[index].weight
                              //           .toString(),
                              //       style: TextStyle(
                              //         fontWeight: FontWeight.w400,
                              //         fontSize: 14,
                              //         letterSpacing: -0.1,
                              //         color: AppColors.kblack.withOpacity(0.5),
                              //       ),
                              //     ),
                              //     // SizedBox(width: 5),
                              //     // discountVal > 0
                              //     //     ? Container(
                              //     //         decoration: BoxDecoration(
                              //     //           color:
                              //     //               const Color(0xffFF5454),
                              //     //           borderRadius:
                              //     //               BorderRadius.circular(2),
                              //     //         ),
                              //     //         child: Text(
                              //     //           "${discountVal.round()}% OFF",
                              //     //           style: const TextStyle(
                              //     //             color: Colors.white,
                              //     //             fontSize: 10,
                              //     //             fontWeight: FontWeight.w700,
                              //     //             letterSpacing: -0.5,
                              //     //           ),
                              //     //           textAlign: TextAlign.center,
                              //     //         ).paddingAll(3),
                              //     //       )
                              //     //     : const SizedBox.shrink(),
                              //   ],
                              // ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 5),

                        // add remove button
                        Container(
                          height: 36,
                          width: 84,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: Theme.of(context).primaryColor,
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Flexible(
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () async {
                                    if (controller.myCartItems[index].qty ==
                                        1) {
                                      await DatabaseHelper.instance.remove(
                                        controller.myCartItems[index],
                                      );
                                      controller.getCartList();
                                    } else {
                                      await DatabaseHelper.instance.decreseQty(
                                        controller.myCartItems[index],
                                      );
                                      controller.getCartList();
                                    }
                                  },
                                  icon: Icon(
                                    Icons.remove_outlined,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              Text(
                                controller.myCartItems[index].qty.toString(),
                                maxLines: 1,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              Flexible(
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  onPressed:
                                      !canBeIncremented
                                          ? null
                                          : () async {
                                            await DatabaseHelper.instance
                                                .increseQty(
                                                  controller.myCartItems[index],
                                                );
                                            controller.getCartList();
                                          },
                                  icon: Icon(
                                    Icons.add,
                                    color:
                                        !canBeIncremented
                                            ? AppColors.kgrey
                                            : Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),

                        // price
                        SizedBox(
                          width: 45,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '\u20b9${((controller.myCartItems[index].price ?? 0.0) * (controller.myCartItems[index].qty?.toDouble() ?? 0)).round()}',
                                style: GoogleFonts.inter(
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: -0.1,
                                  color: Colors.black,
                                ),
                              ),
                              Visibility(
                                visible:
                                    !(controller.myCartItems[index].mrp ==
                                            0.00 ||
                                        controller.myCartItems[index].mrp ==
                                            null ||
                                        controller.myCartItems[index].mrp ==
                                            controller
                                                .myCartItems[index]
                                                .price),
                                child: Text(
                                  '\u20b9${((controller.myCartItems[index].mrp ?? 0) * (controller.myCartItems[index].qty ?? 0)).round()}',
                                  style: GoogleFonts.inter(
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black.withOpacity(0.5),
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Future clearCoupon() async {
    await DatabaseHelper.instance.setDiscountValue(0, 0, 0, '');
    await DatabaseHelper.instance.removeFreeItem(); // to remove free item
    controller.getCartList();
  }
}

// class SubscribtionCartCard extends StatelessWidget {
//   const SubscribtionCartCard({
//     super.key,
//   });
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(
//         horizontal: 16,
//         vertical: 6,
//       ),
//       child: SizedBox(
//         child: Card(
//           elevation: 8,
//           shadowColor: AppColors.kblack.withOpacity(0.15),
//           child: Container(
//             color: Colors.white,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   color: AppColors.kblack.withOpacity(0.06),
//                   child: Row(
//                     children: [
//                       Text(
//                         'Subscription',
//                         style: TextStyle(
//                           fontWeight: FontWeight.w500,
//                           color: AppColors.kblack.withOpacity(0.6),
//                         ),
//                       ).paddingSymmetric(horizontal: 16, vertical: 8)
//                     ],
//                   ),
//                 ),
//                 SizedBox(
//                   height: 150,
//                   child: Stack(
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           SizedBox(
//                             height: 47,
//                             child: Stack(
//                               children: [
//                                 SvgPicture.asset(
//                                   'assets/svg/triangle_offer.svg',
//                                   colorFilter: const ColorFilter.mode(
//                                       Colors.red, BlendMode.srcIn),
//                                   height: 47,
//                                 ),
//                                 Positioned(
//                                   bottom: 20,
//                                   right: 4,
//                                   left: 5,
//                                   child: Transform.rotate(
//                                     angle: 5.54,
//                                     child: const Text(
//                                       '30% OFF',
//                                       style: TextStyle(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.w700,
//                                         fontSize: 10,
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           IconButton(
//                             onPressed: () {},
//                             icon: const Icon(
//                               Icons.close,
//                               color: kgrey,
//                               size: 30,
//                               semanticLabel: 'Remove from Cart',
//                             ),
//                           )
//                         ],
//                       ),
//                       Padding(
//                         padding: const EdgeInsets.only(left: 16, right: 16),
//                         child: Row(
//                           mainAxisSize: MainAxisSize.max,
//                           children: [
//                             Expanded(
//                               flex: 2,
//                               child: SizedBox(
//                                 child: Image.asset(
//                                   'assets/images/milk.webp',
//                                 ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 5,
//                               child: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   const Text(
//                                     'Cow Milk',
//                                     style: TextStyle(
//                                       fontSize: 18,
//                                       fontWeight: FontWeight.w700,
//                                     ),
//                                   ),
//                                   const SizedBox(height: 5),
//                                   Row(
//                                     children: [
//                                       Text(
//                                         '1L',
//                                         style: TextStyle(
//                                           fontWeight: FontWeight.w400,
//                                           color: AppColors.kblack.withOpacity(0.5),
//                                         ),
//                                       ),
//                                       SizedBox(width: 10),
//                                       Icon(
//                                         Icons.circle,
//                                         size: 5,
//                                         color: AppColors.kblack.withOpacity(0.5),
//                                       ),
//                                       SizedBox(width: 10),
//                                       Text(
//                                         '₹36',
//                                         style: TextStyle(
//                                           fontWeight: FontWeight.w400,
//                                           color: AppColors.kblack.withOpacity(0.5),
//                                         ),
//                                       ),
//                                       SizedBox(width: 10),
//                                       Text(
//                                         '₹50',
//                                         style: TextStyle(
//                                           decoration:
//                                               TextDecoration.lineThrough,
//                                           fontWeight: FontWeight.w400,
//                                           color: AppColors.kblack.withOpacity(0.5),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   const SizedBox(height: 16/2),
//                                   Row(
//                                     children: [
//                                       Expanded(
//                                         child: Text(
//                                           '3 days/week for next 2 months (06 July, 2021)',
//                                           style: TextStyle(
//                                             color: AppColors.kblack.withOpacity(0.5),
//                                           ),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       const Expanded(
//                                         flex: 4,
//                                         child: Row(
//                                           children: [
//                                             Text(
//                                               '₹364',
//                                               style: TextStyle(
//                                                 fontWeight: FontWeight.w700,
//                                                 fontSize: 18,
//                                               ),
//                                             ),
//                                             SizedBox(width: 10),
//                                             Text(
//                                               '₹520',
//                                               style: TextStyle(
//                                                 decoration:
//                                                     TextDecoration.lineThrough,
//                                               ),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                       Expanded(
//                                         flex: 3,
//                                         child: Container(
//                                           color: AppColors.kblack,
//                                           child: const Center(
//                                             child: Padding(
//                                               padding: EdgeInsets.symmetric(
//                                                 vertical: 6,
//                                                 horizontal: 8,
//                                               ),
//                                               child: Row(
//                                                 mainAxisAlignment:
//                                                     MainAxisAlignment
//                                                         .spaceBetween,
//                                                 children: [
//                                                   Icon(
//                                                     Icons.remove,
//                                                     color: Colors.white,
//                                                   ),
//                                                   Text(
//                                                     "1",
//                                                     style: TextStyle(
//                                                       fontSize: 18,
//                                                       fontWeight:
//                                                           FontWeight.w500,
//                                                       color: Colors.white,
//                                                     ),
//                                                   ),
//                                                   Icon(
//                                                     Icons.add,
//                                                     color: Colors.white,
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                           ),
//                                         ),
//                                       ),
//                                     ],
//                                   )
//                                 ],
//                               ),
//                             )
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   child: Divider(
//                     thickness: 1,
//                     height: 1,
//                     color: AppColors.kblack.withOpacity(0.06),
//                   ),
//                 ),
//                 Padding(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//                   child: Text(
//                     'Starting on 06 May, 2021',
//                     style: TextStyle(
//                       color: AppColors.kblack.withOpacity(0.7),
//                       fontWeight: FontWeight.w400,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

SizedBox _cartCardShimmer() {
  return SizedBox(
    child: Shimmer.fromColors(
      baseColor: Colors.grey[200]!,
      highlightColor: Colors.grey[100]!,
      child: Container(height: 130, width: double.infinity, color: Colors.grey),
    ),
  );
}
