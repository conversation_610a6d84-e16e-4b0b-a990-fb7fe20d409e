import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import '../../../utils/colors.dart';
import '../../widgets/decorated_card.dart';
import '../../widgets/rapsap_appbar.dart';
import '../map_screen/select_location_screen.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/user_controller.dart';
import '../../../model/coupon_list_model.dart';
class ApplyCouponsScreen extends StatefulWidget {
  const ApplyCouponsScreen({super.key});

  @override
  State<ApplyCouponsScreen> createState() => _ApplyCouponsScreenState();
}

class _ApplyCouponsScreenState extends State<ApplyCouponsScreen> {
  final TextEditingController couponTextCtrl = TextEditingController();

  final UserController userController = Get.find<UserController>();

  final CartController cartcontroller = Get.find<CartController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RapsapAppBar("My Coupons"),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 56,
                child: TextFormField(
                  maxLines: 1,
                  controller: couponTextCtrl,
                  decoration: InputDecoration(
                    fillColor: Colors.white,
                    filled: true,
                    hintText: 'Enter Coupon Code',
                    isDense: true,
                    suffixIcon: SizedBox(
                      height: 53,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.ctaBackgroundColor,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 0),
                        ),
                        onPressed: () {
                          validateCouponCode(couponTextCtrl.text, context);
                        },
                        child: const Text('Apply'),
                      ),
                    ),
                    hintStyle: TextStyle(
                      color: const Color(0xff556F80).withOpacity(0.7),
                    ),
                    suffixIconConstraints: const BoxConstraints(
                      minWidth: 90,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.ctaBackgroundColor.withOpacity(0.25),
                      ),
                      borderRadius: BorderRadius.zero,
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.ctaBackgroundColor.withOpacity(0.25),
                      ),
                      borderRadius: BorderRadius.zero,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.ctaBackgroundColor.withOpacity(0.25),
                      ),
                      borderRadius: BorderRadius.zero,
                    ),
                  ),
                  onChanged: (value) {},
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Available Coupon',
                style: GoogleFonts.dmSans(
                  color: Colors.black,
                  fontSize: 18,
                  height: 1,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              FutureBuilder<List<Coupon>>(
                future: cartcontroller.getOffers(),
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    snapshot.data!.sort(
                      (b, a) => a.to!.compareTo(b.to!),
                    );

                    return ListView.separated(
                        separatorBuilder: (context, index) =>
                            const SizedBox(height: 8),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: snapshot.data?.length ?? 0,
                        itemBuilder: (context, index) {
                          return DecoratedCard(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    DottedBorder(
                                      padding: EdgeInsets.zero,
                                      color: Theme.of(context).primaryColor,
                                      strokeWidth: 1,
                                      borderType: BorderType.Rect,
                                      child: Container(
                                        height: 48,
                                        decoration: BoxDecoration(
                                            color: AppColors.kHomeAppBarBG),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16),
                                        alignment: Alignment.center,
                                        child: Text(
                                          snapshot.data![index].code
                                                  ?.toUpperCase() ??
                                              '---',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            height: 17.6 / 16,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                    GetBuilder<CartController>(
                                      builder: (_) {
                                        final condition = cartcontroller
                                                    .appliedCoupon !=
                                                null ||
                                            cartcontroller.myCartItems
                                                    .fold<int>(
                                                        0,
                                                        (p, e) =>
                                                            p +
                                                            (e.price?.toInt() ??
                                                                0)) <
                                                (int.tryParse(snapshot
                                                        .data![index]
                                                        .minPurchaseAmount
                                                        .toString()) ??
                                                    0);
                                        return InkWell(
                                          onTap: condition
                                              ? null
                                              : () async {
                                                  Get.back();
                                                  cartcontroller
                                                    ..appliedCoupon =
                                                        snapshot.data?[index]
                                                    ..update();
                                                },
                                          child: Text(
                                            cartcontroller.appliedCoupon !=
                                                    snapshot.data?[index]
                                                ? 'APPLY'
                                                : 'APPLIED',
                                            style: GoogleFonts.inter(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 16,
                                              height: 20.8 / 16,
                                              color: condition
                                                  ? AppColors.kgrey
                                                  : AppColors
                                                      .ctaBackgroundColor,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  snapshot.data![index].description ?? '',
                                  style: const TextStyle(
                                    color: Color(0xFF556F80),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          );
                        });
                  } else {
                    return CustomAppShimmer(
                      child: Column(
                        children: List.generate(
                          5,
                          (index) => Column(
                            children: [
                              Container(
                                width: double.infinity,
                                height: 1,
                                color: AppColors.kgrey.withOpacity(0.5),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      DottedBorder(
                                        padding: EdgeInsets.zero,
                                        color: Colors.black,
                                        strokeWidth: 1,
                                        borderType: BorderType.RRect,
                                        child: Container(
                                          height: 40,
                                          width: 120,
                                          color:
                                              AppColors.kgrey.withOpacity(0.5),
                                        ),
                                      ),
                                      Text(
                                        'APPLY',
                                        style: GoogleFonts.inter(
                                          color: Theme.of(context).primaryColor,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 18,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 10),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.kgrey.withOpacity(0.5),
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                    height: 10,
                                    width: Get.width * 0.6,
                                  ),
                                ],
                              ).paddingSymmetric(horizontal: 24, vertical: 20),
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // void applyCoupon(BuildContext context, Coupon coupon) async {
  //   // Get.close(1);;

  //   // cartcontroller.coupontext = coupon.couponCode.toString();

  //   // couponTextCtrl.text = coupon.couponCode.toString();
  //   // cartcontroller.update();
  // }

  void validateCouponCode(couponText, context) async {
    // await validateApplyCoupon(couponText, context);
  }

  // Future validateApplyCoupon(couponText, context) async {
  //   var order = await DatabaseHelper.instance.getDbOrder2();
  //   var reqpayload = {
  //     'coupon_code': couponText,
  //     'sub_total': order.first.subTotal
  //   };
  //   var response = await OrderServices.vaidateCouponCode(reqpayload);
  //   if (response['success'] == false) {
  //     showDialog(
  //       // backgroundColor: Colors.transparent,
  //       context: context,
  //       builder: (context) => Dialog(
  //         child: Container(
  //           height: 250,
  //           color: Colors.white,
  //           padding: const EdgeInsets.all(20),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.spaceAround,
  //             crossAxisAlignment: CrossAxisAlignment.center,
  //             children: [
  //               const Icon(
  //                 Icons.warning_rounded,
  //                 size: 40,
  //                 color: Colors.amber,
  //               ),
  //               response['data']['min_amount'] != null &&
  //                       response['data']['min_amount'].length > 0
  //                   ? Text(response['msg'].toString(),
  //                       textAlign: TextAlign.center,
  //                       style: const TextStyle(fontSize: 20))
  //                   : const Text('Coupon is invalid',
  //                       textAlign: TextAlign.center,
  //                       style: TextStyle(fontSize: 20)),
  //               ElevatedButton(
  //                 onPressed: () {
  //                   Get.close(1);
  //                 },
  //                 child: const Text('OK'),
  //               )
  //             ],
  //           ),
  //         ),
  //       ),
  //     );
  //   } else {
  //     // if (response['data']['offer_type'] == 'variant') {
  //     //   showDialog(
  //     //     // backgroundColor: Colors.transparent,
  //     //     context: context,
  //     //     builder: (context) => Dialog(
  //     //       child: Container(
  //     //         height: 300,
  //     //         color: Colors.white,
  //     //         child: Column(
  //     //           mainAxisAlignment: MainAxisAlignment.center,
  //     //           children: [
  //     //             SizedBox(
  //     //               width: 120,
  //     //               child: CachedNetworkImage(
  //     //                  httpHeaders: {
  //     //   HttpHeaders.authorizationHeader:'bearer ${userController.userdata.value?.token}'
  //     // },
  //     //                 fit: BoxFit.scaleDown,
  //     //                 placeholder: (context, url) => const Center(
  //     //                     child: CircularProgressIndicator.adaptive()),
  //     //                 imageUrl:
  //     //                     'https://prod-junq.s3.ap-south-1.amazonaws.com/icons8-party-popper-100.png',
  //     //               ),
  //     //             ),
  //     //             const Text('Yay!'),
  //     //             const SizedBox(height: 5),
  //     //             Text(
  //     //               '${couponText.toString()} applied!',
  //     //               style: const TextStyle(
  //     //                 fontSize: 14,
  //     //                 fontWeight: FontWeight.bold,
  //     //               ),
  //     //             ),
  //     //             const SizedBox(height: 10),
  //     //             Text(
  //     //               response['data']['product_name'] ?? '',
  //     //               style: const TextStyle(
  //     //                 fontSize: 20,
  //     //                 fontWeight: FontWeight.bold,
  //     //               ),
  //     //             ),
  //     //             const SizedBox(height: 5),
  //     //             const Text('Product Free on this coupon code.'),
  //     //             const SizedBox(height: 5),
  //     //             ElevatedButton(
  //     //               onPressed: () async {
  //     //                 await acceptCoupon(response);
  //     //               },
  //     //               child: const Text('Continue'),
  //     //             ),
  //     //           ],
  //     //         ),
  //     //       ),
  //     //     ),
  //     //   );
  //     // } else {
  //     showDialog(
  //       // backgroundColor: Colors.transparent,
  //       context: context,
  //       builder: (context) => Dialog(
  //         child: Container(
  //           height: 300,
  //           color: Colors.white,
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               SizedBox(
  //                 width: 120,
  //                 child: CachedNetworkImage(
  //                   fit: BoxFit.scaleDown,
  //                   placeholder: (context, url) => const Center(
  //                       child: CircularProgressIndicator.adaptive()),
  //                   imageUrl:
  //                       'https://prod-junq.s3.ap-south-1.amazonaws.com/icons8-party-popper-100.png',
  //                   httpHeaders: {
  //                     HttpHeaders.authorizationHeader:
  //                         'bearer ${userController.userdata.value?.token}'
  //                   },
  //                 ),
  //               ),
  //               const Text('Yay!'),
  //               const SizedBox(height: 5),
  //               Text(
  //                 '${couponText.toString()} applied!',
  //                 style: const TextStyle(
  //                   fontSize: 14,
  //                   fontWeight: FontWeight.bold,
  //                 ),
  //               ),
  //               const SizedBox(height: 10),
  //               Text(
  //                 '₹${response['data']['discount'].toString()}',
  //                 style: const TextStyle(
  //                   fontSize: 20,
  //                   fontWeight: FontWeight.bold,
  //                 ),
  //               ),
  //               const SizedBox(height: 5),
  //               const Text('savings with this coupon'),
  //               const SizedBox(height: 5),
  //               ElevatedButton(
  //                   onPressed: () async {
  //                     await acceptCoupon(response);
  //                   },
  //                   child: const Text('Continue'))
  //             ],
  //           ),
  //         ),
  //       ),
  //     );
  //     // }
  //   }
  // }

  // Future clearCoupon() async {
  //   await DatabaseHelper.instance.setDiscountValue(0, 0, 0, '');
  //   await DatabaseHelper.instance.removeFreeItem(); // to remove free item
  //   // cartcontroller.coupontext = '';
  //   // getProducts();
  // }

  // Future acceptCoupon(response) async {
  //   await clearCoupon(); // clear before applling new coupon.
  //   if (response['data']['offer_type'] == 'variant') {
  //     await DatabaseHelper.instance.addUpdate(
  //       LocalShoppingCart(
  //         name: response['data']['product_name'] ?? '',
  //         qty: response['data']['quantity'],
  //         productID: response['data']['product_id'],
  //         price: double.parse(response['data']['price'].toString()),
  //         // variantID: response['data']['variant_id'],
  //         imageURL: null,
  //         weight: double.parse(response['data']['weight'].toString()),
  //       ),
  //     );

  //     await DatabaseHelper.instance.setDiscountValue(
  //         double.parse(response['data']['discount'].toString()),
  //         response['data']['offer_id'],
  //         double.parse(response['data']['min_amount'].toString()),
  //         response['data']['coupon_code']);
  //     // getProducts();
  //   } else {
  //     await DatabaseHelper.instance.setDiscountValue(
  //         double.parse(response['data']['discount'].toString()),
  //         response['data']['offer_id'],
  //         double.parse(response['data']['min_amount'].toString()),
  //         response['data']['coupon_code']);
  //   }

  //   // cartcontroller.coupontext = response['data']['coupon_code'];

  //   // couponTextCtrl.text = '';
  //   Get.close(2);
  // }
}

class FloatingModal extends StatelessWidget {
  final Widget child;
  final Color backgroundColor;

  const FloatingModal({
    super.key,
    required this.child,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Material(
          color: backgroundColor,
          clipBehavior: Clip.antiAlias,
          borderRadius: BorderRadius.circular(12),
          child: child,
        ),
      ),
    );
  }
}

Future<T> showFloatingModalBottomSheet<T>({
  required BuildContext context,
  required WidgetBuilder builder,
  required Color backgroundColor,
}) async {
  final result = await showCustomModalBottomSheet(
      context: context,
      builder: builder,
      barrierColor: Colors.black.withOpacity(0.5),
      containerWidget: (_, animation, child) => FloatingModal(
            backgroundColor: Colors.transparent,
            child: child,
          ),
      expand: false);

  return result;
}
