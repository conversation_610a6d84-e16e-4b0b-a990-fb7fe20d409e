import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../root_page/root_page.dart';

class EmptyCartWidget extends StatelessWidget {
  const EmptyCartWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(
          vertical: 16 * 2,
          horizontal: 16,
        ),
        child: <PERSON>umn(
          children: [
            SizedBox(
              width: Get.width * 0.7,
              child: <PERSON>um<PERSON>(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    "assets/images/empty_cart_illustration.webp",
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    "Your cart is empty. Looks like you haven’t made your choice",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      height: 18.2 / 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: Get.height * 0.065,
                    child: OutlinedButton(
                      onPressed: () {
                        Get.to(() => const RootPage());
                      },
                      style: OutlinedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(2),
                        ),
                        side: BorderSide(
                          color: Theme.of(context).primaryColor,
                        ),
                        foregroundColor: Theme.of(context).primaryColor,
                        textStyle: const TextStyle(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          height: 20.8 / 16,
                          letterSpacing: -1,
                        ),
                      ),
                      child: const Text("Continue Shopping"),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
