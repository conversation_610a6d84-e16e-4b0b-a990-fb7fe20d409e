import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FreeTagWidget extends StatelessWidget {
  const FreeTagWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      height: 36,
      width: 84,
      alignment: Alignment.center,
      child: Text(
        "Free",
        style: GoogleFonts.dmSans(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w700,
          fontSize: 16,
        ),
      ),
    );
  }
}
