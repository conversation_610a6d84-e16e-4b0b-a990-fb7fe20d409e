import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PriceRowsWidget extends StatelessWidget {
  final String title;
  final String amount;
  final Color? secondColor;
  const PriceRowsWidget({
    super.key,
    required this.title,
    required this.amount,
    this.secondColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Text(
            title,
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 1,
              color: Colors.black,
            ),
          ),
        ),
        Text(
          amount,
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.w500,
            fontSize: 14,
            height: 1,
            color: secondColor ?? Colors.black,
          ),
        )
      ],
    );
  }
}
