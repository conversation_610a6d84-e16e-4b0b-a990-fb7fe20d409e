import 'package:flutter/material.dart';

import '../../../../utils/colors.dart';
import '../../../widgets/decorated_card.dart';
import 'textfield_border.dart';

class CardAdditionForm extends StatelessWidget {
  const CardAdditionForm({super.key});

  @override
  Widget build(BuildContext context) {
    return DecoratedCard(
      child: Form(
        child: Column(
          children: [
            TextFormField(
              decoration: InputDecoration(
                labelText: "Card Number",
                enabledBorder: textfieldBorder,
                focusedBorder: textfieldBorder,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 48,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    flex: 96,
                    child: DropdownButtonFormField<String>(
                      value: mockExpiryMonths.first,
                      items: mockExpiryMonths
                          .map(
                            (e) => DropdownMenuItem<String>(
                              value: e,
                              child: Text(e),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {},
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 109,
                    child: DropdownButtonFormField<String>(
                      value: mockExpiryYears.first,
                      items: mockExpiryYears
                          .map(
                            (e) => DropdownMenuItem<String>(
                              value: e,
                              child: Text(e),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {},
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 113,
                    child: TextFormField(
                      decoration: InputDecoration(
                        labelText: "CVV",
                        enabledBorder: textfieldBorder,
                        focusedBorder: textfieldBorder,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: InputDecoration(
                labelText: "Name on the Card",
                enabledBorder: textfieldBorder,
                focusedBorder: textfieldBorder,
              ),
            ),
            const SizedBox(height: 40),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.ctaBackgroundColor,
                    foregroundColor: AppColors.ctaTextColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      height: 20.8 / 16,
                      letterSpacing: -1,
                    )),
                onPressed: () {},
                child: const Text("Save Card Details"),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

List<String> mockExpiryMonths = [
  "MM",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12"
];
List<String> mockExpiryYears = [
  "YYYY",
  ...List.generate(41, (index) => "${2000 + index}")
];
