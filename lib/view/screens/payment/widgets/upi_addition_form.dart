import 'package:flutter/material.dart';

import '../../../../utils/colors.dart';
import '../../../widgets/decorated_card.dart';
import 'textfield_border.dart';

class UpiAdditionForm extends StatelessWidget {
  const UpiAdditionForm({super.key});

  @override
  Widget build(BuildContext context) {
    return DecoratedCard(
      child: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              decoration: InputDecoration(
                labelText: "Add UPI ID",
                enabledBorder: textfieldBorder,
                focusedBorder: textfieldBorder,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              "Your UPI ID will be encrypted and is 100% safe with us",
              textAlign: TextAlign.start,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                height: 13 / 10,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 40),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.ctaBackgroundColor,
                  foregroundColor: AppColors.ctaTextColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    height: 20.8 / 16,
                    letterSpacing: -1,
                  ),
                ),
                onPressed: () {},
                child: const Text("Add UPI ID"),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
