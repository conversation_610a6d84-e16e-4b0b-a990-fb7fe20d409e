import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../utils/colors.dart';
import '../../../../utils/enumerations.dart';

class AddablePaymentOptionWidget extends StatelessWidget {
  final bool isSelected;
  final PaymentType addablePaymentType;
  final void Function(PaymentType value) onChanged;

  const AddablePaymentOptionWidget({
    super.key,
    required this.isSelected,
    required this.addablePaymentType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onChanged(addablePaymentType);
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 52,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color:
                    isSelected ? Theme.of(context).primaryColor : Colors.white,
                borderRadius: BorderRadius.circular(2),
                boxShadow: !isSelected
                    ? null
                    : [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                          spreadRadius: 0,
                        ),
                      ],
              ),
              child: Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                      isSelected
                          ? "assets/images/selected_payoption_bg.webp"
                          : "assets/images/unselected_payoption_bg.webp",
                    ),
                  ),
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  addablePaymentType.svgAssetPath,
                  colorFilter: ColorFilter.mode(
                    isSelected ? Colors.white : Theme.of(context).primaryColor,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              addablePaymentType == PaymentType.creditCard
                  ? "Credit / Debit Card"
                  : addablePaymentType.name,
              maxLines: 2,
              textAlign: TextAlign.center,
              style: GoogleFonts.dmSans(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontSize: 10,
                height: 13 / 10,
                color: isSelected ? Colors.black : AppColors.kgreyDark,
              ),
            )
          ],
        ),
      ),
    );
  }
}
