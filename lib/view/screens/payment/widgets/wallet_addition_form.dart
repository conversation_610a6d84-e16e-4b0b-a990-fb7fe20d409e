import 'package:flutter/material.dart';

import '../../../../utils/colors.dart';
import '../../../widgets/decorated_card.dart';
import 'textfield_border.dart';

class WalletAdditionForm extends StatelessWidget {
  const WalletAdditionForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        DecoratedCard(
          child: Form(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                      "assets/images/phonepe_logo.webp",
                      height: 24,
                      width: 24,
                    ),
                    const SizedBox(width: 16 / 2),
                    const Text(
                      "PhonePe",
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        height: 18.2 / 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16 / 2),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: "Enter Mobile Number",
                    enabledBorder: textfieldBorder,
                    focusedBorder: textfieldBorder,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  "If you don’t have an account, a new account will be created",
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 10,
                    height: 13 / 10,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 40),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.ctaBackgroundColor,
                      foregroundColor: AppColors.ctaTextColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      textStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        height: 20.8 / 16,
                        letterSpacing: -1,
                      ),
                    ),
                    onPressed: () {},
                    child: const Text("Link Wallet"),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        DecoratedCard(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Image.asset(
                "assets/images/paytm_logo.webp",
                height: 10,
              ),
              const SizedBox(width: 16 / 2),
              const Text("Paytm"),
              const Spacer(),
              TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).primaryColor,
                  textStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    height: 18.2 / 14,
                  ),
                ),
                child: const Text("Link Wallet"),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
