import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../utils/enumerations.dart';
import '../../widgets/rapsap_appbar.dart';
import 'widgets/addable_payment_option_widget.dart';
import 'widgets/card_addition_form.dart';
import 'widgets/upi_addition_form.dart';
import 'widgets/wallet_addition_form.dart';

class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  PaymentOption? selectedPaymentOption;
  PaymentType selectedAddablePaymentType = PaymentType.creditCard;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RapsapAppBar("Payment Method"),
      body: ListView(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 40),
        children: [
          const Text(
            "Payment Options",
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontSize: 18,
              height: 1,
            ),
          ),
          const SizedBox(height: 16),
          ...mockSavedPaymentOptions.map(
            (e) => Card(
              color: Colors.white,
              elevation: 0,
              shape:
                  const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
              child: RadioListTile.adaptive(
                value: e,
                groupValue: selectedPaymentOption,
                onChanged: (value) {
                  setState(() {
                    selectedPaymentOption = e;
                  });
                },
                fillColor: WidgetStateColor.resolveWith(
                    (_) => Theme.of(context).primaryColor),
                title: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            e.type == PaymentType.creditCard ||
                                    e.type == PaymentType.debitCard
                                ? "${(e as PaymentOtionImpl).bankName} ${e.type.name}"
                                : e.type == PaymentType.upi
                                    ? (e as PaymentOtionImpl)
                                        .virtualPaymentAddress
                                    : "Cash on Delivery",
                          ),
                          Text(
                            e.type == PaymentType.creditCard ||
                                    e.type == PaymentType.debitCard
                                ? (e as PaymentOtionImpl).cardNumber
                                : e.type == PaymentType.upi
                                    ? (e as PaymentOtionImpl)
                                        .virtualPaymentAddress
                                    : "Cash on delivery is not applicable on orders less than Rs.50",
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                              height: 16.93 / 13,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (e.type == PaymentType.creditCard ||
                        e.type == PaymentType.debitCard)
                      SvgPicture.asset("assets/svg/visa_logo.svg")
                    else if (e.type == PaymentType.upi)
                      SvgPicture.asset("assets/svg/upi_icon.svg")
                    else
                      const SizedBox.shrink(),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 40),
          const Text(
            "Add Payment Options",
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontSize: 18,
              height: 1,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: PaymentType.addableValues
                .map(
                  (addablePaymentType) => AddablePaymentOptionWidget(
                    addablePaymentType: addablePaymentType,
                    isSelected:
                        selectedAddablePaymentType == addablePaymentType,
                    onChanged: (value) {
                      setState(() {
                        selectedAddablePaymentType = value;
                      });
                    },
                  ),
                )
                .toList(),
          ),
          const SizedBox(height: 16),
          IndexedStack(
            index:
                PaymentType.addableValues.indexOf(selectedAddablePaymentType),
            children: const [
              CardAdditionForm(),
              UpiAdditionForm(),
              WalletAdditionForm(),
            ],
          ),
        ],
      ),
    );
  }
}

List<PaymentOption> mockSavedPaymentOptions = [
  PaymentOtionImpl(
    type: PaymentType.debitCard,
    bankName: "ICICI",
    cardNumber: "************",
    transactionPartner: "VISA",
    expiryMonth: "02",
    expiryYear: "27",
  ),
  PaymentOtionImpl(
    type: PaymentType.upi,
    virtualPaymentAddress: "johncena@kotak",
    transactionPartner: "UPI",
  ),
  PaymentOtionImpl(
    type: PaymentType.cashhOnDelivery,
    isEligibleForCOD: true,
  ),
];

abstract class PaymentOption {
  late PaymentType type;
}

enum PaymentWalletType { phonepe, paytm }

class PaymentOtionImpl implements PaymentOption {
  PaymentOtionImpl({
    required this.type,
    this.bankName = "",
    this.cardNumber = "",
    this.expiryMonth = "",
    this.expiryYear = "",
    this.nameOnCard = "",
    this.transactionPartner = "",
    this.virtualPaymentAddress = "",
    this.walletType,
    this.isEligibleForCOD = false,
  })
  //  : assert(
  //           (type == PaymentType.creditCard || type == PaymentType.debitCard) &&
  //               bankName.isNotEmpty &&
  //               cardNumber.length == 12 &&
  //               transactionPartner.isNotEmpty &&
  //               expiryMonth.length == 2 &&
  //               expiryYear.length == 2),
  //       // TODO: add validation for upi address
  //       assert(type == PaymentType.upi && virtualPaymentAddress.isNotEmpty),
  //       // TODO: add validation for payment wallet
  //       assert(type == PaymentType.wallet && walletType != null),
  //       assert(type == PaymentType.cashhOnDelivery && isEligibleForCOD)
  ;

  @override
  PaymentType type;

  // card details
  final String bankName;
  final String cardNumber;
  final String expiryMonth;
  final String expiryYear;
  final String nameOnCard;
  final String transactionPartner;

  // upi details
  final String virtualPaymentAddress;

  // wallet details
  final PaymentWalletType? walletType;

  // cash on delivery details
  final bool isEligibleForCOD;
}
