import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../controllers/user_controller.dart';
import '../../../model/model.dart';
import '../../../utils/colors.dart';
import '../../widgets/widgets.dart';
import '../product_screen/product_screen.dart';
import '../../widgets/add_remove_button.dart';

class SearchResultCard extends StatelessWidget {
  const SearchResultCard({super.key, required this.model});

  final Item model;

  @override
  Widget build(BuildContext context) {
    final discountVal =
        model.mrp == 0
            ? 0.0
            : ((100 * (model.mrp - model.price)) / model.mrp).floorToDouble();
    return ButtonAnimation(
      onpress: () {
        Get.to(() => ProductScreen(item: model));
      },
      animationWidget: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Stack(
          children: [
            DecoratedBox(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.kgrey),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // product image
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: CachedNetworkImage(
                        placeholder:
                            (context, url) =>
                                Image.asset("assets/images/error-image.webp"),
                        errorWidget:
                            (context, url, error) =>
                                Image.asset("assets/images/error-image.webp"),
                        fit: BoxFit.contain,
                        imageUrl: model.imageUrls.firstOrNull ?? '',
                      ).paddingAll(2),
                    ),
                  ),

                  // product label
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(color: AppColors.kHomeAppBarBG),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // name
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              model.name?.capitalize ?? "",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              textAlign: TextAlign.start,
                              style: GoogleFonts.dmSans(
                                height: 1,
                                letterSpacing: -0.01,
                                fontSize: 16,
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          // weight
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${model.weight}${model.unit}",
                              style: GoogleFonts.dmSans(
                                fontSize: 14,
                                letterSpacing: -0.01,
                                color: Colors.black.withOpacity(0.9),
                              ),
                            ),
                          ),

                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // prices
                              FittedBox(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '\u20b9${model.price.round() == 0 ? model.mrp.round() : model.price.round()}',
                                      style: GoogleFonts.roboto(
                                        height: 1,
                                        fontSize: 20,
                                        letterSpacing: -0.01,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    Visibility(
                                      visible:
                                          model.price.round() != 0 &&
                                          model.mrp.round() != 0 &&
                                          model.mrp != model.price,
                                      child: Text(
                                        '\u20b9${model.mrp.toDouble().round()}',
                                        style: GoogleFonts.roboto(
                                          fontSize: 16,
                                          height: 20.8 / 16,
                                          color: Colors.black.withOpacity(0.5),
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // add/remove button
                              Flexible(child: AddRemoveButton(model: model)),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Visibility(
              visible: discountVal != 0,
              replacement: const SizedBox(width: 32, height: 44),
              child: Container(
                width: 32,
                height: 44,
                alignment: Alignment.topLeft,
                child: Stack(
                  alignment: Alignment.topLeft,
                  children: [
                    SvgPicture.asset(
                      'assets/svg/offertag.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xffFF5454),
                        BlendMode.srcIn,
                      ),
                    ),
                    Center(
                      child: Text(
                        "${discountVal.round()}% \n OFF",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                          letterSpacing: -0.5,
                        ),
                        textAlign: TextAlign.center,
                      ).paddingOnly(bottom: 10),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
