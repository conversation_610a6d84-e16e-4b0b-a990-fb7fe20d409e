// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../main.dart';
import '../../../model/model.dart';
import '../../../utils/colors.dart';
import '../../widgets/widgets.dart';
import '../../../controllers/search_controller.dart' as sw;
import 'no_search_result_found_widget.dart';
import 'search_result_card.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  int page = 1;
  final sw.SearchController controller = Get.put(sw.SearchController());

  final ScrollController scrollController = ScrollController();

  String query = "";

  final _debouncer = Debouncer(milliseconds: 1000);
  late final FocusNode searchfocus;

  @override
  void initState() {
    searchfocus = FocusNode();
    scrollController.addListener(() {
      final nextPageTrigger =
          scrollController.position.maxScrollExtent - Get.height * 0.2;
      if (scrollController.position.pixels > nextPageTrigger &&
          !controller.nextpageloading &&
          controller.searchtotal.value > controller.searchresults.docs.length) {
        getProducts();
      }
    });
    controller.searchlist.value = List<String>.from(
        storage.read<List>("searchlist")?.map((e) => e.toString()) ?? []);
    super.initState();
  }

  Future<void> getProducts() async {
    await controller.getproducts(
        query: controller.textEditingController.text, page: page++);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        toolbarHeight: 80,
        leadingWidth: 0,
        titleSpacing: 0,
        elevation: 0,
        backgroundColor: Colors.white,
        title: SizedBox(
          height: 45,
          child: TextField(
            autofocus: true,
            focusNode: searchfocus,
            textInputAction: TextInputAction.search,
            controller: controller.textEditingController,
            onChanged: (value) => searchchanged(value),
            maxLines: 1,
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 12),
              // isDense: true,
              prefixIcon: InkWell(
                onTap: () => Get.back(),
                child: Padding(
                  padding: const EdgeInsets.all(14),
                  child: SvgPicture.asset("assets/svg/iconback.svg"),
                ),
              ),
              suffixIcon: InkWell(
                onTap: () {
                  controller
                    ..textEditingController.clear()
                    ..searchresults = ItemListModel()
                    ..update();
                },
                child: Icon(
                  Icons.close,
                  color: AppColors.kgrey,
                  size: 20,
                ),
              ),
              hintText: "Search for products...",
              hintStyle: const TextStyle(fontSize: 15),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: const BorderSide(color: Color(0xffC5C8CD)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: const BorderSide(
                  width: 1,
                  color: Color(0xffC5C8CD),
                ),
              ),
            ),
          ).paddingSymmetric(horizontal: 16),
        ),
      ),
      body: KeyboardHider(
        child: Stack(
          children: [
            SingleChildScrollView(
              controller: scrollController,
              child: GetBuilder<sw.SearchController>(
                builder: (_) {
                  return Visibility(
                    visible: !controller.searchLoading.value,
                    replacement: searchLoading().paddingAll(24),
                    child: Visibility(
                      visible: controller.textEditingController.text.isNotEmpty,
                      replacement: initialSearchStateWidget(),
                      child: Visibility(
                        visible: controller.searchresults.docs.isNotEmpty,
                        replacement: NoSearchResultsFoundWidget(
                          searchQuery:
                              controller.textEditingController.text.trim(),
                        ),
                        child: Container(
                          color: Colors.white,
                          child: Column(
                            children: [
                              searchResultTitle().paddingSymmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  GridView.count(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                    ),
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    childAspectRatio: 0.62,
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 10,
                                    mainAxisSpacing: 10,
                                    children: controller.searchresults.docs
                                        .map(
                                          (e) => SearchResultCard(model: e),
                                        )
                                        .toList(),
                                  ),
                                  const SizedBox(height: 10),
                                  if (controller.nextpageloading)
                                    CircularProgressIndicator.adaptive(
                                      valueColor: AlwaysStoppedAnimation(
                                          Theme.of(context).primaryColor),
                                    ),
                                ],
                              ),
                              const SizedBox(height: 50),
                              const SizedBox(height: 50),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const Align(
              alignment: Alignment.bottomCenter,
              child: MinimisedCartItemStatusWidget(),
            )
          ],
        ),
      ),
    );
  }

  Widget initialSearchStateWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Search',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 18,
              ),
            ),
            InkWell(
              onTap: () async {
                await storage.remove('searchlist');
                controller
                  ..searchlist.value = storage
                          .read<List>('searchlist')
                          ?.map((e) => e.toString())
                          .toList() ??
                      []
                  ..update();
              },
              child: Text(
                'Clear',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          ],
        ),
        const SizedBox(height: 10),
        Wrap(
          alignment: WrapAlignment.start,
          spacing: 8,
          runSpacing: 8,
          children: List.generate(
            controller.searchlist.length,
            (index) => InkWell(
              onTap: () {
                controller.textEditingController.text =
                    controller.searchlist[index];
                searchchanged(controller.textEditingController.text);
              },
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.black.withOpacity(0.5),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 5,
                      ),
                      child: Text(
                        controller.searchlist[index],
                        style: TextStyle(
                          color: Colors.black.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      ],
    ).paddingSymmetric(horizontal: 24);
  }

  Row searchResultTitle() {
    return Row(
      children: [
        Text(
          '${controller.searchresults.totalCount ?? 0} results of ',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
          ),
        ),
        Text(
          '"$query"',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  Row searchLoading() {
    return Row(
      children: [
        SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator.adaptive(
            valueColor: AlwaysStoppedAnimation(
              Colors.grey.shade300,
            ),
          ),
        ),
        const SizedBox(width: 20),
        const Text('Searching'),
      ],
    );
  }

  void searchchanged(String value) {
    if (value.isEmpty) {
      controller
        ..textEditingController.clear()
        ..searchresults = ItemListModel()
        ..update();
      return;
    }
    if (value.isNotEmpty && value.length > 1) {
      if (query != controller.textEditingController.text) {
        _debouncer.run(
          () async {
            page = 1;
            await controller.getproducts(
                query: controller.textEditingController.text);

            query = controller.textEditingController.text.trim();
            Future.delayed(
              const Duration(milliseconds: 1000),
              () async {
                if (query == controller.textEditingController.text &&
                    (controller.searchresults.docs.isNotEmpty) &&
                    query.isNotEmpty) {
                  if (controller.searchlist.contains(query)) {
                    controller.searchlist.remove(query);
                  }
                  controller.searchlist.insert(0, query);
                  if (controller.searchlist.length > 10) {
                    controller.searchlist.removeLast();
                  }
                  await storage.write('searchlist', controller.searchlist);
                  controller
                    ..searchlist.value = storage
                            .read<List>('searchlist')
                            ?.map((e) => e.toString())
                            .toList() ??
                        []
                    ..update();
                }
              },
            );
          },
        );
      }
    }
  }
}

class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(VoidCallback action) {
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}
