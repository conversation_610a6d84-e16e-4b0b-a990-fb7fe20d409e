import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../../utils/colors.dart';

class NoSearchResultsFoundWidget extends StatefulWidget {
  const NoSearchResultsFoundWidget({
    super.key,
    required this.searchQuery,
  });

  final String searchQuery;

  @override
  State<NoSearchResultsFoundWidget> createState() =>
      _NoSearchResultsFoundWidgetState();
}

class _NoSearchResultsFoundWidgetState
    extends State<NoSearchResultsFoundWidget> {
  bool isSuggestingItem = false;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              text: "Sorry! We couldn’t find any results for ",
              children: [
                TextSpan(
                  text: "“${widget.searchQuery}”",
                  style: const TextStyle(
                    fontWeight: FontWeight.w700,
                  ),
                )
              ],
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 20.8 / 16,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 16 / 2),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "Have any specific items or brands you'd like us to consider adding to our inventory?",
                            style: TextStyle(
                              fontSize: 14,
                              height: 18.2 / 14,
                              color: Colors.black.withOpacity(0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            "Let us know the detail and we will try our best to add it to the inventory",
                            style: TextStyle(
                              fontSize: 12,
                              height: 16 / 12,
                              color: AppColors.ktealGrey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Image.asset(
                        "assets/images/groceries_circle_avatar.webp"),
                  )
                ],
              ),
              Visibility(
                visible: isSuggestingItem,
                replacement: Directionality(
                  textDirection: TextDirection.rtl,
                  child: TextButton.icon(
                    onPressed: () {
                      setState(() {
                        isSuggestingItem = !isSuggestingItem;
                      });
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.only(left: 16 / 2),
                      textStyle: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 12,
                        height: 16 / 12,
                      ),
                    ),
                    icon: Icon(
                      Platform.isAndroid
                          ? Icons.arrow_back
                          : Icons.chevron_left,
                    ),
                    label: const Text("Let us Know"),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: CustomPaint(
                    painter: DashedPainter(
                      customPath: (p0) {
                        Path path = Path()
                          ..moveTo(0, 0)
                          ..lineTo(p0.width, 0);
                        return path;
                      },
                      dashPattern: [5, 2],
                      strokeWidth: 1,
                      color: AppColors.kgreyDark,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextFormField(
                            maxLines: 4,
                            decoration: const InputDecoration(
                              hintText: "Specify Item or Brand",
                              hintStyle: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                height: 16 / 12,
                              ),
                              border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              OutlinedButton(
                                onPressed: () {
                                  // TODO: clear textEditingController
                                  setState(() {
                                    isSuggestingItem = !isSuggestingItem;
                                  });
                                },
                                style: OutlinedButton.styleFrom(
                                  foregroundColor:
                                      AppColors.secondaryBtnBorderColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: const Text("Cancel"),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: () {
                                  // TODO: clear textEditingController
                                  // TODO: clear textEditingController of searchText
                                  // TODO: request focus on search field
                                },
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.ctaTextColor,
                                  backgroundColor: AppColors.ctaBackgroundColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: const Text("Submit"),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
