import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import '../../../controllers/order_controller.dart';
import '../../../utils/colors.dart';
import 'order_detail.dart';

// ignore: must_be_immutable
class CancelScreen extends StatelessWidget {
  CancelScreen({super.key});
  String reasonText = "";
  final OrderController orderController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leadingWidth: 0,
        elevation: 0,
        toolbarHeight: 60,
        automaticallyImplyLeading: false,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => Get.back(),
                  child: Icon(
                    Icons.arrow_back,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(width: 27),
                SizedBox(
                  width: Get.width * 0.5,
                  child: Text(
                    "Cancel Order",
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.dmSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     getSvgIcon("assets/svg/cart-coverd.svg"),
            //     SizedBox(width: 10),
            //   ],
            // )
          ],
        ),
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(10),
          child: ThickDivider(),
        ),
      ),
      body: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Container(
          height: MediaQuery.of(context).size.height / 1.5,
          color: Colors.white,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                const Align(
                  alignment: Alignment.topLeft,
                  child: Text('Reason for cancellation'),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  minLines: 5,
                  maxLines: 5,
                  maxLength: 128,

                  decoration: const InputDecoration(
                    focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black, width: 2.0),
                        borderRadius: BorderRadius.zero),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey, width: 2.0),
                      borderRadius: BorderRadius.zero,
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey, width: 2.0),
                      borderRadius: BorderRadius.all(
                        Radius.circular(19.0),
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Colors.redAccent, width: 2.0),
                      borderRadius: BorderRadius.all(
                        Radius.circular(19.0),
                      ),
                    ),
                    hintText: 'Type Reason here!',
                  ),
                  // The validator receives the text that the user has entered.
                  onChanged: (val) {
                    // setState(() {
                    reasonText = val;
                    // });
                  },
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return "reason required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),
                Row(
                  children: [
                    Expanded(
                      child: Obx(
                        () => ElevatedButton(
                          onPressed: orderController.buttonloading.value
                              ? null
                              : () async {
                                  if (_formKey.currentState!.validate()) {
                                    orderController.buttonloading.value = true;
                                    await cancelOrderFn();
                                  }
                                },
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            backgroundColor: AppColors.ctaBackgroundColor,
                            padding: const EdgeInsets.symmetric(
                              vertical: 13,
                              horizontal: 30,
                            ),
                          ),
                          child: orderController.buttonloading.value
                              ? SizedBox(
                                  height: 20,
                                  child: LoadingIndicator(
                                    colors: [
                                      Theme.of(context).primaryColor,
                                      Colors.black,
                                    ],
                                    indicatorType: Indicator.cubeTransition,
                                  ),
                                )
                              : const Text(
                                  'Cancel Order',
                                  style: TextStyle(fontSize: 17),
                                ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future cancelOrderFn() async {
    // var req = {
    //   'order_id': orderController.orderDetailModel.value.data?.orderId,
    //   'cancel_reason': reasonText,
    //   'cancelled_by': 'Customer',
    // };
    //  TODO: call cancel api here
    // orderController.cancelOrder();
    // Get.offUntil(
    //     GetPageRoute(
    //         page: () => OrderDetailScreen(
    //               orderId: orderController.orderDetailModel.value.data!.orderId,
    //             )),
    //     (route) => false);

    // getSingleOrder(singleOrder.data?.orderId);
  }
}
