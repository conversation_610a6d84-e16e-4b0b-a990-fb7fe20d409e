// // ignore_for_file: no_leading_underscores_for_local_identifiers

// import 'dart:developer';

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:get/get.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';

// import '../../../controllers/account_controller.dart';
// import '../../../model/database_models/database_models.dart';
// import '../../../services/order_services.dart';
// import '../login/widgets/button.dart';
// import 'order_success.dart';
// import '../../../controllers/cart_controlller.dart';
// import '../../../controllers/user_controller.dart';
// import '../../../main.dart';
// import '../../../services/database_helper.dart';
// import '../../../utils/constants.dart';
// import '../cart/cart_screen.dart';

// class OrderProcessing extends StatefulWidget {
//   final LocalOrder order;

//   final List<LocalShoppingCart> itemlist;

//   const OrderProcessing({
//     super.key,
//     required this.order,
//     required this.itemlist,
//   });

//   @override
//   State<OrderProcessing> createState() => _OrderProcessingState();
// }

// class _OrderProcessingState extends State<OrderProcessing> {
//   final CartController controller = Get.find<CartController>();

//   final UserController userController = Get.find<UserController>();

//   final AccountController accountController =
//       Get.isRegistered<AccountController>()
//           ? Get.find<AccountController>()
//           : Get.put(AccountController());
//   final _razorpay = Razorpay();
//   @override
//   void initState() {
//     super.initState();
//     createOrder();

//     _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
//     _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
//     _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
//   }

//   double getGrandTotal(LocalOrder _order) {
//     double? discount = _order.discount ?? 0.0;
//     log(discount.toString());
//     double grandtotal = (_order.subTotal! - discount);
//     // return grandtotal.floorToDouble();
//     if (grandtotal >
//         double.parse(configModel.deliveryFeeThreshold.toString())) {
//       return grandtotal.floorToDouble();
//     }
//     return (grandtotal + double.parse(configModel.deliveryFee.toString()))
//         .floorToDouble();
//   }

//   Future<void> createOrder() async {
//     LocalOrder _order = widget.order;
//     var tax = _order.tax ?? 0.0;

//     log("START");
//     var orderpayload = {
//       // TODO: find a way to do this without user id
//       // "user_id": userController.userdata.value!.id.toString(),
//       "brand_id": 0,
//       "discount": _order.discount ?? 0.0,
//       "grand_total": getGrandTotal(_order), // need to change here
//       "gst": ((_order.subTotal! * tax) / 100).floorToDouble(),
//       "orderDetails": widget.itemlist.map((e) {
//         return {
//           "product_id": e.productID,
//           "quantity": e.qty,
//           // "variant_id": e.variantID,
//           "buying_price": e.price,
//         };
//       }).toList(),
//       "address_id": accountController.selectedAddress.value.id,
//       "order_type": 'ONLINE',
//       "store_id": storage.read('storeID') ?? 0,
//       "sub_total": _order.subTotal,
//       "offer_id": _order.offerID ?? 0,
//       "delivery_cost": getGrandTotal(_order) >
//               double.parse(configModel.deliveryFeeThreshold.toString())
//           ? 0
//           : double.parse(configModel.deliveryFee.toString())
//     };

//     if (_order.orderID == null) {
//       log("order created");
//       try {
//         var ordRes = await OrderServices.createOrder(orderpayload);
//         if (ordRes['success'] == true) {
//           await DatabaseHelper.instance.setOrderID(ordRes['data']['order_id']);
//           List<LocalOrder> odrList =
//               await DatabaseHelper.instance.getDbOrder1();

//           _order = odrList[0];

//           initiatePayment(_order, userController);
//         }
//       } catch (e) {
//         controller.buttonloading = false;
//         controller.update();
//         // EasyLoading.dismiss();
//       }
//     } else {
//       log("order_updated");
//       var orderpayload = {
//         "order_id": _order.orderID,
//         // TODO: find a way to do this without user id
//         // "updated_by": userController.userdata.value!.id.toString(),
//         "brand_id": 0,
//         "discount": _order.discount ?? 0.0,
//         "grand_total": getGrandTotal(_order), // need to change here
//         // "gst": ((_order.subTotal! * _order.tax!) / 100).floorToDouble(),
//         "gst": 0,
//         "orderDetails": widget.itemlist.map((e) {
//           return {
//             "product_id": e.productID,
//             "quantity": e.qty,
//             // "variant_id": e.variantID,
//             "buying_price": e.price,
//           };
//         }).toList(),
//         "address_id": accountController.selectedAddress.value.id,
//         "store_id": storage.read('storeID') ?? 1,
//         "sub_total": _order.subTotal,
//         "status": "order_updated",
//         "order_type": 'ONLINE',
//         "delivery_cost": getGrandTotal(_order) >
//                 double.parse(configModel.deliveryFeeThreshold.toString())
//             ? 0
//             : double.parse(configModel.deliveryFee.toString())
//       };
//       var ordUpdateRes = await OrderServices.updateOrder(orderpayload);
//       if (ordUpdateRes['success'] == true) {
//         initiatePayment(_order, userController);
//       }
//     }
//   }

//   Future<void> initiatePayment(_order, usertCTRL) async {
//     var initPayload = {
//       "order_id": _order.orderID,
//       "grand_total": getGrandTotal(_order),
//       "user_id": usertCTRL.userdata.value.data!.id.toString()
//     };

//     var payment = await DatabaseHelper.instance.getPaymentDetails();
//     if (payment.length > 0) {
//       var options = {
//         'key': payment[0]['razorKey'],
//         'amount': getGrandTotal(_order),
//         'image': rapsaplogobase64,

//         'name': 'RapSap',
//         'order_id': payment[0]['razorInvoiceID'],
//         'description': 'RapSAp',
//         "theme.color": "#000000",

//         // 'timeout': 60, // in seconds
//         'prefill': {
//           'contact': usertCTRL.userdata.value.data!.mobile,
//           'email': usertCTRL.userdata.value.data!.email
//         },
//         "notes": {
//           "order_id": _order.orderID,
//           "grand_total": getGrandTotal(_order),
//           "user_id": usertCTRL.userdata.value.data!.id.toString()
//         }
//       };
//       controller
//         ..buttonloading = false
//         ..update();

//       _razorpay.open(options);
//     } else {
//       var payOption = await OrderServices.initiatePayment(initPayload);
//       // print('initiatePayment:: ${payOption}');
//       // print('razorpay_key_id:: ${payOption['data']['razorpay_key_id']}');
//       if (payOption['success'] == true) {
//         // EasyLoading.dismiss();
//         DatabaseHelper.instance.setPaymentDetails(
//           payOption['data']['order_id'].toString(),
//           payOption['data']['id'],
//           payOption['data']['status'],
//           payOption['data']['razorpay_key_id'],
//         );
//         var options = {
//           'key': payOption['data']['razorpay_key_id'],
//           'amount': getGrandTotal(_order),
//           'image': rapsaplogobase64,
//           'order_id': payOption['data']['razorpay_id'],
//           'description': 'Rapsap',
//           'name': 'RapSap',
//           "theme.color": "#000000",

//           // 'timeout': 60, // in seconds
//           'prefill': {
//             'contact': usertCTRL.userdata.value.data!.mobile,
//             'email': usertCTRL.userdata.value.data!.email
//           },
//           "notes": {
//             "order_id": _order.orderID,
//             "grand_total": getGrandTotal(_order),
//             "user_id": usertCTRL.userdata.value.data!.id.toString()
//           }
//         };
//         controller
//           ..buttonloading = false
//           ..update();

//         _razorpay.open(options);
//       } else {
//         controller
//           ..buttonloading = false
//           ..update();
//       }
//     }
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     _razorpay.clear();
//   }

//   void _handlePaymentSuccess(PaymentSuccessResponse response) async {
//     // Do something when payment succeeds
//     if (kDebugMode) {
//       print('response.orderId:: ${response.orderId}');
//       print('response.paymentId:: ${response.paymentId}');
//       print('response.signature:: ${response.signature}');
//     }

//     if (response.paymentId != null && response.signature != null) {
//       // Get.snackbar('Success', 'Payment is capture successfully!');
//       // EasyLoading.dismiss();

//       Get.off(() => const SuccessPage());
//     }
//   }

//   void _handlePaymentError(PaymentFailureResponse response) async {
//     // Do something when payment fails
//     if (kDebugMode) {
//       print('_handlePaymentError:: ${response.code}');
//       print('_handlePaymentError:: ${response.message}');
//     }
//     Get.back();

//     await showDialog(
//       context: context,
//       builder: (context) {
//         return Dialog(
//           insetPadding: const EdgeInsets.all(16),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SvgPicture.asset('assets/svg/paymentfailed.svg'),
//               const SizedBox(height: 20),
//               Text(
//                 'Oops! Payment Failed',
//                 style: headTextStyle,
//               ),
//               const SizedBox(height: 10),
//               // Text(
//               //   'Sorry! payment unsuccessfull, Please try again',
//               // ),
//               Text(
//                 '${response.message}',
//               ),
//               const SizedBox(height: 30),
//               SubmitButton(
//                 height: 45,
//                 text: 'Try Again',
//                 onpressed: () {
//                   Get.close(1);
//                 },
//                 textsize: 16,
//               ),
//               const SizedBox(height: 20),
//             ],
//           ).paddingAll(16),
//         );
//       },
//     );
//     // EasyLoading.dismiss();
//   }

//   void _handleExternalWallet(ExternalWalletResponse response) {
//     // Do something when an external wallet was selected
//     // EasyLoading.dismiss();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return const Scaffold(
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             CircularProgressIndicator.adaptive(
//               strokeWidth: 5,
//               backgroundColor: kgrey,
//               valueColor: AlwaysStoppedAnimation<Color>(kblack),
//             ),
//             SizedBox(height: 20),
//             Text(
//               'Processing',
//               style: TextStyle(fontWeight: FontWeight.bold),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
