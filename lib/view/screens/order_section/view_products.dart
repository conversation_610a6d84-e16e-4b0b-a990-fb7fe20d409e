import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../model/order/order_detail_model/order_detail.dart';
import 'order_detail.dart';

class ViewProducts extends StatelessWidget {
  const ViewProducts({super.key, required this.listitems});
  final List<OrderDetail> listitems;

  @override
  Widget build(BuildContext context) {
    // print(listitems[1].toJson());
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leadingWidth: 0,
        elevation: 0,
        toolbarHeight: 60,
        automaticallyImplyLeading: false,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => Get.back(),
                  child: Icon(
                    Icons.arrow_back,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(width: 27),
                SizedBox(
                  width: Get.width * 0.5,
                  child: const Text(
                    "View Products",
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     getSvgIcon("assets/svg/cart-coverd.svg"),
            //     SizedBox(width: 10),
            //   ],
            // )
          ],
        ),
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(10),
          child: ThickDivider(),
        ),
      ),
      body: ListView.builder(
        itemCount: listitems.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return ItemCard(data: listitems[index], index: index);
        },
      ),
    );
  }
}

class ItemCard extends StatelessWidget {
  final OrderDetail data;
  final int index;
  const ItemCard({required this.data, required this.index, super.key});
  // final CartController controller = Get.find<CartController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      child: ClipRRect(
        child: Card(
          elevation: 20,
          shadowColor: Colors.black.withOpacity(0.2),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 2.0,
                  spreadRadius: 0.4,
                  offset: const Offset(0.1, 0.5),
                ),
              ],
            ),
            child: InkWell(
              onTap: () {
                // Get.to(() => ProductScreen(
                //       productId: data.productId!,
                //       variantId: data.variantId,
                //     ));
              },
              child: Column(
                children: [
                  Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(10),
                                    child:
                                        data.image!.url == null
                                            ? Image.asset(
                                              "assets/images/error-image.webp",
                                            )
                                            : CachedNetworkImage(
                                              imageUrl: data.image!.url!,
                                            ),
                                  ),
                                ),
                                Expanded(
                                  flex: 5,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: Get.width * 0.4,
                                        child: Text(
                                          data.productName!,
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      // Text(
                                      //   double.tryParse(data.variantName
                                      //                   ?.split('')
                                      //                   .first ??
                                      //               "") ==
                                      //           null
                                      //       ? data.variantName ?? "..."
                                      //       : getweight(data.variantName
                                      //               ?.split(' ')
                                      //               .first ??
                                      //           "0"),
                                      //   style: TextStyle(
                                      //     fontWeight: FontWeight.w400,
                                      //     color: AppColors.kblack.withOpacity(0.5),
                                      //   ),
                                      // ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            flex: 4,
                                            child: Row(
                                              children: [
                                                Text(
                                                  double.parse(
                                                    data.buyingPrice ??
                                                        data.price ??
                                                        "",
                                                  ).round().toString(),
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w700,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                // Text(
                                                //   data.variants![variant_index]
                                                //                   .mrp ==
                                                //               "0.00" ||
                                                //           data
                                                //                   .variants![
                                                //                       variant_index]
                                                //                   .mrp ==
                                                //               data
                                                //                   .variants![
                                                //                       variant_index]
                                                //                   .price
                                                //       ? ""
                                                //       : '${data.variants![variant_index].mrp}',
                                                //   style: TextStyle(
                                                //       decoration: TextDecoration
                                                //           .lineThrough),
                                                // ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: Container(
                                              height: 34,
                                              color: Colors.black,
                                              child: Center(
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  children: [
                                                    Text(
                                                      "Qty: ${data.quantity}",
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     SizedBox(
                      //       height: 47,
                      //       child: Stack(
                      //         children: [
                      //           SvgPicture.asset(
                      //             'assets/svg/triangle_offer.svg',
                      //             color: Color(0xffE73636),
                      //             height: 47,
                      //           ),
                      //           Positioned(
                      //             bottom: 20,
                      //             right: 4,
                      //             left: 3,
                      //             child: Transform.rotate(
                      //                 angle: 5.54,
                      //                 child: const Text(
                      //                   '30% OFF',
                      //                   style: TextStyle(
                      //                       color: Colors.white,
                      //                       fontWeight: FontWeight.w700,
                      //                       fontSize: 10),
                      //                 )),
                      //           ),
                      //         ],
                      //       ),
                      //     ),
                      //   ],
                      // ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
