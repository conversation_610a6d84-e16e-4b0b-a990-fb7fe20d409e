import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';
import '../../widgets/decorated_card.dart';
import '../login/widgets/button.dart';
import '../root_page/root_page.dart';
import '../../../controllers/order_controller.dart';

class MyOrders extends StatelessWidget {
  MyOrders({super.key});
  final OrderController orderController = Get.find<OrderController>();

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      displacement: 125,
      backgroundColor: Colors.white,
      color: Theme.of(context).primaryColor,
      onRefresh: () async {
        await orderController.getOrders();
      },
      child: Scaffold(
        appBar: RapsapAppBar("My Orders"),
        body: Obx(
          () => Visibility(
            // real data
            // visible: orderController.orderListModel.value.data?.isNotEmpty ??
            //     false,
            // mock data
            visible:
                orderController.orderListModel.value.data?.isNotEmpty ?? false,
            replacement: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset("assets/svg/emptyprders.svg")
                    .paddingSymmetric(horizontal: 40),
                const SizedBox(height: 30),
                const Text(
                  "No orders yet",
                  style: TextStyle(
                      fontSize: 20,
                      color: Color(0xff6F6F6F),
                      fontWeight: FontWeight.w700),
                ),
                const SizedBox(height: 30),
                SizedBox(
                  width: 200,
                  height: 50,
                  child: SubmitButton(
                    textsize: 20,
                    text: 'Order now',
                    onpressed: () {
                      Get.offAll(
                        const RootPage(),
                        transition: Transition.rightToLeftWithFade,
                      );
                    },
                  ),
                ),
              ],
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder: (context, index) {
                // real data
                // final data =
                //     orderController.orderListModel.value.data![index];

                // mock data
                final data = orderController.orderListModel.value.data?[index];
                return DecoratedCard(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16 / 2,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 24,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        child: InkWell(
                          onTap: () {
                            try {
                              // if (orderController.orderDetailModel.value
                              //         .data?.orderId !=
                              //     data.orderId) {
                              //   orderController.orderDetailModel.value =
                              //       OrderDetailModel();
                              // }

                              // Get.to(() => OrderDetailScreen(order: data));
                              throw UnimplementedError();
                            } catch (e) {
                              log(e.toString());
                            }
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Order ID ${data?.salesOrderNumber}",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 14,
                                      height: 18.23 / 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 16 / 4),
                                  if (data?.orderDate != null)
                                    Text(
                                      "${DateFormat('dd MMM yyyy').format(data!.orderDate!)} at ${DateFormat('hh:mm a').format(data.orderDate!).toLowerCase()}",
                                      style: TextStyle(
                                        color: AppColors.kgreyDark,
                                        fontWeight: FontWeight.w400,
                                        fontSize: 12,
                                        height: 15.62 / 12,
                                      ),
                                    )
                                ],
                              ),
                              const Icon(
                                Icons.arrow_forward_ios,
                                size: 13,
                              )
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16 / 2),
                      Divider(
                        color: AppColors.kDecoratedCardBorder,
                        height: 1,
                        thickness: 1,
                      ),
                      const SizedBox(height: 16 / 2),
                      const Text(
                        "Delivery address",
                        style: TextStyle(
                          fontSize: 14,
                          height: 21 / 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        data?.address?.toString() ?? "---",
                        style: TextStyle(
                          fontSize: 12,
                          height: 16.8 / 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.black.withOpacity(0.5),
                        ),
                      ),
                      const SizedBox(height: 16 / 2),
                      Divider(
                        color: AppColors.kDecoratedCardBorder,
                        height: 1,
                        thickness: 1,
                      ),
                      const SizedBox(height: 16 / 2),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${data?.items?.fold(0, (previousValue, element) => previousValue + (int.tryParse(element.quantity ?? '0') ?? 0))} items',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              height: 21 / 14,
                              color: Colors.black,
                            ),
                          ),
                          Text(
                            "\u20b9${data?.total}",
                            style: GoogleFonts.inter(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              height: 21 / 14,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Flexible(
                        child: Text(
                          data?.items
                                  ?.map((element) =>
                                      "${element.quantity} x ${element.itemId?.name?.capitalize}")
                                  .join('\n') ??
                              "---",
                          // overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12,
                            height: 21 / 12,
                            color: Colors.black.withOpacity(0.5),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16 / 2),
                      Divider(
                        color: AppColors.kDecoratedCardBorder,
                        height: 1,
                        thickness: 1,
                      ),
                      const SizedBox(height: 16 / 2),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 16,
                                color: orderStatusColor(data!.status!),
                              ),
                              const SizedBox(width: 16 / 2),
                              Text(
                                statusCheck(data.status!),
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                  height: 18 / 14,
                                  color: orderStatusColor(data.status!),
                                ),
                              ),
                            ],
                          ),
                          OutlinedButton(
                            onPressed: () {
                              // TODO: add reorder logic here
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Theme.of(context).primaryColor,
                              textStyle: const TextStyle(
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                                height: 20.8 / 16,
                              ),
                              side: BorderSide(
                                  color: Theme.of(context).primaryColor),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            child: const Text("Reorder"),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) =>
                  const SizedBox(height: 16 / 2),
              itemCount:
                  // real data
                  // orderController.orderListModel.value.data!.length,
                  // mock data
                  orderController.orderListModel.value.data?.length ?? 0,
            ),
          ),
        ),
      ),
    );
  }

  String? getDateformat(DateTime date) =>
      DateFormat('dd MMM yyyy hh:mm a').format(date);
}

String statusCheck(String orderStatus) {
  return switch (orderStatus.toLowerCase()) {
    'order_updated' => 'Ordered', //
    'order_paid' => 'Paid', //
    'received' => 'Received',
    'packing' => 'Packing...',
    'packed' => 'Packed',
    'delivery_agent_assigned' => 'Delivery-agent assigned',
    'order_created' => 'Ordered', //
    'cancelled' => 'Cancelled',
    'out_for_delivery' => 'Out for Delivery',
    'out_for_delivery_delayed' => 'Slightly Delayed',
    'delivered' => 'Delivered',
    'reschedule' => 'Rescheduled', //
    'arrived' => 'out_for_delivery', //
    'refund_created' => 'refund initiated', //
    'refund_processed' => 'refund initiated', //
    'refund_failed' => 'refund failed', //
    _ => 'Ordered',
  };
}

Color orderStatusColor(String orderStatus) {
  return switch (orderStatus.toLowerCase()) {
    'order_updated' => const Color(0xFF2B96DC), //
    'order_paid' => const Color(0xff03A74F), //
    'received' => const Color(0xFF2B96DC),
    'packing' => Colors.black,
    'packed' => const Color(0xFFF37A20),
    'delivery_agent_assigned' => const Color(0xFFF37A20),
    'order_created' => const Color(0xFF2B96DC), //
    'cancelled' => const Color(0xffF1373A),
    'out_for_delivery' => const Color.fromARGB(255, 248, 122, 4),
    'out_for_delivery_delayed' => const Color.fromARGB(255, 248, 122, 4),
    'arrived' => const Color.fromARGB(255, 248, 122, 4), //
    'reschedule' => const Color.fromARGB(255, 248, 122, 4), //
    'refund_created' => const Color(0xFFF37A20), //
    'refund_processed' => const Color(0xff03A74F), //
    'refund_failed' => const Color(0xffF1373A), //
    'delivered' => Colors.green.shade700,
    _ => const Color(0xFF2B96DC),
  };
}

// final mockOrderList = <OrderModel>[
//   OrderModel(
//     addressType: "home",
//     createdAt: DateTime.now(),
//     grandTotal: "100",
//     mobile: "234567890",
//     orderId: 1,
//     orderStatus: "delivered",
//     orderDetails: [
//       OrderDetail(quantity: 4, productName: "Amul doodh"),
//     ],
//   ),
// ];
