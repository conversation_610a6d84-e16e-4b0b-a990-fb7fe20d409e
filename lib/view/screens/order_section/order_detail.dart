// ignore_for_file: dead_code

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../../controllers/order_controller.dart';
import '../../../model/order/order_list_model/datum.dart';
import '../../../utils/colors.dart';
import '../cart/price_row_widget.dart';
import '../home_screen/home_screen.dart';
import 'cancel_screen.dart';

class OrderDetailScreen extends StatelessWidget {
  const OrderDetailScreen({super.key, required this.order});
  final OrderModel? order;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          leadingWidth: 0,
          elevation: 0,
          toolbarHeight: 60,
          automaticallyImplyLeading: false,
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  InkWell(
                    onTap: () => Get.back(),
                    child: Icon(
                      Icons.arrow_back,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(width: 27),
                  SizedBox(
                    width: Get.width * 0.5,
                    child: const Text(
                      "Order Details",
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        color: Colors.black,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                width: 50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        // if (!await launchUrl(Uri.parse(url()))) {
                        //   throw "Could not launch whatsapp";
                        // }
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SizedBox(
                          width: 30,
                          child: getSvgIcon(
                            "assets/svg/customer-support.svg",
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          bottom: const PreferredSize(
              preferredSize: Size.fromHeight(10), child: ThickDivider()),
        ),
        body: Obx(
          () => order == null
              ? Center(
                  child: CircularProgressIndicator.adaptive(
                    strokeWidth: 4,
                    backgroundColor: AppColors.kgrey,
                    valueColor:
                        AlwaysStoppedAnimation(Theme.of(context).primaryColor),
                  ),
                )
              : SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 10),
                          Text(
                            'Order ID :  RAPS-${order?.salesOrderNumber}',
                            style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                color: Colors.black.withOpacity(0.7)),
                          ),
                          const SizedBox(height: 10),
                        ],
                      ).paddingSymmetric(horizontal: 16),
                      const ThickDivider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Ordered Product (${order?.items?.length} item)',
                            style: const TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 16),
                          ),
                          InkWell(
                            onTap: () {
                              // Get.to(() => ViewProducts(
                              //       listitems: orderController.orderDetailModel
                              //           .value.data!.orderDetails!,
                              //     ));
                            },
                            child: Container(
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Theme.of(context).primaryColor)),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    'View',
                                    style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w500),
                                  ),
                                )),
                          )
                        ],
                      ).paddingSymmetric(horizontal: 16, vertical: 14),
                      const ThickDivider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 16),
                        child: ListView(
                          primary: false,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            TimelineTile(
                              // alignment: TimelineAlign.manual,
                              lineXY: 0.1,
                              isFirst: true,
                              indicatorStyle: IndicatorStyle(
                                width: 40,
                                height: 40,
                                color: Theme.of(context).primaryColor,
                                padding: const EdgeInsets.all(2),
                                indicator: SvgPicture.asset(
                                  "assets/svg/${!false ? 'order-placed-pending' : 'order-placed-done'}.svg",
                                  fit: BoxFit.scaleDown,
                                ),
                              ),
                              endChild: _RightChild(
                                // asset: 'images/coin.png',
                                title: 'Order Placed',

                                message: false
                                    ? 'Order placed at ${DateFormat('dd MMM yyyy hh:mma').format(order!.orderDate!)}'
                                    : 'Pending',
                              ),
                              beforeLineStyle: LineStyle(
                                color: false
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[300]!,
                              ),
                            ),
                            false
                                ? const SizedBox()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: Theme.of(context).primaryColor,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!false ? 'order-confirmed-pending' : 'order-confirmed-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: const _RightChild(
                                      title: 'Order confirmed',
                                      disabled: false,
                                      message: false
                                          ? 'Your order is confirmed and will be delivered soon.'
                                          : 'Pending',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: false
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey[300]!,
                                    ),
                                  ),
                            false && !false
                                ? const SizedBox.shrink()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: Theme.of(context).primaryColor,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!false ? 'order-shipped-pending' : 'order-shipped-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: const _RightChild(
                                      title: 'Order Packed',
                                      disabled: !false,
                                      message: false
                                          ? 'Your order has been packed.'
                                          : 'Pending',
                                      //  singleOrder.data!.orderLogs!.any(
                                      //         (element) => element.status == 'ARRIVED')
                                      //     ? 'Your order Shipped at ${singleOrder.data!.orderLogs!.where((element) => element.status == 'ARRIVED').first.meta.arrivalTime}'
                                      //     : 'Pending',
                                    ),
                                    // beforeLineStyle: LineStyle(
                                    //   color: orderController.orderDetailModel
                                    //           .value.data!.orderLogs!
                                    //           .any((element) =>
                                    //               element.status == 'packed')
                                    //       ? Theme.of(context).primaryColor
                                    //       : Colors.grey[300]!,
                                    // ),
                                    // afterLineStyle: const LineStyle(
                                    //   color: Colors.red,
                                    // ),
                                  ),
                            false && !false && !false
                                ? const SizedBox.shrink()
                                : Row(
                                    children: [
                                      Expanded(
                                        child: TimelineTile(
                                          // alignment: TimelineAlign.manual,
                                          lineXY: 0.1,
                                          indicatorStyle: IndicatorStyle(
                                            width: 40,
                                            height: 40,
                                            color:
                                                Theme.of(context).primaryColor,
                                            padding: const EdgeInsets.all(2),
                                            indicator: SvgPicture.asset(
                                              "assets/svg/${!false ? 'out-for-delivery-pending' : 'out-for-delivery-done'}.svg",
                                              fit: BoxFit.scaleDown,
                                            ),
                                          ),
                                          endChild: _RightChild(
                                            disabled: !false,
                                            title: 'Out for Delivery',
                                            message:
                                                order?.status?.toLowerCase() ==
                                                        'out_for_delivery'
                                                    ? '<delivery-agent-name>'
                                                    : 'Pending',
                                          ),
                                          beforeLineStyle: LineStyle(
                                            color: false
                                                ? Theme.of(context).primaryColor
                                                : Colors.grey[300]!,
                                          ),
                                        ),
                                      ),
                                      // order?.status?.toLowerCase() ==
                                      //             "out_for_delivery" ||
                                      //         order?.status?.toLowerCase() ==
                                      //             "delivered"
                                      //     ? IconButton(
                                      //         onPressed: () async {
                                      //           if (!await launchUrl(Uri.parse(
                                      //               "tel: ${order!.orderLogs!.where((element) => element.status == 'out_for_delivery').first.meta!.riderContact.toString()}"))) {
                                      //             throw "Could not launch https://rapsap.com/terms.html";
                                      //           }
                                      //         },
                                      //         icon: const Icon(Icons.phone),
                                      //       )
                                      //     : const SizedBox.shrink()
                                    ],
                                  ),
                            false
                                ? const SizedBox.shrink()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    isLast: true,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: Theme.of(context).primaryColor,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!false ? 'order-deliverd-pending' : 'order-deliverd-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: _RightChild(
                                      disabled: !false,
                                      title: 'Order Delivered',
                                      message: order?.status?.toLowerCase() ==
                                              'delivered'
                                          ? 'delivered'
                                          : 'Pending',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: false
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey[300]!,
                                    ),
                                  ),
                            order?.status?.toLowerCase() == 'order_cancelled'
                                ? TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    isLast: true,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 30,
                                      color: Colors.red.withOpacity(0.5),
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/order-cancelled-done.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: const _RightChild(
                                      title: 'Order Cancelled',
                                      message: 'CANCELLED',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: Colors.red.withOpacity(0.5),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ],
                        ),
                      ),
                      const ThickDivider(),
                      const SizedBox(height: 10),
                      canCancel(order?.status?.toLowerCase() ?? '')
                          ? const SizedBox.shrink()
                          : InkWell(
                              onTap: () {
                                Get.to(() => CancelScreen());
                              },
                              child: Column(
                                children: [
                                  const Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Cancel Order',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 15,
                                      )
                                    ],
                                  ).paddingSymmetric(horizontal: 16),
                                  const SizedBox(height: 10),
                                  const ThickDivider(),
                                ],
                              )),
                      const SizedBox(height: 10),
                      order?.status?.toLowerCase() == 'out_for_delivery'
                          ? Column(
                              children: [
                                const Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          'Delivery Agent :  ',
                                          style: TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          '<delivery-agent-name>',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Icon(
                                      Icons.phone_outlined,
                                      size: 22,
                                      color: Colors.green,
                                    ),
                                  ],
                                ).paddingSymmetric(horizontal: 16),
                                const SizedBox(height: 10),
                              ],
                            )
                          : const SizedBox.shrink(),
                      // order!.refund ==
                      //         null
                      //     ? const SizedBox.shrink()
                      //     : Column(
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           const Text(
                      //             'Refund Details',
                      //             style: TextStyle(
                      //               color: AppColors.kblack,
                      //               fontSize: 18,
                      //               fontWeight: FontWeight.w600,
                      //             ),
                      //           ).paddingSymmetric(horizontal: 16),
                      //           ...List.generate(
                      //             order!
                      //                 .refund!.length,
                      //             (index) {
                      //               Refund refund = orderController
                      //                   .orderDetailModel
                      //                   .value
                      //                   .data!
                      //                   .refund![index];

                      //               return Container(
                      //                 child: Column(
                      //                   crossAxisAlignment:
                      //                       CrossAxisAlignment.start,
                      //                   children: [
                      //                     hgap(15),
                      //                     Text(
                      //                       refund.status == "processed"
                      //                           ? "Refund Completed"
                      //                           : refund.status == "failed"
                      //                               ? "failed"
                      //                               : "Initiated",
                      //                       style: TextStyle(
                      //                         color: refund.status == "failed"
                      //                             ? Colors.red
                      //                             : Colors.green,
                      //                         fontSize: 16,
                      //                         fontWeight: FontWeight.w600,
                      //                       ),
                      //                     ),
                      //                     Text(
                      //                       "₹ ${refund.amount}  ${refund.status == "processed" ? " has been refunded" : refund.status == "refund request has been failed" ? "failed" : " has been initiated"} on ${getDate(refund.updatedAt.toString())} ",
                      //                       style: const TextStyle(
                      //                         color: AppColors.kblack,
                      //                         fontSize: 15,
                      //                         fontWeight: FontWeight.w500,
                      //                       ),
                      //                     ).paddingSymmetric(vertical: 10),
                      //                     const Divider()
                      //                   ],
                      //                 ).paddingSymmetric(horizontal: 16),
                      //               );
                      //             },
                      //           ),
                      //           const ThickDivider(),
                      //         ],
                      //       ),
                      const Text(
                        'Delivery Address',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ).paddingSymmetric(horizontal: 16),
                      const SizedBox(height: 20),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Row(
                          //   children: [
                          //     Text(
                          //       order?. ?? "",
                          //       style: const TextStyle(
                          //           fontWeight: FontWeight.w600),
                          //     ),
                          //     SizedBox(width: 20),
                          //     Container(
                          //       decoration: BoxDecoration(
                          //           borderRadius: BorderRadius.circular(4),
                          //           color: Theme.of(context).primaryColor.withOpacity(0.2)),
                          //       child: Padding(
                          //         padding: const EdgeInsets.symmetric(
                          //             horizontal: 8.0, vertical: 4),
                          //         child: Text(
                          //           order!.deliveryAddress!.addressType ?? "",
                          //           style: const TextStyle(color: Theme.of(context).primaryColor),
                          //         ),
                          //       ),
                          //     )
                          //   ],
                          // ),
                          const SizedBox(height: 5),
                          Text(
                            order?.address?.toString() ?? '',
                            style: const TextStyle(color: Color(0xFF556F80)),
                            textAlign: TextAlign.start,
                          ),
                        ],
                      ).paddingSymmetric(horizontal: 16),
                      const SizedBox(height: 10),
                      const ThickDivider(),
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Price Details',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 18),
                            ),
                            const SizedBox(height: 20),
                            PriceRowsWidget(
                              amount: '\u20b9${order?.items?.fold<double>(
                                0.0,
                                (prev, e) =>
                                    prev +
                                    ((double.tryParse(e.rate ?? '0.0') ?? 0.0) *
                                        (double.tryParse(e.quantity ?? "0.0") ??
                                            0.0)),
                              )}',
                              title: 'Price(${order?.items?.length} Items)',
                            ),
                            const SizedBox(height: 16),
                            // PriceRowsWidget(
                            //   amount: '${order?.items?.fold<double>(
                            //         0.0,
                            //         (prev, e) =>
                            //             prev +
                            //             ((double.tryParse(e.rate ?? '0.0') ??
                            //                     0.0) *
                            //                 (double.tryParse(
                            //                         e.quantity ?? "0.0") ??
                            //                     0.0)),
                            //       )}',
                            //   title: 'Discount',
                            // ),
                            // hgap(16),
                            // PriceRowsWidget(
                            //   amount: '${order?.deliveryCost ?? 0}',
                            //   title: 'Delivery fee',
                            // ),
                            // hgap(16),
                            const PriceRowsWidget(
                              amount: '0',
                              title: 'Taxes',
                            ),
                            Divider(
                              thickness: 1,
                              color: Colors.black.withOpacity(0.06),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Total Amount',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                Text(
                                  '\u20b9${order?.total}',
                                  style: GoogleFonts.inter(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700),
                                ),
                              ],
                            ).paddingSymmetric(vertical: 20),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        ));
  }

  // String url() {
  //   if (Platform.isIOS) {
  //     return "whatsapp://wa.me/919867870802?text=Enquiry on RAPS-$orderId";
  //   } else {
  //     return "whatsapp://send?phone=919867870802&text=Enquiry on  RAPS-$orderId";
  //   }
  // }

  bool isChecked(status) {
    return Get.find<OrderController>()
        .orderDetailModel
        .value
        .data!
        .orderLogs!
        .any((element) => element.status == status);
  }
}

String? getDate(String date) {
  return DateFormat('dd-MMM-yyyy hh:mm')
      .format(DateTime.parse(date))
      .toString();
}

class ThickDivider extends StatelessWidget {
  const ThickDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: Colors.black.withOpacity(0.06),
      thickness: 4,
    );
  }
}

bool canCancel(status) {
  // return singleOrder.data!.orderLogs!
  // .any((element) => element.status == status);

  switch (status) {
    case 'received':
      return true;
    case 'order_updated':
    case 'order_paid':
    case 'ARRIVED':
    case 'packed':
    case 'DISPATCHED':
    case 'delivered':
    case 'arrived':
    case 'reschedule':
    case 'out_for_delivery':
    case 'CANCELLED':
    case 'order_cancelled':
    default:
      return false;
  }
}

class _RightChild extends StatelessWidget {
  const _RightChild({
    this.title = '',
    this.message = '',
    this.disabled = false,
  });

  final String title;
  final String message;
  final bool disabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            title,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            message,
            softWrap: true,
            style: TextStyle(
              color: Colors.black.withOpacity(0.5),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
