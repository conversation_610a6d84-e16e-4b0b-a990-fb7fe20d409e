// ignore_for_file: library_private_types_in_public_api, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../controllers/cart_controlller.dart';
import '../../../controllers/order_controller.dart';
import '../../../model/database_models/database_models.dart';
import '../../../model/order/order_list_model/order_list_model.dart';
import '../cart/cart_screen.dart';
import '../login/widgets/button.dart';
import '../root_page/root_page.dart';
import '../../../services/database_helper.dart';
import '../../../services/firebase_services.dart';
import 'pulsating_circle_iconbutton.dart';
import 'my_orders.dart';

class SuccessPage extends StatefulWidget {
  const SuccessPage({super.key});

  @override
  _SuccessPageState createState() => _SuccessPageState();
}

class _SuccessPageState extends State<SuccessPage> {
  final CartController cartController = Get.find<CartController>();
  final OrderController orderController = Get.find<OrderController>();

  String orderid = '';
  @override
  void initState() {
    HapticFeedback.vibrate();
    super.initState();
    getOrderDetails();
  }

  Future getOrderDetails() async {
    List<LocalOrder> odrList = await DatabaseHelper.instance.getDbOrder();

    setState(() {
      orderid = odrList[0].orderID.toString();
    });
    // final everyitem = cartController.myCartItems.map((e) {
    //   return AnalyticsEventItem(
    //       currency: "INR",
    //       itemId: e.id.toString(),
    //       itemName: e.name,
    //       price: e.price,
    //       quantity: e.qty);
    // }).toList();

    // await FirebaseService.firebaseAnalytics.logPurchase(
    //     currency: "INR",
    //     items: everyitem,
    //     coupon: _order.couponCode,
    //     value: cartController.totalprice.value);
    await FirebaseService.firebaseAnalytics.logEvent(
        name: 'Purchase_done',
        parameters: {'amount': cartController.totalprice.value});

    await DatabaseHelper.instance.clearAllOrders();
    await DatabaseHelper.instance.clearShopingCart();
    await DatabaseHelper.instance.clearPayment();
    cartController.myCartItems.clear();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: (() async {
        Get.offUntil(
            GetPageRoute(page: () => const CartScreen()), (route) => false);
        return false;
      }),
      child: Scaffold(
        body: SafeArea(
          child: Container(
            padding: const EdgeInsets.all(25),
            // color: Color(0xFFF3F4F9),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                PulsatingCircleIconButton(
                  onTap: () {},
                  icon: const Icon(
                    Icons.check_sharp,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
                const SizedBox(height: 50),
                const Text(
                  'Your Order has been\n placed Successfully!',
                  style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87),
                ),
                Align(
                  alignment: Alignment.center,
                  heightFactor: 1.5,
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      text: 'Your item has been placed and is on',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        height: 1.3,
                      ),
                      children: [
                        const TextSpan(
                          text: '\nit\'s way to being processed.',
                        ),
                        const TextSpan(
                          text: '\nOrder id ',
                        ),
                        // ignore: unnecessary_null_comparison

                        TextSpan(
                          text: '#${orderid.toString()}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: SubmitButton(
                        text: 'My Orders',
                        onpressed: () {
                          orderController.orderListModel.value =
                              OrderListModel();
                          orderController.getOrders();
                          Get.to(() => MyOrders());
                        },
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 20),
                // Row(
                //   children: [
                //     Expanded(
                //       child: SubmitButton(
                //           bgcolor: Colors.white,
                //           side: Border.all(color: kblack),
                //           text: 'My Subscriptions',
                //           txtcolor: kblack,
                //           onpress: () {
                //             Get.to(() => SubscriptionCommingSoon());
                //           }),
                //     )
                //   ],
                // ),
                // kheight10,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: () async {
                        await DatabaseHelper.instance.clearAllOrders();
                        await DatabaseHelper.instance.clearShopingCart();
                        await DatabaseHelper.instance.clearPayment();

                        Get.to(() => const RootPage());
                        cartController.myCartItems.clear();
                      },
                      child: Text(
                        "Continue Shopping",
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
