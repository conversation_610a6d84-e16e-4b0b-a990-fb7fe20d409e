import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/colors.dart';
import '../../widgets/decorated_card.dart';

class OrderDeliveredScreen extends StatelessWidget {
  const OrderDeliveredScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        toolbarHeight: Get.height * 0.1,
        flexibleSpace: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Order  No. #1234578",
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                        height: 18 / 14,
                        letterSpacing: -1,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16 / 2),
                    Text(
                      "9 : 15 pm 3 Items, Rs . 1234",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        height: 18 / 12,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                  ],
                ),
              ),
              CloseButton(
                onPressed: () {
                  Get.back();
                },
              )
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              "assets/images/order_delivered_confirmation_bg.webp",
              // height: 343,
              // width: double.infinity,
              fit: BoxFit.cover,
            ),
            DecoratedCard(
              margin: const EdgeInsets.fromLTRB(
                16 / 2,
                16,
                16 / 2,
                16 / 2,
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 16),
                  const Text(
                    "Your Order has been Delivered",
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                      height: 1,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 16 / 2),
                  Text(
                    "Take some time to rate your delivery, which would help us to serve you better",
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      height: 18 / 12,
                      color: AppColors.kgrey,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: List.generate(
                        5,
                        (index) => const Icon(
                              Icons.star_outline_rounded,
                              size: 16 * 2,
                            )),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 58,
              child: DecoratedCard(
                color: Colors.red.withOpacity(0.15),
                margin: const EdgeInsets.symmetric(horizontal: 16 / 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "Not Delivered? Call the delivery Executive",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                        height: 15.6 / 12,
                        color: Colors.red,
                      ),
                    ),
                    InkWell(
                      onTap: () {},
                      child: const Icon(
                        Icons.phone,
                        color: Colors.red,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
