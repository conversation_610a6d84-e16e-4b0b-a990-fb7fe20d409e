import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../controllers/order_controller.dart';
import 'tracking_order.dart';

class MinimisedOrderTrackingWidget extends StatefulWidget {
  const MinimisedOrderTrackingWidget({super.key});

  @override
  State<MinimisedOrderTrackingWidget> createState() =>
      _MinimisedOrderTrackingWidgetState();
}

class _MinimisedOrderTrackingWidgetState
    extends State<MinimisedOrderTrackingWidget> {
  int page = 0;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderController>(
      builder: (controller) => Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            color: const Color(0xff081854),
            height: 90,
            child: PageView.builder(
              onPageChanged: (value) => setState(() {
                page = value;
              }),
              padEnds: false,
              itemCount: controller.currentOrders.length,
              itemBuilder: (context, index) => Padding(
                padding: const EdgeInsets.fromLTRB(
                  16,
                  16,
                  16,
                  28,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // order sales order number
                          Text(
                            "Order No. ${controller.currentOrders.entries.elementAt(index).value?.salesOrderNumber}",
                            style: GoogleFonts.leagueSpartan(
                              fontWeight: FontWeight.w300,
                              fontSize: 10,
                              height: 9.2 / 10,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 10),

                          // estimated delivery time
                          Text.rich(
                            TextSpan(
                              text: "Your order will be delivered in ",
                              children: [
                                TextSpan(
                                  text:
                                      "${controller.currentOrders.entries.elementAt(index).value?.estDeliveryTime?.toString() ?? controller.currentOrders.entries.elementAt(index).value?.getETAbyStatus ?? ''} mins",
                                  style: GoogleFonts.leagueSpartan(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            maxLines: 2,
                            style: GoogleFonts.leagueSpartan(
                              fontWeight: FontWeight.w300,
                              fontSize: 14,
                              height: 12.88 / 14,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Get.to(() => TrackOrderScreen(
                              orderId: controller.currentOrders.entries
                                  .elementAt(index)
                                  .value!
                                  .id!));
                        },
                        borderRadius: BorderRadius.circular(4),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: Theme.of(context).primaryColor),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            "View Details",
                            style: GoogleFonts.leagueSpartan(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              height: 12.88 / 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Visibility(
            visible: controller.currentOrders.length > 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                controller.currentOrders.length,
                (index) => Container(
                  height: 7,
                  width: 7,
                  decoration: ShapeDecoration(
                    shape: const CircleBorder(),
                    color:
                        page == index ? Colors.white : const Color(0xff6F8080),
                  ),
                  margin: const EdgeInsets.symmetric(
                      horizontal: 7 / 2, vertical: 8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
