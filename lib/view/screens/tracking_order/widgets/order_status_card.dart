import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart' as l;

import '../../../../utils/colors.dart';
import '../../../widgets/decorated_card.dart';

class OrderStatusCard extends StatelessWidget {
  const OrderStatusCard({
    super.key,
    required this.index,
  });

  final int index;
  @override
  Widget build(BuildContext context) {
    return DecoratedCard(
      margin: const EdgeInsets.symmetric(
        horizontal: 3,
        vertical: 16 / 2,
      ),
      borderRadius: 20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Order Status",
            textAlign: TextAlign.start,
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              height: 1,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 24),
          // icon row
          Stack(
            children: [
              Container(
                height: 60,
                alignment: Alignment.center,
                child: Container(
                  height: 3,
                  color: AppColors.kgrey,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 0
                  Material(
                    color: Colors.white,
                    shape: const CircleBorder(),
                    child: Container(
                      height: index == 0 ? 60 : 40,
                      width: index == 0 ? 60 : 40,
                      decoration: BoxDecoration(
                        color: index == 0
                            ? Theme.of(context).primaryColor
                            : index > 0
                                ? AppColors.kHomeAppBarBG
                                : AppColors.kgrey,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: index == 0
                          ? l.Lottie.asset(
                              "assets/json/order_received_status_lottie.json",
                              height: 36,
                            )
                          : SvgPicture.asset(
                              "assets/svg/order_received.svg",
                              height: index == 0 ? 36 : 24,
                              colorFilter: ColorFilter.mode(
                                index == 0
                                    ? Colors.white
                                    : index > 0
                                        ? Theme.of(context).primaryColor
                                        : Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
                  // 1
                  Material(
                    color: Colors.white,
                    shape: const CircleBorder(),
                    child: Container(
                      height: index == 1 ? 60 : 40,
                      width: index == 1 ? 60 : 40,
                      decoration: BoxDecoration(
                        color: index == 1
                            ? Theme.of(context).primaryColor
                            : index > 1
                                ? AppColors.kHomeAppBarBG
                                : AppColors.kgrey,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: index == 1
                          ? l.Lottie.asset(
                              "assets/json/order_processing_status_lottie.json",
                              height: 36,
                            )
                          : SvgPicture.asset(
                              "assets/svg/order_processing.svg",
                              height: index == 1 ? 36 : 24,
                              colorFilter: ColorFilter.mode(
                                index == 1
                                    ? Colors.white
                                    : index > 1
                                        ? Theme.of(context).primaryColor
                                        : Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
                  // 2
                  Material(
                    color: Colors.white,
                    shape: const CircleBorder(),
                    child: Container(
                        height: index == 2 ? 60 : 40,
                        width: index == 2 ? 60 : 40,
                        decoration: BoxDecoration(
                          color: index == 2
                              ? Theme.of(context).primaryColor
                              : index > 2
                                  ? AppColors.kHomeAppBarBG
                                  : AppColors.kgrey,
                          shape: BoxShape.circle,
                        ),
                        alignment: Alignment.center,
                        child: Container(
                          margin: const EdgeInsets.all(8),
                          child: index == 2
                              ? l.Lottie.asset(
                                  "assets/json/order_packed_status_lottie.json",
                                  height: 36,
                                )
                              : SvgPicture.asset(
                                  "assets/svg/packed.svg",
                                  height: index == 2 ? 36 : 24,
                                  width: index == 2 ? 36 : 24,
                                  colorFilter: ColorFilter.mode(
                                    index == 2
                                        ? Colors.white
                                        : index > 2
                                            ? Theme.of(context).primaryColor
                                            : Colors.black,
                                    BlendMode.srcIn,
                                  ),
                                ),
                        )),
                  ),
                  // 3
                  Material(
                    color: Colors.white,
                    shape: const CircleBorder(),
                    child: Container(
                      height: index == 3 ? 60 : 40,
                      width: index == 3 ? 60 : 40,
                      decoration: BoxDecoration(
                        color: index == 3
                            ? Theme.of(context).primaryColor
                            : index > 3
                                ? AppColors.kHomeAppBarBG
                                : AppColors.kgrey,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: index == 3
                          ? l.Lottie.asset(
                              "assets/json/order_on_the_way_status_lottie.json",
                              height: 36,
                            )
                          : SvgPicture.asset(
                              "assets/svg/order_on_the_way.svg",
                              height: index == 3 ? 36 : 24,
                              colorFilter: ColorFilter.mode(
                                index == 3
                                    ? Colors.white
                                    : index > 3
                                        ? Theme.of(context).primaryColor
                                        : Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
                  // 4
                  Material(
                    color: Colors.white,
                    shape: const CircleBorder(),
                    child: Container(
                      height: index == 4 ? 60 : 40,
                      width: index == 4 ? 60 : 40,
                      decoration: BoxDecoration(
                        color: index == 4
                            ? Theme.of(context).primaryColor
                            : index > 4
                                ? AppColors.kHomeAppBarBG
                                : AppColors.kgrey,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: index == 4
                          ? l.Lottie.asset(
                              "assets/json/ordered_delivered_status_lottie.json",
                              height: 36,
                            )
                          : SvgPicture.asset(
                              "assets/svg/order_delivered.svg",
                              height: index == 4 ? 36 : 24,
                              colorFilter: ColorFilter.mode(
                                index == 4
                                    ? Colors.white
                                    : index > 4
                                        ? Theme.of(context).primaryColor
                                        : Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
                ],
              )
            ],
          ),
          const SizedBox(height: 6),

          // text row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              "Order\nReceived",
              "Order\nProcessing",
              "Order\nPacked",
              "Order on\nthe way",
              "Order\nDelivered",
            ]
                .asMap()
                .entries
                .map(
                  (e) => Text(
                    e.value,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      height: 18 / 12,
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }
}
