import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/colors.dart';
import '../../widgets/widgets.dart';

class CancelOrderReasonSheet extends StatefulWidget {
  const CancelOrderReasonSheet({
    super.key,
  });

  @override
  State<CancelOrderReasonSheet> createState() => _CancelOrderReasonSheetState();
}

class _CancelOrderReasonSheetState extends State<CancelOrderReasonSheet> {
  String? selectedReason;
  String customReason = "";
  final List<String> reasonsForCancellingOrder = const [
    "Dissatisfied with delivery time or options",
    "I was trying out Rapsap",
    "No longer need the items",
    "Mistakenly ordered the wrong items",
    "Decided to shop in-store instead",
    "Selected wrong address",
    "Not available to receive the order",
    "Other",
  ];
  final GlobalKey<FormState> _formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
      builder: (
        context,
      ) =>
          KeyboardHider(
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: ListView(
            shrinkWrap: true,
            children: [
              Container(
                padding: const EdgeInsets.only(
                  top: 16 / 2,
                  right: 16 / 2,
                ),
                alignment: Alignment.topRight,
                child: IconButton(
                  onPressed: () {
                    Get.close(1);
                  },
                  icon: const Icon(
                    Icons.close,
                    color: Colors.black,
                  ),
                ),
              ),
              const Text(
                "Why do you want to cancel the order?",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 18,
                  height: 23.4 / 18,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16 / 2),
              Text(
                "Let us know, so we can improve.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  height: 18.2 / 14,
                  color: AppColors.kgreyDark,
                ),
              ),
              const SizedBox(height: 24),
              ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(horizontal: 16 / 2),
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return Container(
                    color: Colors.white,
                    child: RadioListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16 / 4,
                      ),
                      controlAffinity: ListTileControlAffinity.trailing,
                      activeColor: Theme.of(context).primaryColor,
                      title: Text(
                        reasonsForCancellingOrder.elementAt(index),
                        maxLines: 1,
                        style: const TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                          height: 20.8 / 16,
                        ),
                      ),
                      value: reasonsForCancellingOrder.elementAt(index),
                      groupValue: selectedReason,
                      onChanged: (String? value) {
                        setState(() {
                          selectedReason =
                              reasonsForCancellingOrder.elementAt(index);
                        });
                      },
                    ),
                  );
                },
                separatorBuilder: (context, index) => const Divider(
                  height: 1,
                ),
                itemCount: reasonsForCancellingOrder.length,
              ),
              const SizedBox(height: 16),
              Visibility(
                visible: selectedReason == reasonsForCancellingOrder.last,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16 / 2),
                  child: Form(
                    key: _formKey,
                    child: TextFormField(
                      onChanged: (value) => customReason = value,
                      validator: (value) =>
                          value?.isEmpty ?? true ? "required" : null,
                      decoration: InputDecoration(
                        hintText: "Enter Reason",
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 16),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.zero,
                          borderSide:
                              BorderSide(color: Theme.of(context).primaryColor),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.zero,
                          borderSide:
                              BorderSide(color: Theme.of(context).primaryColor),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.all(16),
                      child: ElevatedButton(
                        onPressed: selectedReason != null
                            ? () {
                                if (selectedReason !=
                                    reasonsForCancellingOrder.last) {
                                  Get.back<String>(result: selectedReason);
                                } else {
                                  if (_formKey.currentState?.validate() ??
                                      false) {
                                    Get.back<String>(result: customReason);
                                  }
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: AppColors.ctaBackgroundColor,
                          foregroundColor: AppColors.ctaTextColor,
                          textStyle: const TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 18,
                            height: 23.4 / 18,
                            letterSpacing: -1,
                          ),
                        ),
                        child: const Text("Cancel Order"),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      onClosing: () {},
    );
  }
}
