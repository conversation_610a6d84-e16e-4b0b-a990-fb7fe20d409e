import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart' as l;
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../controllers/order_controller.dart';
import '../../../model/single_order.dart';
import '../../../utils/colors.dart';
import '../../../utils/enumerations.dart';
// import '../account/coupon_won_screen.dart';
import '../cart/price_row_widget.dart';
import '../../widgets/decorated_card.dart';
import 'cancel_order_reason_sheet.dart';
import 'widgets/order_status_card.dart';
// import 'order_delivered_screen.dart';

class TrackOrderScreen extends StatefulWidget {
  final String orderId;
  const TrackOrderScreen({super.key, required this.orderId});

  @override
  State<TrackOrderScreen> createState() => _TrackOrderScreenState();
}

class _TrackOrderScreenState extends State<TrackOrderScreen>
    with WidgetsBindingObserver {
  final Completer<GoogleMapController> _completer = Completer();
  late LatLng currentPosition;
  CameraPosition _kGooglePlex = const CameraPosition(
    target: LatLng(19.**************, 72.**************),
    zoom: 14,
  );
  List<Marker> allMarker = [];

  bool areOrderDetailsVisible = false;
  final OrderController orderController = Get.find<OrderController>();
  SingleOrder? order;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      refreshCurrentOrder();
    });
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    await getPosition();
    setState(() {
      _kGooglePlex = CameraPosition(
        target: LatLng(currentPosition.latitude, currentPosition.longitude),
        zoom: 17,
      );
    });
    allMarker.add(Marker(
      markerId: const MarkerId('MyMarker'),
      consumeTapEvents: true,
      // draggable: true,
      // onDragEnd: (LatLng latLng) {
      //   print('Moved here ${latLng}');
      //   setState(() {
      //     currentPosition = latLng;
      //   });
      //   getAddressFromGeo(latLng);
      // },
      position: currentPosition,
    ));
  }

  void refreshCurrentOrder() {
    orderController.getCurrentOrders().then(
      (_) async {
        order = orderController.currentOrders[widget.orderId] ??
            await orderController.getOrderbyId(widget.orderId);
        setState(() {});
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      refreshCurrentOrder();
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderController>(
      builder: (_) {
        return Scaffold(
          body: Stack(
            children: [
              _buildBackgroundImage(),
              _buildOrderCardTop(),
              _buildBottomSheet(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBackgroundImage() {
    final statusIndex =
        orderController.getIndexFromStatus(order?.status?.toLowerCase());
    return IndexedStack(
      sizing: StackFit.expand,
      index: statusIndex,
      children: [
        _buildImageBackground("assets/gif/order_received_bg.webp", BoxFit.fill),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.asset(
                    "assets/images/order_processing_bg.webp",
                    fit: BoxFit.fill,
                  ),
                  l.Lottie.asset(
                    "assets/json/order_processing_bg_lottie.json",
                    fit: BoxFit.fill,
                  ),
                ],
              ),
            ),
            const Spacer()
          ],
        ),
        _buildImageBackground("assets/gif/order_packed_bg.webp", BoxFit.cover),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: mapWidget(),
            ),
            const Spacer()
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: l.Lottie.asset(
                "assets/json/ordered_delivered_bg_lottie.json",
                fit: BoxFit.fill,
              ),
            ),
            const Spacer()
          ],
        ),
      ],
    );
  }

  Widget _buildImageBackground(String assetPath, BoxFit? fit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: Image.asset(assetPath, fit: fit),
        ),
        const Spacer()
      ],
    );
  }

  DraggableScrollableSheet _buildBottomSheet() {
    return DraggableScrollableSheet(
      maxChildSize: 0.8,
      initialChildSize: 0.5,
      minChildSize: 0.5,
      builder: (BuildContext context, sc) => Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.only(top: Get.height * 0.05),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
            ),
            // height: Get.height * 0.55,
            child: Column(
              children: [
                Expanded(
                  child: RefreshIndicator(
                    color: Theme.of(context).primaryColor,
                    backgroundColor: Colors.white,
                    onRefresh: () async {
                      refreshCurrentOrder();
                    },
                    child: ListView(
                      physics: const ClampingScrollPhysics(),
                      controller: sc,
                      shrinkWrap: true,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16 / 2,
                        vertical: 16 * 2,
                      ),
                      children: [
                        OrderStatusCard(
                          index: orderController
                              .getIndexFromStatus(order?.status?.toLowerCase()),
                        ),
                        orderDetailsCard(),

                        // delivery address
                        DecoratedCard(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 3,
                            vertical: 16 / 2,
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Delivering to",
                                style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16,
                                  height: 20.8 / 16,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          order?.address?.type ?? '',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: 14,
                                            height: 18 / 14,
                                            letterSpacing: -1,
                                            color: Colors.black,
                                          ),
                                        ),
                                        const SizedBox(height: 16 / 2),
                                        Text(
                                          [
                                            order?.address?.house ?? '',
                                            order?.address?.street ?? '',
                                            order?.address?.landmark ?? '',
                                            order?.address?.city ?? '',
                                            order?.address?.pincode ?? '',
                                          ].join(', '),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12,
                                            height: 15.6 / 12,
                                            color: AppColors.kgreyDark,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Visibility(
                                    visible: order?.status == 'received',
                                    child: InkWell(
                                      onTap: () {},
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColors
                                                  .secondaryBtnBorderColor),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'Change',
                                          style: TextStyle(
                                            color: AppColors
                                                .secondaryBtnForegroundColor,
                                            fontSize: 14,
                                            height: 16.94 / 14,
                                            letterSpacing: -1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // cancel order
                        Visibility(
                          visible: order?.status == 'received',
                          child: DecoratedCard(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 3,
                              vertical: 16 / 2,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "Cancel My Order",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16,
                                    height: 20.8 / 16,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 16 / 2),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "Cancel order before it is packed",
                                        maxLines: 2,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 12,
                                          height: 15.6 / 12,
                                          color: AppColors.kgreyDark,
                                        ),
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () async {
                                        // ignore: unused_local_variable
                                        String? reasonToCancelOrder =
                                            await showModalBottomSheet(
                                          isScrollControlled: true,
                                          context: context,
                                          builder: (context) =>
                                              const CancelOrderReasonSheet(),
                                        );
                                        if (reasonToCancelOrder?.isNotEmpty ??
                                            false) {
                                          final result =
                                              await orderController.cancelOrder(
                                                  orderId: order?.id ?? '');
                                          if (result) {
                                            orderController.getCurrentOrders();
                                            Get.back();
                                          }
                                        }
                                      },
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.red,
                                        textStyle: GoogleFonts.inter(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                          height: 15.6 / 12,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        padding: const EdgeInsets.all(8),
                                      ),
                                      child: const Text(
                                        "Cancel Order",
                                        maxLines: 2,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          // margin: const EdgeInsets.only(bottom: 50),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "While you await your order...",
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 18,
                                  height: 1,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Image.asset(
                                  "assets/images/trackorder_screen_advertisement.webp")
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: Get.height * (-0.02),
            right: 0,
            left: 0,
            child: SizedBox(
              height: Get.height * 0.1,
              width: Get.width * 0.95,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  deliveryPartnerDetails(),
                  deliveryProgressWidget(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Column paymentMethodDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Payment Method",
          textAlign: TextAlign.start,
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 18,
            height: 1,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order?.paymentMethod ?? "",
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    height: 18.2 / 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 5),
                Visibility(
                  visible: order?.paymentMethod?.toLowerCase() != 'cod',
                  child: Text(
                    "**** **** **** 0007",
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w400,
                      fontSize: 13,
                      height: 16.9 / 13,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Visibility(
              visible: order?.paymentMethod?.toLowerCase() != 'cod',
              child: SvgPicture.asset("assets/svg/visa_logo.svg"),
            )
          ],
        )
      ],
    );
  }

  Column priceDetails() {
    final subtotal = order?.items?.fold<double>(
      0.0,
      (prev, e) =>
          prev + (e.rate?.toDouble() ?? 0.0) * (e.quantity?.toDouble() ?? 0.0),
    );
    final totalDiscount = order?.items?.fold(
      0.0,
      (prev, e) =>
          prev +
          ((e.discount?.type != DiscountType.currency
                  ? (e.rate ?? 0).toDouble() * (e.discount?.value ?? 0.0) / 100
                  : (e.discount?.value ?? 0.0)) *
              (e.quantity?.toDouble() ?? 0.0)),
    );
    final totalTax = order?.items?.fold<double>(
      0.0,
      (prev, e) =>
          prev +
          (e.tax.fold<double>(
            0.0,
            (prev1, e1) =>
                prev1 +
                (((e.rate?.toDouble() ?? 0.0) *
                        (e1.value?.toDouble() ?? 0.0) /
                        100.0) *
                    (e.quantity?.toDouble() ?? 0.0)),
          )),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Price Details",
          textAlign: TextAlign.start,
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 18,
            height: 1,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 24),
        PriceRowsWidget(
            title: "Price (${order?.items?.length} items)",
            amount: "\u20b9$subtotal"),
        const SizedBox(height: 16),
        PriceRowsWidget(
          title: "Discount",
          amount: "- \u20b9$totalDiscount",
          secondColor: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: 16),
        PriceRowsWidget(
          title: "Coupon discount",
          amount: "- \u20b90",
          secondColor: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: 16),
        PriceRowsWidget(
          title: "Delivery fee",
          amount: "Free",
          secondColor: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: 16),
        PriceRowsWidget(
            title: "Taxes", amount: "\u20b9${totalTax?.toPrecision(2)}"),
        const SizedBox(height: 12),
        Divider(
          height: 1,
          thickness: 1,
          color: AppColors.kDecoratedCardBorder,
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Total Amount",
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                height: 20.8 / 14,
                color: Colors.black,
              ),
            ),
            Text(
              "\u20b9${order?.total}",
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                height: 20.8 / 16,
                color: Colors.black,
              ),
            ),
          ],
        )
      ],
    );
  }

  DecoratedCard orderDetailsCard() {
    return DecoratedCard(
      margin: const EdgeInsets.symmetric(
        horizontal: 3,
        vertical: 16 / 2,
      ),
      borderRadius: 20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Order Details (${order?.items?.length} items)",
            textAlign: TextAlign.start,
            style: const TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              height: 1,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: order?.items?.length ?? 0,
            itemBuilder: (context, index) {
              final e = order?.items?.elementAt(index);
              return PriceRowsWidget(
                  title:
                      "${e?.quantity ?? 0} x ${e?.itemId?.name} (${e?.itemId?.weight} ${e?.itemId?.unit})",
                  amount: "\u20b9 ${e?.rate ?? 0}");
            },
            separatorBuilder: (context, _) => const SizedBox(height: 16),
          ),
          Visibility(
            visible: areOrderDetailsVisible,
            replacement: const SizedBox(height: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 24),
                Divider(
                  height: 1,
                  thickness: 1,
                  color: AppColors.kDecoratedCardBorder,
                ),
                const SizedBox(height: 24),
                priceDetails(),
                const SizedBox(height: 24),
                Divider(
                  height: 1,
                  thickness: 1,
                  color: AppColors.kDecoratedCardBorder,
                ),
                const SizedBox(height: 24),
                paymentMethodDetails(),
                const SizedBox(height: 24),
                Divider(
                  height: 1,
                  thickness: 1,
                  color: AppColors.kDecoratedCardBorder,
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              setState(() {
                areOrderDetailsVisible = !areOrderDetailsVisible;
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "View ${areOrderDetailsVisible ? "Less" : "More"}",
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    height: 18.2 / 14,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Icon(
                  areOrderDetailsVisible
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Positioned deliveryProgressWidget() {
    return Positioned(
      top: -50,
      right: 0,
      left: 0,
      child: Center(
        child: Stack(
          children: [
            Container(
              height: 100,
              width: 100,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: Visibility(
                visible: orderController
                        .getIndexFromStatus(order?.status?.toLowerCase()) !=
                    4,
                replacement: Container(
                  height: 70,
                  width: 70,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    color: Color(0xffD8E0FF),
                    shape: BoxShape.circle,
                  ),
                  child: Container(
                    height: 49,
                    width: 49,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      "Delivering in",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 10,
                        height: 18 / 10,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      order?.estDeliveryTime?.toString() ??
                          order?.getETAbyStatus ??
                          '',
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 18,
                        height: 1,
                        color: Colors.black,
                        letterSpacing: -1,
                      ),
                    ),
                    Text(
                      "mins",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        height: 18 / 12,
                        color: AppColors.kgrey,
                        letterSpacing: -1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 100,
              width: 100,
              child: CircularProgressIndicator(
                backgroundColor: AppColors.kgrey,
                valueColor:
                    AlwaysStoppedAnimation(Theme.of(context).primaryColor),
                color: Colors.white,
                value: orderController
                            .getIndexFromStatus(order?.status?.toLowerCase()) ==
                        4
                    ? 1
                    : orderController.getIndexFromStatus(
                                order?.status?.toLowerCase()) ==
                            3
                        ? 0.75
                        : orderController.getIndexFromStatus(
                                    order?.status?.toLowerCase()) ==
                                2
                            ? 0.5
                            : orderController.getIndexFromStatus(
                                        order?.status?.toLowerCase()) ==
                                    1
                                ? 0.25
                                : 0,
                strokeCap: StrokeCap.round,
                strokeWidth: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget deliveryPartnerDetails() {
    return ClipPath(
      clipper: RoundedTrapeziumClipper(),
      child: Container(
        color: Colors.black,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox.shrink(),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16 * 3),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (orderController
                          .getIndexFromStatus(order?.status?.toLowerCase()) ==
                      4)
                    Column(
                      children: [
                        const Text(
                          "Est Time",
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 18 / 12,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          "${order?.estDeliveryTime} mins",
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 18 / 12,
                            color: Colors.white,
                          ),
                        )
                      ],
                    )
                  else if (orderController.getIndexFromStatus(
                              order?.status?.toLowerCase()) ==
                          3 ||
                      orderController.getIndexFromStatus(
                              order?.status?.toLowerCase()) ==
                          2) ...[
                    // CircleAvatar(
                    //   backgroundImage: CachedNetworkImageProvider(
                    //     "https://picsum.photos/50",
                    //     errorListener: (p0) => debugPrint(p0.toString()),
                    //   ),
                    // ),
                    // const SizedBox(width: 5),
                    Visibility(
                      visible: order?.deliveryAgent != null,
                      child: Text(
                        order?.deliveryAgent?.name ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 12,
                          height: 18 / 12,
                          color: Colors.white,
                        ),
                      ),
                    )
                  ],
                  const Spacer(),
                  if (orderController
                          .getIndexFromStatus(order?.status?.toLowerCase()) ==
                      4)
                    Column(
                      children: [
                        const Text(
                          "Delivered in",
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 18 / 12,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          order?.deliveredIn != null
                              ? "${order?.deliveredIn?.inMinutes} mins"
                              : '',
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 18 / 12,
                            color: Theme.of(context).primaryColor,
                          ),
                        )
                      ],
                    )
                  else if (orderController.getIndexFromStatus(
                              order?.status?.toLowerCase()) ==
                          3 ||
                      orderController.getIndexFromStatus(
                              order?.status?.toLowerCase()) ==
                          2)
                    InkWell(
                      onTap: () async {
                        String url = "tel:${order?.deliveryAgent?.phone}";
                        if (await canLaunchUrlString(url)) {
                          await launchUrlString(url);
                        } else {
                          throw 'Could not launch $url';
                        }
                      },
                      child: SvgPicture.asset(
                        "assets/svg/ringing_telephone_outline_filled.svg",
                        height: 26,
                        width: 26,
                      ),
                    ),
                ],
              ),
            ),
            Visibility(
              replacement: const SizedBox.shrink(),
              child: Container(
                margin: const EdgeInsets.only(bottom: 4),
                child: Builder(builder: (context) {
                  final orderstatusindex = orderController
                      .getIndexFromStatus(order?.status?.toLowerCase());
                  return Text(
                    "Your order ${orderstatusindex == 0 ? "has been received" : orderstatusindex == 1 ? "is being packed" : orderstatusindex == 2 ? (order?.statusList.contains('delivery_agent_assigned') ?? false) ? "has been assigned" : "is packed" : orderstatusindex == 3 ? "is on the way" : "has been Successfully Delivered"}",
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 14,
                      height: 18 / 14,
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCardTop() {
    return Visibility(
      visible: order?.status != 'delivered',
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 5),
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 12,
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16 / 1.45),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Visibility(
                            visible: !orderController.loading,
                            replacement: Shimmer.fromColors(
                              baseColor: Colors.grey[200]!,
                              highlightColor: Colors.white,
                              child: Container(
                                height: 20,
                                color: Colors.grey,
                              ),
                            ),
                            child: Text(
                              "Order No. #${order?.salesOrderNumber}",
                              style: const TextStyle(
                                fontWeight: FontWeight.w700,
                                color: Colors.black,
                                fontSize: 14,
                                height: 18 / 14,
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "${order != null ? DateFormat('hh : mm a').format(order!.orderDate!) : ""} | ${order?.items?.length} Items\n\u20b9 ${order?.total}",
                            style: GoogleFonts.inter(
                              fontWeight: FontWeight.w400,
                              color: AppColors.kgreyDark,
                              fontSize: 12,
                              height: 18 / 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(40),
                          topRight: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                          vertical: 13, horizontal: 16 / 2),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            "assets/svg/location.svg",
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                            height: 24,
                          ),
                          const SizedBox(width: 9),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      order?.address?.type ?? '',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w400,
                                        color: Colors.white,
                                        fontSize: 12,
                                        height: 18 / 12,
                                        letterSpacing: -1,
                                      ),
                                    ),
                                    const SizedBox(width: 5),
                                    Icon(
                                      Icons.edit,
                                      color: AppColors.kgrey,
                                      size: 10,
                                    )
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  [
                                    order?.address?.house ?? '',
                                    order?.address?.street ?? '',
                                    order?.address?.landmark ?? '',
                                    order?.address?.city ?? '',
                                    order?.address?.pincode ?? '',
                                  ].join(', '),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.kgrey,
                                    fontSize: 10,
                                    height: 13 / 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Visibility(
              visible:
                  order?.status?.toLowerCase() == 'out_for_delivery_delayed',
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 11,
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 6, horizontal: 27),
                decoration: BoxDecoration(
                  color: const Color(0xffD20000),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  'There is a slight delay in the order due to the heavy rains',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.dmSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    height: 18 / 14,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  GoogleMap mapWidget() {
    return GoogleMap(
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      compassEnabled: false,
      liteModeEnabled: true,
      mapToolbarEnabled: false,
      onCameraMove: (position) {
        // setState(() {
        //   loading = true;
        //   allMarker.add(Marker(
        //       markerId: const MarkerId('MyMarker'),
        //       position: position.target));
        //   currentPosition = position.target;
        // });
      },
      mapType: MapType.normal,
      initialCameraPosition: _kGooglePlex,
      onMapCreated: (GoogleMapController controller) {
        _completer.complete(controller);
      },
      markers: Set.from(allMarker),
    );
  }

  Future<void> getPosition() async {
    currentPosition = LatLng(order!.address!.geoLocation!.coordinates![1],
        order!.address!.geoLocation!.coordinates![0]);
  }
}

class RoundedTrapeziumClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double height = size.height;
    double width = size.width;

    Path path = Path()
      ..moveTo(width * 0.15, 0)
      ..cubicTo(width * 0.1, 0, width * 0.1, 0, width * 0.01, height * 0.8)
      ..cubicTo(0, height, width * 0.02, height, width * 0.1, height)
      ..lineTo(width * 0.9, height)
      ..cubicTo(width, height, width, height, width * 0.964, height * 0.65)
      ..cubicTo(width * 0.89, 0, width * 0.87, 0, width * 0.80, 0)
      ..close();

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
