import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
// import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:geocoder/geocoder.dart';
// import 'package:flutter_geocoder/geocoder.dart';
import 'package:geocoding/geocoding.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:marquee/marquee.dart';
import 'package:shimmer/shimmer.dart';

// import '../../widgets/button_animation.dart';
import '../../../controllers/account_controller.dart';
import '../../widgets/rapsap_appbar.dart';
// import '../../widgets/widgets.dart';
import '../login/widgets/button.dart';
import '../../../controllers/user_controller.dart';
// import 'search_places_sheet.dart';

class SelectLocationScreen extends StatefulWidget {
  final LatLng latLng;

  const SelectLocationScreen({
    super.key,
    required this.latLng,
  });

  @override
  State<SelectLocationScreen> createState() => _SelectLocationScreenState();
}

class _SelectLocationScreenState extends State<SelectLocationScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  final UserController _userController = Get.find<UserController>();
  bool isOutofService = false;
  bool loading = true;

  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());

  List<Marker> allMarker = [];

  List<Placemark> addressList = [];
  late LatLng currentPosition;

  CameraPosition _kGooglePlex = const CameraPosition(
    target: LatLng(19.**************, 72.**************),
    zoom: 14,
  );

  //  LatLng(19.***************, 72.**************)

  @override
  void initState() {
    super.initState();

    log("current location: ${widget.latLng.toString()}");
    // if (widget.latLng.latitude != null && widget.latLng.longitude != null) {
    setState(() {
      currentPosition = widget.latLng;
      _kGooglePlex = CameraPosition(
        target: LatLng(widget.latLng.latitude, widget.latLng.longitude),
        zoom: 17,
      );
    });
    getAddressFromGeo(LatLng(widget.latLng.latitude, widget.latLng.longitude));
    // } else {
    // setState(() {
    //   _kGooglePlex = const CameraPosition(
    //     target: LatLng(19.**************, 72.**************),
    //     zoom: 17,
    //   );
    //   currentPosition = const LatLng(19.**************, 72.**************);
    // });
    // getAddressFromGeo(const LatLng(19.**************, 72.**************));
    // }
    allMarker
      ..clear()
      ..add(Marker(
        markerId: const MarkerId('MyMarker'),
        infoWindow: InfoWindow.noText,
        // draggable: true,
        // onDragEnd: (LatLng latLng) {
        //   print('Moved here ${latLng}');
        //   setState(() {
        //     currentPosition = latLng;
        //   });
        //   getAddressFromGeo(latLng);
        // },
        position: widget.latLng,
      ));
  }

  Future<void> getAddressFromGeo(LatLng latLng) async {
    setState(() {
      loading = true;
    });
    // final coordinates = new Coordinates(latLng.latitude, latLng.longitude);
    // List<Location> address =
    //     await Geocoder.local.findAddressesFromCoordinates(coordinates);

    List<Placemark> placemarks =
        await placemarkFromCoordinates(latLng.latitude, latLng.longitude);
    addressList = placemarks;
    final result = await _userController
        .getNearbyStore([latLng.longitude, latLng.latitude]);
    isOutofService = !result;
    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_userController.userdata.value != null) {
      accountController.getaddress();
    }

    return Scaffold(
      appBar: RapsapAppBar("Select Location"),
      body: Column(
        children: [
          Expanded(
            child: GoogleMap(
              myLocationEnabled: true,
              myLocationButtonEnabled: !loading,
              zoomControlsEnabled: !loading,
              zoomGesturesEnabled: true,
              scrollGesturesEnabled: true,
              onCameraIdle: () async {
                await getAddressFromGeo(currentPosition);
              },
              onCameraMove: (CameraPosition position) {
                setState(() {
                  loading = true;
                });
                allMarker
                  ..clear()
                  ..add(
                    Marker(
                      markerId: const MarkerId('current'),
                      position: position.target,
                    ),
                  );
                currentPosition = position.target;
              },
              onTap: loading
                  ? null
                  : (LatLng argument) {
                      allMarker
                        ..clear()
                        ..add(Marker(
                          markerId: const MarkerId('current'),
                          position: argument,
                        ));
                      _controller.future.then(
                        (value) => value.animateCamera(
                          CameraUpdate.newCameraPosition(
                            CameraPosition(
                              target:
                                  LatLng(argument.latitude, argument.longitude),
                              zoom: 17,
                            ),
                          ),
                        ),
                      );

                      setState(() {
                        currentPosition = argument;
                      });
                    },
              mapType: MapType.terrain,
              initialCameraPosition: _kGooglePlex,
              onMapCreated: (GoogleMapController controller) {
                _controller.complete(controller);
              },
              markers: Set.from(allMarker),
              // ignore: prefer_collection_literals
              // gestureRecognizers: Set()
              //   ..add(Factory<PanGestureRecognizer>(
              //       () => PanGestureRecognizer())),
            ),
          ),
          Column(
            children: [
              isOutofService == true
                  ? Container(
                      height: 20,
                      width: double.infinity,
                      color: Theme.of(context).primaryColor,
                      child: Row(
                        children: [
                          Expanded(
                            child: Marquee(
                              text:
                                  'We are increasing our reach everyday. We shall service your locality soon',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Colors.white,
                              ),
                              blankSpace: 100,
                            ),
                          ),
                        ],
                      ))
                  : Container(),
              Container(
                color: Colors.white,
                height: 180,
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Your location',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                    ),
                    const Spacer(),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/images/locationverified.svg',
                            colorFilter: ColorFilter.mode(
                                Theme.of(context).primaryColor,
                                BlendMode.srcIn),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            flex: 4,
                            child: Text(
                              addressList.isNotEmpty
                                  ? [
                                      '${addressList.firstOrNull?.name}',
                                      '${addressList.firstOrNull?.subLocality}',
                                      '${addressList.firstOrNull?.thoroughfare}',
                                      "${addressList.firstOrNull?.administrativeArea}",
                                      '${addressList.firstOrNull?.postalCode}'
                                    ].join(', ')
                                  : "",
                              maxLines: 2,
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 15),
                            ),
                          ),
                          // Expanded(
                          //   flex: 2,
                          //   child: Center(
                          //     child: ButtonAnimation(
                          //       onpress: () async {
                          //         Position? result =
                          //             await showModalBottomSheet<Position>(
                          //           context: context,
                          //           isScrollControlled: true,
                          //           enableDrag: true,
                          //           builder: (context) =>
                          //               const SearchPlacesSheet(),
                          //         );
                          //         if (result != null) {
                          //           setState(() {
                          //             _kGooglePlex = CameraPosition(
                          //                 target: LatLng(result.latitude,
                          //                     result.longitude),
                          //                 zoom: 17);
                          //           });
                          //           _controller.future.then(
                          //             (value) => value.animateCamera(
                          //               CameraUpdate.newCameraPosition(
                          //                 CameraPosition(
                          //                   target: LatLng(result.latitude,
                          //                       result.longitude),
                          //                   zoom: 14,
                          //                 ),
                          //               ),
                          //             ),
                          //           );
                          //         }
                          //       },
                          //       animationWidget: Container(
                          //         decoration: BoxDecoration(
                          //           border: Border.all(color: AppColors.secondaryBtnBorderColor),
                          //           borderRadius: BorderRadius.circular(4),
                          //         ),
                          //         child: const Text(
                          //           'Change',
                          //           style: TextStyle(color: AppColors.secondaryBtnForegroundColor),
                          //         ).paddingAll(8),
                          //       ),
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    loading
                        ? ElevatedButton(
                            onPressed: null,
                            child: Container(
                              width: double.infinity,
                              height: 56,
                              alignment: Alignment.center,
                              child: SizedBox(
                                height: 20,
                                width: 40,
                                child: LoadingIndicator(
                                  colors: [
                                    Theme.of(context).primaryColor,
                                    Colors.black,
                                  ],
                                  indicatorType: Indicator.cubeTransition,
                                ),
                              ),
                            ),
                          )
                        : SubmitButton(
                            text: isOutofService
                                ? 'Out of Service'
                                : 'Confirm Location',
                            onpressed: isOutofService
                                ? null
                                : () {
                                    Get.back<(LatLng, Placemark?)>(result: (
                                      currentPosition,
                                      addressList.firstOrNull
                                    ));
                                  },
                          ),
                  ],
                ),
                // width: MediaQuery.of(context).size.width / 2,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

Future<void> showAlertDialog(
    BuildContext context, String title, String content) {
  // set up the button
  Widget okButton = SubmitButton(
    height: 45,
    text: "OK",
    onpressed: () {
      Get.close(1);
    },
  ).paddingSymmetric(horizontal: 30, vertical: 8);

  // set up the AlertDialog
  AlertDialog alert = AlertDialog(
    backgroundColor: Colors.white,
    surfaceTintColor: Colors.white,
    title: Text(
      title,
      textAlign: TextAlign.center,
    ),
    titleTextStyle: const TextStyle(
      fontWeight: FontWeight.w700,
      fontSize: 18,
      height: 23.44 / 18,
      color: Colors.black,
    ),
    content: Text(
      content,
      textAlign: TextAlign.center,
    ),
    contentTextStyle: const TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      height: 18.23 / 14,
      color: Colors.black,
    ),
    actions: [
      okButton,
    ],
  );

  // show the dialog
  return showDialog(
    context: context,
    builder: (BuildContext context) {
      return alert;
    },
  );
}

class CustomAppShimmer extends StatelessWidget {
  final Widget child;

  const CustomAppShimmer({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(2),
        child: child,
      ),
    );
  }
}
