import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

import '../../../controllers/location_controller.dart';
import '../../../utils/colors.dart';
import '../../widgets/decorated_card.dart';
import 'search_page.dart';

class SearchPlacesSheet extends StatefulWidget {
  const SearchPlacesSheet({super.key});

  @override
  State<SearchPlacesSheet> createState() => _SearchPlacesSheetState();
}

class _SearchPlacesSheetState extends State<SearchPlacesSheet> {
  final LocationController locationController = Get.find<LocationController>();

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      expand: false,
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      builder: (context, scrollController) {
        return Material(
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              color: Colors.white,
            ),
            child: ListView(
              shrinkWrap: true,
              controller: scrollController,
              children: [
                const Text(
                  "Change delivery location",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    height: 1,
                    letterSpacing: -1,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 16 * 1.5),
                TextFormField(
                  autofocus: true,
                  controller: locationController.searchController,
                  onEditingComplete: () {
                    // if (searchController.text.length > 4) {
                    locationController.searchPlace();

                    // }
                  },
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(15),
                      child: SvgPicture.asset(
                        "assets/svg/search-icon.svg",
                        height: 16,
                      ),
                    ),
                    hintText: "Search for area, Street Name",
                    hintStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      height: 15.4 / 14,
                      letterSpacing: -0.01,
                      color: AppColors.kgrey,
                    ),
                  ),
                ),
                const SizedBox(height: 16 * 1.5),
                DecoratedCard(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16 / 2,
                  ),
                  child: ListTile(
                    onTap: () async {
                      final position = await determinePosition();
                      Get.back<Position>(result: position);
                    },
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                    trailing: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.black,
                    ),
                    leading: Icon(
                      Icons.my_location_outlined,
                      color: AppColors.ktealGrey,
                    ),
                    title: const Text(
                      'Use your current location',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        height: 18.2 / 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16 * 1.5),
                Obx(
                  () => Column(
                    mainAxisSize: MainAxisSize.min,
                    children: locationController.predictionList
                        .map((element) => ListTile(
                              onTap: () {},
                              title: Text("${element.description}"),
                            ))
                        .toList(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
