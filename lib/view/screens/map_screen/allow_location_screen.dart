// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../controllers/location_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/order_controller.dart';
import '../../../model/address_model.dart';
import '../../../utils/colors.dart';
import '../address/address_selection_widget.dart';
import '../login/widgets/button.dart';
import '../root_page/root_page.dart';
import 'select_location_screen.dart';

class AllowLocationScreen extends GetView<LocationController> {
  const AllowLocationScreen({super.key});

  Future<bool> _checkLocationAndNavigate(LocationController locationController,
      UserController userController) async {
    try {
      final singleOrderController = Get.find<OrderController>();
      print('singleOrderController.currentOrders');
      print(singleOrderController.currentOrders.entries);
      final isNewuser = userController.userdata.value?.isNewUser ?? false;
      if (isNewuser) {
        return false;
      }
      final result = await locationController.getlocation();
      if (result != null) {
        final isNearbyStoreAvailable = await userController.getNearbyStore(
          <double>[result.longitude, result.latitude],
        );
        if (isNearbyStoreAvailable) {
          // Navigate to the home screen
          Get.offAll(() => const RootPage());
          return true; // Indicate successful navigation
        }
      }
    } catch (error) {
      // Log the error or handle it as needed
      print('Error occurred while fetching location: $error');
    }
    return false; // Indicate navigation was not successful
  }

  @override
  Widget build(BuildContext context) {
    final userController = Get.find<UserController>();

    return FutureBuilder<bool>(
        future: _checkLocationAndNavigate(controller, userController),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show a loading indicator while checking
            return const Scaffold(
              backgroundColor: Colors.white,
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          return Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: SvgPicture.asset(
                      'assets/svg/locationheadimage.svg',
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: Column(
                      children: [
                        const Text(
                          'Hi, Nice to meet you',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 15),
                        Text(
                          " Please allow location permission for accurate delivery tracking and service availability.",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            height: 23.4 / 18,
                            color: AppColors.ktealGrey,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(height: 30),
                        GetBuilder<LocationController>(
                          builder: (_) {
                            return SubmitButton(
                              loading: controller.stateloading,
                              text: 'Use Current Location',
                              onpressed: () async {
                                final result = await controller.getlocation();
                                if (result != null) {
                                  await userController.getNearbyStore(<double>[
                                    result.longitude,
                                    result.latitude
                                  ]).then(
                                    (value) {
                                      if (value) {
                                        Get.offAll(() => const RootPage());
                                      } else {
                                        showAlertDialog(
                                            context,
                                            "Your Location is not servicable!",
                                            "We are increasing our reach everyday. We shall service your locality soon");
                                      }
                                    },
                                  );
                                }
                              },
                            );
                          },
                        ),
                        // Spacer(),
                        const SizedBox(height: 20),
                        GetBuilder<UserController>(
                          builder: (_) {
                            return SubmitButton(
                              loading: userController.isGettingStore,
                              text: 'Set Your Location Manually',
                              onpressed: () async {
                                final result =
                                    await showModalBottomSheet<Address>(
                                  context: context,
                                  builder: (context) =>
                                      AddressSelectionWidget(),
                                  // isScrollControlled: true,
                                  // useSafeArea: true,
                                );
                                if (result != null) {
                                  await userController
                                      .getNearbyStore(
                                          result.geoLocation?.coordinates)
                                      .then(
                                    (value) {
                                      if (value) {
                                        Get.offAll(() => const RootPage());
                                      } else {
                                        showAlertDialog(
                                            context,
                                            "Your Location is not servicable!",
                                            "We are increasing our reach everyday. We shall service your locality soon");
                                      }
                                    },
                                  );
                                }
                              },
                              side: Border.all(
                                  color: AppColors.ctaBackgroundColor,
                                  width: 1.6),
                              bgcolor: Colors.white,
                              txtcolor: Colors.black,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }
}
