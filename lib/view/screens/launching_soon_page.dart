import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import 'login/widgets/button.dart';
import '../../controllers/account_controller.dart';

class LaunchingSoon extends StatelessWidget {
  const LaunchingSoon({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SvgPicture.asset(
            'assets/svg/launchingsoonbg.svg',
            fit: BoxFit.fill,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/svg/launchingsoonhanger.svg',
                  ),
                ],
              ),
              SizedBox(height: Get.height * 0.1),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset('assets/svg/launchingsoonbike.svg'),
                ],
              ),
              const SizedBox(height: 40),
              Text(
                'Online Delivery',
                style: GoogleFonts.barlowCondensed(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Text(
                'Coming soon',
                style: GoogleFonts.barlowCondensed(
                  fontSize: 50,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 15),
              const Flexible(
                child: Text(
                  'We are working to get your groceries at your doorstep. Stay tuned with us.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Color(0xffAFAFAF),
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ),
              const SizedBox(height: 50),
              SizedBox(
                height: 45,
                child: SubmitButton(
                  textsize: 16,
                  text: 'Notify Me',
                  onpressed: () async {
                    // TODO: maybe get user's lat long name and save it on server to record which other locations are in need to be serviced
                    Get.find<AccountController>().logoutUser();
                  },
                ),
              ),
              const SizedBox(height: 20)
            ],
          ).paddingSymmetric(horizontal: 40),
        ],
      ),
    );
  }
}
