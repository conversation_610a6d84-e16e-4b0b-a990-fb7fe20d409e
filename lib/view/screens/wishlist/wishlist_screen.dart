// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

import '../../../model/database_models/database_models.dart';
import '../../../model/item_list_model/item_list_model.dart';
import '../../../controllers/wishlist_controller.dart';
import '../../../services/database_helper.dart';
import '../../../utils/colors.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/user_controller.dart';
import '../../../utils/enumerations.dart';
import '../../widgets/button_animation.dart';
import '../../widgets/minimised_cart_item_status_widget.dart';
import '../map_screen/select_location_screen.dart';
import '../product_screen/product_screen.dart';

class WishListScreen extends StatefulWidget {
  const WishListScreen({super.key});

  @override
  State<WishListScreen> createState() => _WishListScreenState();
}

class _WishListScreenState extends State<WishListScreen> {
  final UserController userController = Get.find<UserController>();
  final WishlistController wishlistcontroller = Get.find();
  int page = 1;
  int limit = 10;

  @override
  void initState() {
    super.initState();
    wishlistcontroller.getWishlist();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: Get.height * 0.1,
        leading: const BackButton(color: Colors.white),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4868D8), Color(0xff334A9A)],
            ),
          ),
        ),
        title: const Text("My Wishlist"),
        centerTitle: false,
        titleTextStyle: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize: 20,
          height: 24.2 / 20,
          color: Colors.white,
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            child: Column(
              children: [
                const Text(
                  "Can't find the item you love? ",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 18,
                    height: 23.44 / 18,
                    letterSpacing: -0.01,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 16 / 2),
                Text(
                  "Don't worry! We'll keep you in the loop and notify you as soon as it's back in stock. Your satisfaction is our priority, and we're committed to ensuring you get the products you need when you need them.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    height: 15.62 / 12,
                    letterSpacing: -0.01,
                    color: Colors.black.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: GetBuilder<WishlistController>(
                    id: 'wishlist',
                    builder: (controller) {
                      return Visibility(
                        visible: !controller.loading,
                        replacement: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(20),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[200]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  height: 130,
                                  width: double.infinity,
                                  color: AppColors.kgrey,
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(20),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[200]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  height: 130,
                                  width: double.infinity,
                                  color: AppColors.kgrey,
                                ),
                              ),
                            ),
                          ],
                        ),
                        child: Visibility(
                          visible: controller.wislistItems.isNotEmpty,
                          replacement: const Center(
                            child: Text('No Items in Wishlist'),
                          ),
                          child: GridView.count(
                            shrinkWrap: true,
                            childAspectRatio: 0.62,
                            crossAxisCount: 2,
                            crossAxisSpacing: 10,
                            mainAxisSpacing: 10,
                            children:
                                controller.wislistItems
                                    .asMap()
                                    .entries
                                    .map(
                                      (e) =>
                                          itemCard(data: e.value, index: e.key),
                                    )
                                    .toList(),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 16),
                  constraints: const BoxConstraints.expand(height: 52),
                  child: OutlinedButton(
                    onPressed: () {
                      Get
                        ..back()
                        ..back();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      side: BorderSide(color: Theme.of(context).primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      textStyle: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 16,
                        height: 20.8 / 16,
                        letterSpacing: -0.01,
                      ),
                    ),
                    child: const Text("Continue Shopping"),
                  ),
                ),
              ],
            ),
          ),
          const Align(
            alignment: Alignment.bottomCenter,
            child: MinimisedCartItemStatusWidget(),
          ),
        ],
      ),
    );
  }

  Widget itemCard({required Item data, required int index}) {
    final discount =
        data.discount?.type == DiscountType.currency
            ? data.discount?.value
            : data.price.toDouble() * (data.discount?.value ?? 0.0);
    final taxInRupees = data.tax.fold<double>(
      0.0,
      (prev, e) => prev + (((e.value ?? 0).toDouble() / 100.0) * data.price),
    );
    return ButtonAnimation(
      onpress:
          data.availableStock == 0
              ? null
              : () {
                Get.to(() => ProductScreen(item: data));
              },
      animationWidget: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            DecoratedBox(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.kgrey),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // product image
                  Expanded(
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        CachedNetworkImage(
                          placeholder:
                              (context, url) =>
                                  Image.asset("assets/images/error-image.webp"),
                          errorWidget:
                              (context, url, error) =>
                                  Image.asset("assets/images/error-image.webp"),
                          fit: BoxFit.cover,
                          imageUrl: data.imageUrls.firstOrNull ?? '',
                        ),
                        Visibility(
                          visible: data.availableStock == 0,
                          child: const Align(
                            alignment: Alignment(0, -0.6),
                            child: Text(
                              "Out of stock",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                                letterSpacing: -0.01,
                                fontSize: 14,
                                height: 18.23 / 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // product label
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(color: AppColors.kHomeAppBarBG),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Text(
                                  // name
                                  data.name?.capitalize ?? "---",
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(
                                    height: 1,
                                    fontSize: 16,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () async {
                                  final findindex = wishlistcontroller
                                      .wislistItems
                                      .indexWhere(
                                        (element) => element.id == data.id,
                                      );

                                  wishlistcontroller
                                    ..wislistItems.removeAt(findindex)
                                    ..update()
                                    ..removeFromwishlist(data.id ?? '');
                                  setState(() {});
                                },
                                icon: Icon(
                                  Icons.favorite,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ],
                          ),

                          // weight
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '${data.weight}${data.unit}',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),

                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // prices
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '\u20b9${data.price.round() == 0 ? data.mrp.round() : data.price.round()}',
                                    style: GoogleFonts.inter(
                                      height: 1,
                                      fontSize: 16,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Visibility(
                                    visible:
                                        data.price.round() != 0 &&
                                        data.mrp.round() != 0 &&
                                        data.mrp != data.price,
                                    child: Text(
                                      '\u20b9${data.mrp.round()}',
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        color: Colors.black.withOpacity(0.5),
                                        decoration: TextDecoration.lineThrough,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              // add/remove button
                              GetBuilder<CartController>(
                                init: Get.find<CartController>(),
                                builder: (cartController) {
                                  return FutureBuilder<int>(
                                    future: DatabaseHelper.instance.getQty(
                                      LocalShoppingCart(
                                        productID: data.id!,
                                        purchaseOrderID:
                                            data.purchaseOrderId ?? '',
                                      ),
                                    ),
                                    builder: (context, snapshot) {
                                      if (snapshot.data == null) {
                                        return SizedBox(
                                          height: 30,
                                          width: 80,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              CustomAppShimmer(
                                                child: Container(
                                                  height: 30,
                                                  width: 65,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          2,
                                                        ),
                                                    color: AppColors.kgrey
                                                        .withOpacity(0.5),
                                                  ),
                                                ),
                                              ),
                                              Shimmer.fromColors(
                                                baseColor: Colors.black
                                                    .withOpacity(0.1),
                                                highlightColor: Colors.white,
                                                child: const Text(
                                                  "Add",
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }
                                      if (snapshot.data == 0) {
                                        return InkWell(
                                          onTap:
                                              data.availableStock == 0
                                                  ? null
                                                  : () async {
                                                    await DatabaseHelper
                                                        .instance
                                                        .addUpdate(
                                                          LocalShoppingCart(
                                                            mrp: data.mrp,
                                                            name: data.name,
                                                            qty: 1,
                                                            productID: data.id!,
                                                            purchaseOrderID:
                                                                data.purchaseOrderId!,
                                                            price: data.price,
                                                            imageURL:
                                                                data
                                                                    .imageUrls
                                                                    .firstOrNull ??
                                                                '',
                                                            availableStock:
                                                                data.availableStock,
                                                            discount: discount,
                                                            tax: taxInRupees,
                                                          ),
                                                        );
                                                    setState(() {});
                                                  },
                                          child: Container(
                                            height: 36,
                                            width: 84,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color:
                                                    data.availableStock == 0
                                                        ? AppColors.kgrey
                                                        : Theme.of(
                                                          context,
                                                        ).primaryColor,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                            ),
                                            child: Text(
                                              "Add",
                                              style: TextStyle(
                                                color:
                                                    data.availableStock == 0
                                                        ? AppColors.kgreyDark
                                                        : Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                          ),
                                        );
                                      }

                                      return InkWell(
                                        onTap: () {
                                          DatabaseHelper.instance.remove(
                                            LocalShoppingCart(
                                              productID: data.id!,
                                              purchaseOrderID:
                                                  data.purchaseOrderId!,
                                            ),
                                          );
                                          setState(() {});
                                        },
                                        child: Text(
                                          'Remove',
                                          style: TextStyle(
                                            color:
                                                Theme.of(context).primaryColor,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Visibility(
              visible: data.discount?.type == DiscountType.percent,
              // replacement: const SizedBox(
              //   width: 32,
              //   height: 44,
              // ),
              child: Container(
                width: 32,
                height: 44,
                alignment: Alignment.topLeft,
                child: Stack(
                  alignment: Alignment.topLeft,
                  children: [
                    SvgPicture.asset(
                      'assets/svg/offertag.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xffFF5454),
                        BlendMode.srcIn,
                      ),
                    ),
                    Center(
                      child: Text(
                        "${data.discount?.value?.round()}% \n OFF",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                          letterSpacing: -0.5,
                        ),
                        textAlign: TextAlign.center,
                      ).paddingOnly(bottom: 10),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
