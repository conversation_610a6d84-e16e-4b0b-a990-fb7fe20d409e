// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';
import '../../../controllers/order_controller.dart';
import '../address/my_address.dart';
import '../cart/apply_coupon_page.dart';
import '../payment/payment_methods_screen.dart';
import '../wishlist/wishlist_screen.dart';
import '../order_section/my_orders.dart';
import '../../../controllers/user_controller.dart';
import '../login/login_screen.dart';
import 'edit_profile_screen.dart';
import 'about_us.dart';
import 'frequently_asked_questions.dart';
import 'privacy_policy.dart';
import 'terms_and_conditions.dart';
import 'widgets/logout_dialog.dart';

class AccountScreen extends StatelessWidget {
  AccountScreen({super.key});
  final UserController userController = Get.find<UserController>();
  final OrderController orderController = Get.find<OrderController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RapsapAppBar("My Account"),
      body: ListView(
        shrinkWrap: true,
        padding: const EdgeInsets.all(24),
        children: [
          // user details
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                userController.userdata.value == null
                    ? "Guest User"
                    : "${userController.userdata.value?.name?.capitalize}",
                // "---",
                maxLines: 1,
                style: const TextStyle(
                  fontSize: 20,
                  height: 18 / 20,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (userController.userdata.value != null) ...[
                          const SizedBox(height: 12),
                          Text(
                            userController.userdata.value?.mobile ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              height: 18 / 14,
                              color: AppColors.kgreyDark,
                            ),
                          ),
                          Text(
                            userController.userdata.value?.email
                                    ?.toLowerCase() ??
                                "",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              height: 18 / 14,
                              color: AppColors.kgreyDark,
                            ),
                          )
                        ],
                      ],
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      userController.userdata.value == null
                          ? Get.to(
                              () => const LoginScreen(),
                              fullscreenDialog: true,
                            )
                          : Get.to(
                              () => const EditProfileScreen(),
                              fullscreenDialog: false,
                            );
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Theme.of(context).primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      foregroundColor: Theme.of(context).primaryColor,
                      backgroundColor: Colors.transparent,
                      padding: const EdgeInsets.all(8),
                    ),
                    child: Text(
                      userController.userdata.value == null ? "Login" : 'Edit',
                    ),
                  ),
                ],
              ),
            ],
          ),
          ListView.separated(
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(vertical: 24),
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) => AccountItemTile(
              item: accountItems.elementAt(index),
            ),
            separatorBuilder: (context, index) => const SizedBox(
              // color: AppColors.kgrey,
              height: 5,
            ),
            itemCount: accountItems.length,
          ),

          // const SizedBox(height: 20),

          FutureBuilder<PackageInfo>(
            future: PackageInfo.fromPlatform(),
            builder: (context, snapshot) {
              switch (snapshot.connectionState) {
                case ConnectionState.done:
                  return Align(
                    alignment: Alignment.bottomCenter,
                    child: Text(
                      'Version ${snapshot.data!.version} + ${snapshot.data!.buildNumber}',
                      style: TextStyle(
                        color: Colors.black.withOpacity(0.5),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                default:
                  return const SizedBox.shrink();
              }
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  final List<AccountItem> accountItems = [
    AccountItem(
      title: "My Orders",
      assetPath: "assets/svg/orders_icon.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.find<OrderController>().getOrders();
              Get.to(() => MyOrders());
            },
    ),
    AccountItem(
      title: 'My Wishlist',
      assetPath: "assets/svg/wishlist.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const WishListScreen());
            },
    ),
    AccountItem(
      title: 'My Coupons',
      assetPath: "assets/svg/coupon.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const ApplyCouponsScreen());
            },
    ),
    AccountItem(
      title: 'My Addresses',
      assetPath: "assets/svg/addressicon.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const MyAddressScreen());
            },
    ),
    AccountItem(
      title: 'Payment Methods',
      assetPath: "assets/svg/payment.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const PaymentMethodsScreen());
            },
    ),

    // AccountItem(
    //     title: 'My Subscriptions',
    //     leading: SvgPicture.asset('assets/svg/subscribtion.svg'),
    //     onTap: () {
    //       Get.to(() => SubscriptionCommingSoon());
    //     }),

    AccountItem(
      title: 'Customer Support',
      assetPath: "assets/svg/customer_support.svg",
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () async {
              if (!await launchUrl(Uri.parse(Platform.isIOS
                  ? "whatsapp://wa.me/************/"
                  : "whatsapp://send?phone=************"))) {
                throw "Could not launch whatsapp";
              }
            },
    ),
    AccountItem(
      title: 'About',
      assetPath: "assets/svg/about_icon.svg",
      onTap: () {
        Get.to(const AboutUs());
      },
    ),
    AccountItem(
      title: 'Terms & Conditions',
      assetPath: "assets/svg/customer_support.svg",
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const TermsAndConditions());
            },
    ),
    AccountItem(
      title: 'FAQs',
      assetPath: "assets/svg/customer_support.svg",
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const FrequentlyAskedQuestions());
            },
    ),
    AccountItem(
      title: 'Privacy Policy',
      assetPath: "assets/svg/customer_support.svg",
      onTap: Get.find<UserController>().userdata.value == null
          ? null
          : () {
              Get.to(() => const PrivacyPolicy());
            },
    ),
    AccountItem(
      title: 'Logout',
      assetPath: "assets/svg/logout.svg",
      isDisabled: Get.find<UserController>().userdata.value == null,
      onTap: () async {
        return showDialog(
          context: Get.context!,
          builder: (BuildContext context) {
            return const LogoutDialog();
          },
        );
      },
    ),
  ];
}

class AccountItem {
  final String title;
  final String assetPath;
  final void Function()? onTap;
  final bool? isDisabled;

  AccountItem({
    required this.title,
    required this.assetPath,
    required this.onTap,
    this.isDisabled,
  });
}

class AccountItemTile extends StatelessWidget {
  final AccountItem item;

  const AccountItemTile({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      tileColor: Colors.white,
      minLeadingWidth: Get.width * 0.07,
      title: Text(
        item.title,
        style: TextStyle(
          color: item.isDisabled ?? false ? AppColors.kgrey : Colors.black,
          fontSize: 18,
          height: 20.8 / 18,
          fontWeight: FontWeight.w500,
        ),
      ),
      leading: SvgPicture.asset(
        item.assetPath,
        colorFilter: ColorFilter.mode(
          item.isDisabled ?? false ? AppColors.kgrey : Colors.black,
          BlendMode.srcIn,
        ),
      ),
      onTap: item.onTap,
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 18,
        color: item.isDisabled ?? false ? AppColors.kgrey : Colors.black,
      ),
    );
  }
}
