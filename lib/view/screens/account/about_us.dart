import 'package:flutter/material.dart';
import '../../../utils/colors.dart';

import '../../widgets/rapsap_appbar.dart';

class AboutUs extends StatelessWidget {
  const AboutUs({super.key});
  @override
  Widget build(BuildContext context) {
    const List<(String, String)> content = <(String, String)>[
      (
        "How do I place my order?",
        "You can open our website junq.in and allow to detect your location so that we can deliver it to you. Choose among various items with a minimum order of 599 and we'll Deliver it to you the next day."
      ),
      (
        "Is it safe?",
        "We started this service to make sure that you don't have to get out of your safe house and get farm fresh and safe food delivered at your doorstep. We sanitize and sort the produce and pack it at the packing unit which is directly opened by you at your doorstep."
      ),
      (
        "Is my order customizable?",
        "Yes you can customize your order across all the Categories we're providing to you we'll get it delivered across mumbai for you."
      ),
      (
        "Can I cancel my order?",
        "We would appreciate if you accept the order once it's placed as we send out a purchase order to our partner farmer accordingly and won't want them to face any problems for the same."
      ),
      (
        "Is there a delivery fee?",
        "Yes we get it delivered to you direct from our farms to your doorsteps at affordable prices which comes with a delivery fee of just INR 39/- also if you place and order above INR 1199/- you get it absolutely free delivery!"
      ),
      (
        "What if I have a problem with my order?",
        "You can always contact us on our 24/7 contact number +917506286234 we shall get your issue resolved immediately!"
      ),
      (
        "Can I return my order?",
        "It's impossible to take a Vegetable back and put it on trees or roots again so we prefer you not to return the farm fresh Product back but send your concerns regarding the same as we're all ears for you!"
      ),
      (
        "Do I need to visit a physical store?",
        "As of now we're just a online based company which provides you no contact safe delivery direct from farms to your doorsteps so you don't need to step out at all !"
      ),
    ];
    return Scaffold(
      appBar: RapsapAppBar("About Us"),
      body: Theme(
        data: Theme.of(context).copyWith(dividerColor: AppColors.kgrey),
        child: ListView(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          children: [
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "GENERAL FAQ’s",
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      height: 18 / 14,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    "Frequently asked questions for our customers",
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      height: 18 / 14,
                      color: AppColors.kgrey,
                    ),
                  ),
                ],
              ),
            ),
            ...content.map(
              (e) => ExpansionTile(
                title: Text(
                  e.$1,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    height: 20.8 / 16,
                    color: Colors.black,
                  ),
                ),
                tilePadding: const EdgeInsets.symmetric(horizontal: 24),
                childrenPadding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
                children: [
                  Text(
                    e.$2,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      height: 18.2 / 14,
                      color: AppColors.kgreyDark,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
