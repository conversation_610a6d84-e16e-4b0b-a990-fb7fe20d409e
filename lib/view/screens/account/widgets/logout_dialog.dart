import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../controllers/account_controller.dart';

class LogoutDialog extends StatelessWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Platform.isAndroid
        ? AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            title: const Text(
              "Confirm Logout",
              textAlign: TextAlign.center,
            ),
            titleTextStyle: GoogleFonts.dmSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: Colors.black,
            ),
            buttonPadding: const EdgeInsets.symmetric(horizontal: 18),
            content: const Text(
              "Are you sure, you want to logout?",
              textAlign: TextAlign.center,
            ),
            contentTextStyle: GoogleFonts.dmSans(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              color: Colors.black,
            ),
            backgroundColor: Colors.white,
            actions: [cancelButton(context), okButton],
            actionsAlignment: MainAxisAlignment.center,
          )
        : CupertinoAlertDialog(
            title: const Text("Logout"),
            content: const Text("Confirm Logout from this account"),
            actions: [cancelButton(context), okButton],
          );
  }

  Widget get okButton => ElevatedButton(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: const Text("Yes"),
        onPressed: () async {
          final accountController = Get.find<AccountController>();
          await accountController.logoutUser();
        },
      );
  Widget cancelButton(BuildContext context) => OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.black),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          foregroundColor: Theme.of(context).textTheme.bodySmall?.color,
        ),
        child: const Text("No"),
        onPressed: () {
          Get.close(1);
        },
      );
}
