import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';

import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';
import 'widgets/form_field_with_label.dart';
import '../login/widgets/button.dart';
import '../../../controllers/user_controller.dart';
import 'delete_account.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late final TextEditingController _name;
  late final TextEditingController _mob;
  late final TextEditingController _mail;
  final UserController userController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  ImagePicker imagepick = ImagePicker();
  @override
  void initState() {
    super.initState();
    _name =
        TextEditingController(text: userController.userdata.value?.name ?? '');
    _mob = TextEditingController(
        text: userController.userdata.value?.mobile ?? '');
    _mail =
        TextEditingController(text: userController.userdata.value?.email ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RapsapAppBar("Edit Profile"),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(children: [
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  AddFormField(
                    fieldController: _name,
                    fieldLabel: 'Name',
                    hintText: 'Enter name',
                    validator: (p0) => p0?.isEmpty ?? true ? 'required' : null,
                  ),
                  const SizedBox(height: 10),
                  AddFormField(
                    fieldController: _mob,
                    fieldLabel: 'Mobile',
                    hintText: 'Enter phone Number',
                  ),
                  const SizedBox(height: 10),
                  AddFormField(
                    fieldController: _mail,
                    fieldLabel: 'Email ID',
                    hintText: 'Enter email',
                    validator: (p0) => p0?.isEmpty ?? true ? 'required' : null,
                  ),
                  const SizedBox(height: 30),
                  SubmitButton(
                    text: 'Submit',
                    onpressed: () async {
                      if (_formKey.currentState?.validate() ?? false) {
                        userController
                            .updateUserDetails(
                                email: _mail.text.trim(),
                                name: _name.text.trim())
                            .then(
                          (value) {
                            if (value) {
                              Get.back();
                            }
                          },
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 30),

            ///List Tile
            // const Divider(
            //   color: AppColors.kgrey,
            //   thickness: 0.5,
            // ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minLeadingWidth: 0,
            //   title: Text(
            //     'Change Password',
            //     style: GoogleFonts.inter(
            //         fontWeight: FontWeight.w500, fontSize: 16),
            //   ),
            //   trailing: const Icon(
            //     Icons.arrow_forward_ios,
            //     color: AppColors.kblack,
            //     size: 18,
            //   ),
            // ),
            Divider(
              height: 0,
              color: AppColors.kgrey,
              thickness: 0.5,
            ),
            ListTile(
              onTap: () {
                Get.to(() => DeleteAccountScreen());
              },
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              title: Text(
                'Delete Account',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: Colors.black,
                size: 18,
              ),
            ),
            Divider(
              height: 0,
              color: AppColors.kgrey,
              thickness: 0.5,
            ),
          ]),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _name.dispose();
    _mob.dispose();
    _mail.dispose();
    super.dispose();
  }
}
