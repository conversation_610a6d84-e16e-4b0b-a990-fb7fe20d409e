import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../cart/apply_coupon_page.dart';

class CouponWonScreen extends StatefulWidget {
  const CouponWonScreen({super.key});

  @override
  State<CouponWonScreen> createState() => _CouponWonScreenState();
}

class _CouponWonScreenState extends State<CouponWonScreen> {
  bool balloonClicked = false;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          actions: [
            CloseButton(
              onPressed: () {
                Get.back();
              },
            )
          ],
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                setState(() {
                  balloonClicked = true;
                });
                Future.delayed(
                  Durations.extralong4,
                  () async {
                    await Get.to(() => const ApplyCouponsScreen());
                    await Future.delayed(Durations.extralong4, () {});
                  },
                );
              },
              child: Container(
                height: Get.height * 0.55,
                width: double.infinity,
                color: Theme.of(context).primaryColor,
                child: Visibility(
                  visible: !balloonClicked,
                  child: Image.asset("assets/images/balloons_bg.webp"),
                ),
              ),
            ),
            SizedBox(height: Get.height * 0.05),
            Text(
              balloonClicked ? "Hurray!!!" : "CLICK",
              style: GoogleFonts.holtwoodOneSc(
                fontWeight: FontWeight.w400,
                fontSize: 20,
                height: 16 / 20,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Visibility(
              visible: !balloonClicked,
              child: const Text(
                "on a ballon to view your lucky coupon",
                style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    height: 16 / 14,
                    color: Colors.black,
                    letterSpacing: -0.08),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
