import 'package:flutter/material.dart';

import '../../../utils/colors.dart';
import '../../widgets/rapsap_appbar.dart';

class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RapsapAppBar("Privacy Policy"),
      body: Theme(
        data: Theme.of(context).copyWith(dividerColor: AppColors.kgrey),
        child: Scrollbar(
          child: ListView(
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            children: [
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Except as otherwise expressly stated with respect to our products, all contents of the site are offered on an \"as is\"basis without any warranty whatsoever either express or implied. ghuge.farm makes no representations, express or implied, including without limitation implied warranties of merchantability and fitness for a particular purpose.ghuge.farm does not guarantee the functions contained in the site will be uninterrupted or error-free, that this site or its server will be free of viruses or other harmful components, or defects will be corrected even if Ghuge.farmis aware of them.",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 18 / 14,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                    const SizedBox(height: 16 * 2),
                    const Text(
                      "Copyright and Trademark",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        height: 21.6 / 18,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16 / 2),
                    Text(
                      "Unless otherwise noted, all materials on this site are protected as the copyrights, trade dress, trademarks and/ or other intellectual properties owned by ghuge.farm or by other parties that have licensed their material toghuge.farm.All rights not expressly granted are reserved.",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 18 / 14,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                    const SizedBox(height: 16 * 2),
                    const Text(
                      "Personal Use",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        height: 21.6 / 18,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16 / 2),
                    Text(
                      "Your use of the materials included on this site is for informational and shopping purposes only. You agree you will not distribute, publish, transmit, modify, display or create derivative works from or exploit the contents of this site in any way. You agree to indemnify, defend and hold harmless ghuge.farm for any and all unauthorized uses you may make of any material on the site. You acknowledge the unauthorized use of the contents could cause irreparable harm to ghuge.farm and that in the event of an unauthorized use, ghuge.farm shall be entitled to aninjunction in addition to any other remedies available at law or in equity.",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 18 / 14,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                    const SizedBox(height: 16 * 2),
                    const Text(
                      "Feedback and Submissions",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        height: 21.6 / 18,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16 / 2),
                    Text(
                      "You agree you are and shall remain solely responsible for the contents of any submissions you make, and you will not submit material that is unlawful, defamatory, abusive or obscene. You agree that you will not submit anything to the site that will violate any right of any third party, including copyright, trademark, privacy or other personal or proprietary right(s).While we appreciate your interest in ghuge.farm, we do not want and cannot accept any ideas you consider to be proprietary regarding designs, product technology or other suggestions you may have developed. Consequently,any material you submit to this site will be deemed a grant of a royalty free non-exclusive right and license to use,reproduce, modify, display, transmit, adapt, publish, translate, create derivative works from and distribute these materials throughout the universe in any medium and through any methods of distribution, transmission and display whether now known or hereafter devised. In addition, you warrant that all so-called \"moral rights\" have been waived.",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 18 / 14,
                        color: AppColors.kgreyDark,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
