import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../controllers/account_controller.dart';
import '../../../main.dart';
import '../../../utils/colors.dart';
import '../login/widgets/button.dart';
import '../../../controllers/cart_controlller.dart';
import '../../../controllers/user_controller.dart';
import '../../../model/address_model.dart';
import '../../../services/database_helper.dart';
import '../../widgets/widgets.dart';
import '../login/login_screen.dart';
import 'widgets/delete_account_instructions.dart';

// ignore: must_be_immutable
class DeleteAccountScreen extends StatelessWidget {
  DeleteAccountScreen({super.key});
  String reasonText = "";
  final AccountController accountController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _formKeyconfirm = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          title: const Text(
            "Delete My Account",
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: Colors.black,
            ),
          ),
        ),
        body: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          Align(
                              alignment: Alignment.topLeft,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Raise a request to delete your Account',
                                    style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  const Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '•',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                          height: 1.8,
                                        ),
                                      ),
                                      SizedBox(width: 5),
                                      Expanded(
                                        child: Text(
                                          // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                          "We are sad to see you go! ",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w400,
                                            height: 1.8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '•',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                          height: 1.8,
                                        ),
                                      ),
                                      SizedBox(width: 5),
                                      Expanded(
                                        child: Text(
                                          // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                          "Before exiting the community, please note that you  will loose all the information you saved as well as access to your account.",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w400,
                                            height: 1.8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '•',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                          height: 1.8,
                                        ),
                                      ),
                                      SizedBox(width: 5),
                                      Expanded(
                                        child: Text(
                                          // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                          "A confirmation of your exit shall be communicated via email or whatsApp. ",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w400,
                                            height: 1.8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              )),
                          const SizedBox(height: 10),
                          TextFormField(
                            minLines: 5,
                            maxLines: 5,
                            maxLength: 128,

                            decoration: const InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: Colors.black, width: 2.0),
                                borderRadius: BorderRadius.zero,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.grey,
                                  width: 2.0,
                                ),
                                borderRadius: BorderRadius.zero,
                              ),
                              border: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.grey,
                                  width: 2.0,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(19.0),
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.redAccent,
                                  width: 2.0,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(19.0),
                                ),
                              ),
                              hintText: 'Reason of deleting account....',
                            ),
                            // The validator receives the text that the user has entered.
                            onChanged: (val) {
                              // setState(() {
                              reasonText = val;
                              // });
                            },
                            validator: (value) {
                              if (value != null && value.isEmpty) {
                                return "reason required";
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 30),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            showModalBottomSheet(
                              backgroundColor: Colors.white,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(20),
                                ),
                              ),
                              context: context,
                              builder: (BuildContext context) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 24, vertical: 16),
                                      child: Text(
                                        "Delete Confirmation",
                                        style: TextStyle(
                                          fontSize: 18,
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    Divider(
                                      height: 5,
                                      color: Colors.grey.shade300,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(24),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "Once you delete your account",
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  color: Theme.of(context)
                                                      .primaryColor,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                              const DeleteAccountInstructionsUI(
                                                  title:
                                                      "You will not be able to recover data \nlater on."),
                                              const DeleteAccountInstructionsUI(
                                                  title:
                                                      "Your account will no longer be accessible \nto use on any other device."),
                                            ],
                                          ),
                                          const SizedBox(height: 50),
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              SizedBox(
                                                height: 48,
                                                child: SubmitButton(
                                                  text: 'Keep Account',
                                                  onpressed: () {
                                                    Get.close(2);
                                                  },
                                                ),
                                              ),
                                              const SizedBox(height: 12),
                                              TextButton(
                                                onPressed: () async {
                                                  Get.back();

                                                  await showDialog(
                                                    useRootNavigator: false,
                                                    barrierDismissible: false,
                                                    context: context,
                                                    builder: (context) {
                                                      return PopScope(
                                                        canPop: false,
                                                        child: Dialog(
                                                          insetPadding:
                                                              const EdgeInsets
                                                                  .all(24),
                                                          child: Container(
                                                            color: Colors.white,
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(
                                                                      20.0),
                                                              child: Column(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .min,
                                                                children: [
                                                                  const Row(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .center,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .center,
                                                                    children: [
                                                                      Expanded(
                                                                        child:
                                                                            Text(
                                                                          'You’re about to delete your account',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                20,
                                                                            color:
                                                                                Colors.black,
                                                                            fontWeight:
                                                                                FontWeight.w600,
                                                                          ),
                                                                          textAlign:
                                                                              TextAlign.center,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  const SizedBox(
                                                                      height:
                                                                          10),
                                                                  const Row(
                                                                    children: [
                                                                      Expanded(
                                                                        child:
                                                                            Text(
                                                                          'All data & features associated with your account will be permanently deleted in 30(Business) days. The information can’t be recovered once the account deleted.',
                                                                          style:
                                                                              TextStyle(
                                                                            color:
                                                                                Color(0xff444444),
                                                                          ),
                                                                          textAlign:
                                                                              TextAlign.center,
                                                                        ),
                                                                      )
                                                                    ],
                                                                  ),
                                                                  const SizedBox(
                                                                      height:
                                                                          20),
                                                                  const Row(
                                                                    children: [
                                                                      Expanded(
                                                                        child: SelectableText
                                                                            .rich(
                                                                          TextSpan(
                                                                            text:
                                                                                'Type ',
                                                                            style:
                                                                                TextStyle(
                                                                              color: Color(0xff444444),
                                                                              fontSize: 16,
                                                                            ),
                                                                            children: <TextSpan>[
                                                                              TextSpan(
                                                                                text: " 'DELETE MY ACCOUNT' ",
                                                                                style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black, fontSize: 16),
                                                                              ),
                                                                              TextSpan(
                                                                                text: 'as it is',
                                                                                style: TextStyle(color: Color(0xff444444), fontSize: 16),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  const SizedBox(
                                                                      height:
                                                                          20),
                                                                  Form(
                                                                    key:
                                                                        _formKeyconfirm,
                                                                    child:
                                                                        TextFormField(
                                                                      decoration:
                                                                          const InputDecoration(
                                                                        isDense:
                                                                            true,
                                                                        focusedBorder: OutlineInputBorder(
                                                                            borderSide:
                                                                                BorderSide(color: Colors.black, width: 2.0),
                                                                            borderRadius: BorderRadius.zero),
                                                                        enabledBorder:
                                                                            OutlineInputBorder(
                                                                          borderSide: BorderSide(
                                                                              color: Colors.black,
                                                                              width: 2.0),
                                                                          borderRadius:
                                                                              BorderRadius.zero,
                                                                        ),
                                                                        border:
                                                                            OutlineInputBorder(
                                                                          borderSide: BorderSide(
                                                                              color: Colors.black,
                                                                              width: 2.0),
                                                                          borderRadius:
                                                                              BorderRadius.zero,
                                                                        ),
                                                                        errorBorder:
                                                                            OutlineInputBorder(
                                                                          borderSide:
                                                                              BorderSide(
                                                                            color:
                                                                                Colors.redAccent,
                                                                            width:
                                                                                2.0,
                                                                          ),
                                                                          borderRadius:
                                                                              BorderRadius.all(
                                                                            Radius.circular(
                                                                              19.0,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                        hintText:
                                                                            'Type here',
                                                                      ),
                                                                      focusNode:
                                                                          FocusNode(),
                                                                      // The validator receives the text that the user has entered.
                                                                      onChanged:
                                                                          (val) {
                                                                        reasonText =
                                                                            val;
                                                                      },
                                                                      validator:
                                                                          (value) {
                                                                        if (value!.trim() !=
                                                                            'DELETE MY ACCOUNT') {
                                                                          return "incorrect";
                                                                        }
                                                                        return null;
                                                                      },
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      height:
                                                                          40),
                                                                  Obx(() =>
                                                                      SizedBox(
                                                                        height:
                                                                            46,
                                                                        child: accountController.loading.value
                                                                            ? SizedBox(
                                                                                width: double.infinity,
                                                                                child: ElevatedButton(
                                                                                  onPressed: null,
                                                                                  child: SizedBox(
                                                                                    height: 20,
                                                                                    child: LoadingIndicator(
                                                                                      colors: [
                                                                                        Theme.of(context).primaryColor,
                                                                                        Colors.black,
                                                                                      ],
                                                                                      indicatorType: Indicator.cubeTransition,
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              )
                                                                            : SubmitButton(
                                                                                bgcolor: Colors.white,
                                                                                text: 'Confirm',
                                                                                onpressed: () async {
                                                                                  if (_formKeyconfirm.currentState!.validate()) {}
                                                                                },
                                                                                txtcolor: Theme.of(context).primaryColor,
                                                                                side: Border.all(color: Theme.of(context).primaryColor),
                                                                              ),
                                                                      )),
                                                                  const SizedBox(
                                                                      height:
                                                                          20),
                                                                  SizedBox(
                                                                    height: 46,
                                                                    child:
                                                                        SubmitButton(
                                                                      onpressed:
                                                                          () {
                                                                        Get.back();
                                                                      },
                                                                      text:
                                                                          'Cancel',
                                                                    ),
                                                                  )
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  );
                                                },
                                                style: TextButton.styleFrom(
                                                  backgroundColor: Colors.red,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 10,
                                                      horizontal: 25),
                                                  shape:
                                                      const RoundedRectangleBorder(
                                                    side: BorderSide(
                                                      color: Colors.red,
                                                      width: 1,
                                                    ),
                                                  ),
                                                ),
                                                child: const Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.warning,
                                                      color: Colors.white,
                                                    ),
                                                    SizedBox(width: 10),
                                                    Text(
                                                      'Delete Anyway',
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: 18,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                );
                              },
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: AppColors.ctaBackgroundColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            vertical: 13,
                            horizontal: 30,
                          ),
                        ),
                        child: const Text(
                          'Delete Account',
                          style: TextStyle(fontSize: 17),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> deletestatusdialog(BuildContext context) async {
  return showDialog(
    useRootNavigator: false,
    barrierDismissible: false,
    context: context,
    builder: ((context) {
      return Dialog(
        insetPadding: const EdgeInsets.all(24),
        child: Container(
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      Get.to(() => const LoadingScreen(text: "Logout"));
                      GetStorage('cart').erase();
                      final cartController = Get.find<CartController>();
                      final accountController = Get.find<AccountController>();
                      final UserController userController = Get.find();

                      userController.userdata.value = null;
                      await DatabaseHelper.instance.clearAllOrders();
                      await DatabaseHelper.instance.clearShopingCart();
                      await DatabaseHelper.instance.clearPayment();

                      accountController.addressModel.value = AddressModel();
                      await cartController.clearCart();

                      // TODO: assign null and test
                      accountController.selectedAddress.value = Address();

                      Future.delayed(
                        const Duration(seconds: 1),
                        () => Get.offAll(() => const LoginScreen()),
                      );
                      storage.erase();
                    },
                    icon: Icon(
                      Icons.logout_sharp,
                      color: Theme.of(context).primaryColor,
                    ),
                    label: Text(
                      'Logout',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  )
                ],
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 20),
                  SvgPicture.asset('assets/svg/deletebin.svg'),
                  const SizedBox(height: 30),
                  const Text('Your account is being deleted',
                      style:
                          TextStyle(fontWeight: FontWeight.w700, fontSize: 16)),
                  const SizedBox(height: 14),
                  const Text(
                    'You have 30 business days to cancel your deletion request, if you don’t want to lose any account data.',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                      'To cancel your request kindly write an email by clicking below',
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14)),
                  const SizedBox(height: 35),
                  SubmitButton(
                      text: 'Write an Email',
                      onpressed: () async {
                        if (!await launchUrl(Uri.parse(
                            "mailto:<EMAIL>?subject=Undo Deletion of Rapsap Account &body="))) {
                          throw "Could not launch email";
                        }
                      })
                ],
              ).paddingAll(20),
            ],
          ),
        ),
      );
    }),
  );
}
