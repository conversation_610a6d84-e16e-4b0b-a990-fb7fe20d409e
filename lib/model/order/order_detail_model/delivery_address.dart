import 'dart:convert';

class DeliveryAddress {
  String? city;
  String? name;
  String? phone;
  String? state;
  String? pincode;
  String? address1;
  String? address2;
  String? latitude;
  String? longitude;
  String? addressType;

  DeliveryAddress({
    this.city,
    this.name,
    this.phone,
    this.state,
    this.pincode,
    this.address1,
    this.address2,
    this.latitude,
    this.longitude,
    this.addressType,
  });

  factory DeliveryAddress.fromMap(Map<String, dynamic> data) {
    return DeliveryAddress(
      city: data['city'] as String?,
      name: data['name'] as String?,
      phone: data['phone'] as String?,
      state: data['state'] as String?,
      pincode: data['pincode'] as String?,
      address1: data['address1'] as String?,
      address2: data['address2'] as String?,
      latitude: data['latitude'] as String?,
      longitude: data['longitude'] as String?,
      addressType: data['address_type'] as String?,
    );
  }

  Map<String, dynamic> toMap() => {
        'city': city,
        'name': name,
        'phone': phone,
        'state': state,
        'pincode': pincode,
        'address1': address1,
        'address2': address2,
        'latitude': latitude,
        'longitude': longitude,
        'address_type': addressType,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [DeliveryAddress].
  factory DeliveryAddress.fromJson(String data) {
    return DeliveryAddress.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [DeliveryAddress] to a JSON string.
  String toJson() => json.encode(toMap());
}
