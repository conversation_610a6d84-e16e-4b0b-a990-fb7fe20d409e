import 'dart:convert';

class Delivery {
  int? id;
  dynamic sfxId;
  String? status;
  dynamic trackUrl;
  String? deliveryCost;

  Delivery({
    this.id,
    this.sfxId,
    this.status,
    this.trackUrl,
    this.deliveryCost,
  });

  factory Delivery.fromMap(Map<String, dynamic> data) => Delivery(
        id: data['id'] as int?,
        sfxId: data['sfx_id'] as dynamic,
        status: data['status'] as String?,
        trackUrl: data['track_url'] as dynamic,
        deliveryCost: data['delivery_cost'] as String?,
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'sfx_id': sfxId,
        'status': status,
        'track_url': trackUrl,
        'delivery_cost': deliveryCost,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Delivery].
  factory Delivery.fromJson(String data) {
    return Delivery.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Delivery] to a JSON string.
  String toJson() => json.encode(toMap());
}
