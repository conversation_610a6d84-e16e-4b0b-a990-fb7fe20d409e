import 'dart:convert';

import 'refund.dart';
import 'delivery.dart';
import 'delivery_address.dart';
import 'order_detail.dart';
import 'order_log.dart';

class Data {
  int? orderId;
  String? orderStatus;
  String? orderType;
  DateTime? createdAt;
  int? addressId;
  String? firstName;
  dynamic lastName;
  String? mobile;
  int? storeId;
  String? storeName;
  String? storeCode;
  String? name;
  String? brandName;
  int? brandId;
  int? paymentId;
  String? paymentStatus;
  dynamic invoiceUrl;
  dynamic referenceId;
  dynamic razorpayRefId;
  String? subTotal;
  String? gst;
  String? discount;
  String? grandTotal;
  String? deliveryCost;
  DeliveryAddress? deliveryAddress;
  List<OrderDetail>? orderDetails;
  List<OrderLog>? orderLogs;
  List<Refund>? refund;

  Delivery? delivery;

  Data(
      {this.orderId,
      this.orderStatus,
      this.orderType,
      this.createdAt,
      this.addressId,
      this.firstName,
      this.lastName,
      this.mobile,
      this.storeId,
      this.storeName,
      this.storeCode,
      this.name,
      this.brandName,
      this.brandId,
      this.paymentId,
      this.paymentStatus,
      this.invoiceUrl,
      this.referenceId,
      this.razorpayRefId,
      this.subTotal,
      this.gst,
      this.discount,
      this.grandTotal,
      this.deliveryCost,
      this.deliveryAddress,
      this.orderDetails,
      this.orderLogs,
      this.delivery,
      this.refund});

  factory Data.fromMap(Map<String, dynamic> data) => Data(
        orderId: data['order_id'] as int?,
        orderStatus: data['order_status'] as String?,
        orderType: data['order_type'] as String?,
        createdAt: data['created_at'] == null
            ? null
            : DateTime.parse(data['created_at'] as String),
        addressId: data['address_id'] as int?,
        firstName: data['first_name'] as String?,
        lastName: data['last_name'] as dynamic,
        mobile: data['mobile'] as String?,
        storeId: data['store_id'] as int?,
        storeName: data['store_name'] as String?,
        storeCode: data['store_code'] as String?,
        name: data['name'] as String?,
        brandName: data['brand_name'] as String?,
        brandId: data['brand_id'] as int?,
        paymentId: data['payment_id'] as int?,
        paymentStatus: data['payment_status'] as String?,
        invoiceUrl: data['invoice_url'] as dynamic,
        referenceId: data['reference_id'] as dynamic,
        razorpayRefId: data['razorpay_ref_id'] as dynamic,
        subTotal: data['sub_total'] as String?,
        gst: data['gst'] as String?,
        discount: data['discount'] as String?,
        grandTotal: data['grand_total'] as String?,
        deliveryCost: data['delivery_cost'] as String?,
        deliveryAddress: data['delivery_address'] == null
            ? null
            : DeliveryAddress.fromMap(
                data['delivery_address'] as Map<String, dynamic>),
        orderDetails: (data['OrderDetails'] as List<dynamic>?)
            ?.map((e) => OrderDetail.fromMap(e as Map<String, dynamic>))
            .toList(),
        orderLogs: (data['OrderLogs'] as List<dynamic>?)
            ?.map((e) => OrderLog.fromMap(e as Map<String, dynamic>))
            .toList(),
        refund: (data['refund'] as List<dynamic>?)
            ?.map((e) => Refund.fromMap(e as Map<String, dynamic>))
            .toList(),
        delivery: data['delivery'] == null
            ? null
            : Delivery.fromMap(data['delivery'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        'order_id': orderId,
        'order_status': orderStatus,
        'order_type': orderType,
        'created_at': createdAt?.toIso8601String(),
        'address_id': addressId,
        'first_name': firstName,
        'last_name': lastName,
        'mobile': mobile,
        'store_id': storeId,
        'store_name': storeName,
        'store_code': storeCode,
        'name': name,
        'brand_name': brandName,
        'brand_id': brandId,
        'payment_id': paymentId,
        'payment_status': paymentStatus,
        'invoice_url': invoiceUrl,
        'reference_id': referenceId,
        'razorpay_ref_id': razorpayRefId,
        'sub_total': subTotal,
        'gst': gst,
        'discount': discount,
        'grand_total': grandTotal,
        'delivery_cost': deliveryCost,
        'delivery_address': deliveryAddress?.toMap(),
        'OrderDetails': orderDetails?.map((e) => e.toMap()).toList(),
        'OrderLogs': orderLogs?.map((e) => e.toMap()).toList(),
        'delivery': delivery?.toMap(),
        'refund': refund?.map((e) => e.toMap()).toList(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Data].
  factory Data.fromJson(String data) {
    return Data.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Data] to a JSON string.
  String toJson() => json.encode(toMap());
}
