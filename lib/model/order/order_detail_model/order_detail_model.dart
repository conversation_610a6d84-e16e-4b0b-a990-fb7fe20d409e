import 'dart:convert';

import 'data.dart';

class OrderDetailModel {
  bool? success;
  String? msg;
  Data? data;

  OrderDetailModel({this.success, this.msg, this.data});

  factory OrderDetailModel.fromMap(Map<String, dynamic> data) {
    return OrderDetailModel(
      success: data['success'] as bool?,
      msg: data['msg'] as String?,
      data: data['data'] == null
          ? null
          : Data.fromMap(data['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [OrderDetailModel].
  factory OrderDetailModel.fromJson(String data) {
    return OrderDetailModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [OrderDetailModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
