import 'datum.dart';
import 'meta.dart';

class OrderListModel {
  bool? success;
  String? msg;
  List<OrderModel>? data;
  Meta? meta;

  OrderListModel({
    this.success,
    this.msg,
    this.data,
    this.meta,
  });

  factory OrderListModel.fromJson(Map<String, dynamic> data) {
    return OrderListModel(
      success: data['success'] as bool?,
      msg: data['msg'] as String?,
      data: (data['data'] as List<dynamic>?)
          ?.map((e) => OrderModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: data['meta'] == null
          ? null
          : Meta.fromJson(data['meta'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toJson()).toList(),
        'meta': meta?.toJson(),
      };
}
