import '../../address_model.dart';
import '../../item_list_model/discount.dart';
import '../../tax.dart';

class OrderModel {
  OrderAddressModel? address;
  String? id;
  String? type;
  String? salesOrderNumber;
  DateTime? orderDate;
  List<OrderListModelItem>? items;
  int? total;
  String? status;
  String? paymentMethod;

  OrderModel({
    this.address,
    this.id,
    this.type,
    this.salesOrderNumber,
    this.orderDate,
    this.items,
    this.total,
    this.status,
    this.paymentMethod,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        address: json["address"] == null
            ? null
            : OrderAddressModel.fromJson(json["address"]),
        id: json["_id"],
        type: json["type"],
        salesOrderNumber: json["sales_order_number"],
        orderDate: json["order_date"] == null
            ? null
            : DateTime.tryParse(json["order_date"])?.toLocal(),
        items: json["items"] == null
            ? []
            : List<OrderListModelItem>.from(
                json["items"]!.map((x) => OrderListModelItem.fromJson(x))),
        total: double.tryParse(json["total"].toString())?.floor(),
        status: json["status"],
        paymentMethod: json["payment_method"],
      );

  Map<String, dynamic> toJson() => {
        "address": address?.toJson(),
        "_id": id,
        "type": type,
        "sales_order_number": salesOrderNumber,
        "order_date": orderDate?.toIso8601String(),
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "total": total,
        "status": status,
        "payment_method": paymentMethod,
      };
}

class OrderAddressModel {
  GeoLocation? geoLocation;
  String? street;
  String? house;
  String? city;
  String? landmark;
  String? pincode;

  OrderAddressModel({
    this.geoLocation,
    this.street,
    this.house,
    this.city,
    this.landmark,
    this.pincode,
  });

  factory OrderAddressModel.fromJson(Map<String, dynamic> json) =>
      OrderAddressModel(
        geoLocation: GeoLocation.fromJson(json['geoLocation']),
        street: json["street"],
        house: json["house"],
        city: json["city"],
        landmark: json["landmark"],
        pincode: json["pincode"],
      );

  Map<String, dynamic> toJson() => {
        "geoLocation": geoLocation?.toJson(),
        "street": street,
        "house": house,
        "city": city,
        "landmark": landmark,
        "pincode": pincode,
      };
  @override
  String toString() {
    return "$house, $street, $landmark, $city, $pincode";
  }
}

class OrderListModelItem {
  OrderListModelItemId? itemId;
  String? quantity;
  String? rate;
  String? id;
  List<Tax> tax;
  int? total;

  OrderListModelItem({
    this.itemId,
    this.quantity,
    this.rate,
    this.id,
    this.tax = const [],
    this.total,
  });

  factory OrderListModelItem.fromJson(Map<String, dynamic> json) =>
      OrderListModelItem(
        itemId: json["item_id"] == null
            ? null
            : OrderListModelItemId.fromJson(json["item_id"]),
        quantity: json["quantity"].toString(),
        rate: json["rate"].toString(),
        id: json["_id"],
        tax: json['tax'] == null
            ? []
            : List<Tax>.from(json['tax']!.map((e) => Tax.fromJson(e))),
        total: double.tryParse(json['total'].toString())?.floor(),
      );

  Map<String, dynamic> toJson() => {
        "item_id": itemId?.toJson(),
        "quantity": quantity,
        "rate": rate,
        "_id": id,
        "tax": tax.map((e) => e.toJson()),
        "total": total,
      };
}

class OrderListModelItemId {
  final String? id;
  final String? name;
  final String? description;
  final String? unit;
  final String? sku;
  final String? upc;
  final String? ean;
  final String? mpn;
  final String? isbn;
  final String? hsn;
  final String? dimension;
  final String? weight;
  final List<Tax>? tax;
  final String? type;
  final List<dynamic>? variants;
  final List<String>? images;
  final String? category;
  final bool? returnable;
  final String? manufacturer;
  final String? brand;
  final List<SalesInfo>? salesInfo;
  final List<PurchaseInfo>? purchaseInfo;
  final List<InventoryInfo>? inventoryInfo;
  final bool? availableForSales;
  final bool? availableForPurchase;
  final bool? trackInventory;
  final bool? isActive;
  final List<String>? salesChannels;
  final List<dynamic>? attributeOptions;
  final List<dynamic>? variantAttributes;
  final List<dynamic>? items;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;

  OrderListModelItemId({
    this.id,
    this.name,
    this.description,
    this.unit,
    this.sku,
    this.upc,
    this.ean,
    this.mpn,
    this.isbn,
    this.hsn,
    this.dimension,
    this.weight,
    this.tax,
    this.type,
    this.variants,
    this.images,
    this.category,
    this.returnable,
    this.manufacturer,
    this.brand,
    this.salesInfo,
    this.purchaseInfo,
    this.inventoryInfo,
    this.availableForSales,
    this.availableForPurchase,
    this.trackInventory,
    this.isActive,
    this.salesChannels,
    this.attributeOptions,
    this.variantAttributes,
    this.items,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory OrderListModelItemId.fromJson(Map<String, dynamic> json) =>
      OrderListModelItemId(
        id: json["_id"],
        name: json["name"],
        description: json["description"],
        unit: json["unit"],
        sku: json["SKU"],
        upc: json["UPC"],
        ean: json["EAN"],
        mpn: json["MPN"],
        isbn: json["ISBN"],
        hsn: json["HSN"],
        dimension: json["dimension"],
        weight: json["weight"],
        tax: json["tax"] == null
            ? []
            : List<Tax>.from(json["tax"]!.map((x) => Tax.fromJson(x))),
        type: json["type"],
        variants: json["variants"] == null
            ? []
            : List<dynamic>.from(json["variants"]!.map((x) => x)),
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        category: json["category"],
        returnable: json["returnable"],
        manufacturer: json["manufacturer"],
        brand: json["brand"],
        salesInfo: json["salesInfo"] == null
            ? []
            : List<SalesInfo>.from(
                json["salesInfo"]!.map((x) => SalesInfo.fromJson(x))),
        purchaseInfo: json["purchaseInfo"] == null
            ? []
            : List<PurchaseInfo>.from(
                json["purchaseInfo"]!.map((x) => PurchaseInfo.fromJson(x))),
        inventoryInfo: json["inventoryInfo"] == null
            ? []
            : List<InventoryInfo>.from(
                json["inventoryInfo"]!.map((x) => InventoryInfo.fromJson(x))),
        availableForSales: json["availableForSales"],
        availableForPurchase: json["availableForPurchase"],
        trackInventory: json["trackInventory"],
        isActive: json["isActive"],
        salesChannels: json["salesChannels"] == null
            ? []
            : List<String>.from(json["salesChannels"]!.map((x) => x)),
        attributeOptions: json["attributeOptions"] == null
            ? []
            : List<dynamic>.from(json["attributeOptions"]!.map((x) => x)),
        variantAttributes: json["variantAttributes"] == null
            ? []
            : List<dynamic>.from(json["variantAttributes"]!.map((x) => x)),
        items: json["items"] == null
            ? []
            : List<dynamic>.from(json["items"]!.map((x) => x)),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "description": description,
        "unit": unit,
        "SKU": sku,
        "UPC": upc,
        "EAN": ean,
        "MPN": mpn,
        "ISBN": isbn,
        "HSN": hsn,
        "dimension": dimension,
        "weight": weight,
        "tax":
            tax == null ? [] : List<dynamic>.from(tax!.map((x) => x.toJson())),
        "type": type,
        "variants":
            variants == null ? [] : List<dynamic>.from(variants!.map((x) => x)),
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "category": category,
        "returnable": returnable,
        "manufacturer": manufacturer,
        "brand": brand,
        "salesInfo": salesInfo == null
            ? []
            : List<dynamic>.from(salesInfo!.map((x) => x.toJson())),
        "purchaseInfo": purchaseInfo == null
            ? []
            : List<dynamic>.from(purchaseInfo!.map((x) => x.toJson())),
        "inventoryInfo": inventoryInfo == null
            ? []
            : List<dynamic>.from(inventoryInfo!.map((x) => x.toJson())),
        "availableForSales": availableForSales,
        "availableForPurchase": availableForPurchase,
        "trackInventory": trackInventory,
        "isActive": isActive,
        "salesChannels": salesChannels == null
            ? []
            : List<dynamic>.from(salesChannels!.map((x) => x)),
        "attributeOptions": attributeOptions == null
            ? []
            : List<dynamic>.from(attributeOptions!.map((x) => x)),
        "variantAttributes": variantAttributes == null
            ? []
            : List<dynamic>.from(variantAttributes!.map((x) => x)),
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x)),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}

class InventoryInfo {
  final String? storeId;
  final String? inventoryAccount;
  final int? openingStock;
  final int? ratePerUnit;
  final int? reorderPoint;
  final int? availableStock;
  final String? id;

  InventoryInfo({
    this.storeId,
    this.inventoryAccount,
    this.openingStock,
    this.ratePerUnit,
    this.reorderPoint,
    this.availableStock,
    this.id,
  });

  factory InventoryInfo.fromJson(Map<String, dynamic> json) => InventoryInfo(
        storeId: json["storeId"],
        inventoryAccount: json["inventoryAccount"],
        openingStock: json["openingStock"],
        ratePerUnit: json["ratePerUnit"],
        reorderPoint: json["reorderPoint"],
        availableStock: json["availableStock"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "storeId": storeId,
        "inventoryAccount": inventoryAccount,
        "openingStock": openingStock,
        "ratePerUnit": ratePerUnit,
        "reorderPoint": reorderPoint,
        "availableStock": availableStock,
        "_id": id,
      };
}

class PurchaseInfo {
  final String? storeId;
  final int? costPrice;
  final String? purchaseAccount;
  final String? description;
  final List<Vendor>? vendors;
  final String? id;

  PurchaseInfo({
    this.storeId,
    this.costPrice,
    this.purchaseAccount,
    this.description,
    this.vendors,
    this.id,
  });

  factory PurchaseInfo.fromJson(Map<String, dynamic> json) => PurchaseInfo(
        storeId: json["storeId"],
        costPrice: json["costPrice"],
        purchaseAccount: json["purchaseAccount"],
        description: json["description"],
        vendors: json["vendors"] == null
            ? []
            : List<Vendor>.from(
                json["vendors"]!.map((x) => Vendor.fromJson(x))),
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "storeId": storeId,
        "costPrice": costPrice,
        "purchaseAccount": purchaseAccount,
        "description": description,
        "vendors": vendors == null
            ? []
            : List<dynamic>.from(vendors!.map((x) => x.toJson())),
        "_id": id,
      };
}

class Vendor {
  final String? id;
  final int? costPrice;

  Vendor({
    this.id,
    this.costPrice,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        id: json["_id"],
        costPrice: json["costPrice"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "costPrice": costPrice,
      };
}

class SalesInfo {
  final Discount? discount;
  final String? storeId;
  final int? sellingPrice;
  final int? mrp;
  final String? salesAccount;
  final String? description;
  final String? id;

  SalesInfo({
    this.discount,
    this.storeId,
    this.sellingPrice,
    this.mrp,
    this.salesAccount,
    this.description,
    this.id,
  });

  factory SalesInfo.fromJson(Map<String, dynamic> json) => SalesInfo(
        discount: json["discount"] == null
            ? null
            : Discount.fromJson(json["discount"]),
        storeId: json["storeId"],
        sellingPrice: json["sellingPrice"],
        mrp: json["MRP"],
        salesAccount: json["salesAccount"],
        description: json["description"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "discount": discount?.toJson(),
        "storeId": storeId,
        "sellingPrice": sellingPrice,
        "MRP": mrp,
        "salesAccount": salesAccount,
        "description": description,
        "_id": id,
      };
}
