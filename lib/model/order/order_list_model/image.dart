import 'dart:convert';

class OrderDetailImage {
  String? url;
  int? imageId;

  OrderDetailImage({this.url, this.imageId});

  factory OrderDetailImage.fromMap(Map<String, dynamic> data) =>
      OrderDetailImage(
        url: data['url'] as String?,
        imageId: data['image_id'] as int?,
      );

  Map<String, dynamic> toMap() => {
        'url': url,
        'image_id': imageId,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [OrderDetailImage].
  factory OrderDetailImage.fromJson(String data) {
    return OrderDetailImage.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [OrderDetailImage] to a JSON string.
  String toJson() => json.encode(toMap());
}
