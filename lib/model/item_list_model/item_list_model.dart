import '../../utils/constants.dart';
import '../tax.dart';
import 'discount.dart';

class ItemListModel {
  final List<Item> docs;
  final int? totalCount;

  ItemListModel({
    this.docs = const <Item>[],
    this.totalCount,
  });

  factory ItemListModel.fromJson(Map<String, dynamic> json,
          [bool isWishList = false]) =>
      ItemListModel(
        docs: json["docs"] == null
            ? []
            : List<Item>.from(
                json["docs"]!.map((x) => Item.fromJson(x, isWishList))),
        totalCount: json["totalCount"],
      );

  Map<String, dynamic> toJson() => {
        "docs": List<dynamic>.from(docs.map((x) => x.toJson())),
        "totalDocs": totalCount,
      };
}

enum ItemType { grouped, single, variant }

class Item {
  String? id;
  String? name;
  String? description;
  String? unit;
  String? weight;
  String? sku;
  String? upc;
  String? ean;
  String? mpn;
  String? isbn;
  final String? dimension;
  final List<Tax> tax;
  List<String> _images;
  List<String> get imageUrls => _images
      .map(
        (e) => "$baseURL/api$e",
      )
      .toList();
  bool? returnable;
  CustomObject? manufacturer;
  CustomObject? brand;
  // final Inventory? inventory;
  final Stock? _stock;
  final List<Stock> _stocks;
  bool? availableForSales;
  bool? availableForPurchase;
  bool? trackInventory;
  bool? isActive;

  // variables to use locally
  // TODO: implement discount when available in response
  Discount? discount;

  // getters
  bool get hasMultiplePrices => _stocks.isNotEmpty;
  Stock? get relevantStock {
    if (_stock != null) {
      return _stock;
    }
    return _stocks.firstOrNull;
  }

  double get price => relevantStock?.sellingPrice?.toDouble() ?? 0.0;
  double get mrp => relevantStock?.mrp?.toDouble() ?? 0.0;
  int get availableStock => relevantStock?.availableQuantity ?? 0;
  String? get purchaseOrderId => relevantStock?.purchaseOrderId;

  Item({
    this.id,
    this.name,
    this.description,
    this.unit,
    this.weight,
    this.sku,
    this.upc,
    this.ean,
    this.mpn,
    this.isbn,
    this.dimension,
    this.tax = const <Tax>[],
    List<String> images = const [],
    this.returnable,
    this.manufacturer,
    this.brand,
    Stock? stock,
    List<Stock> stocks = const [],
    this.availableForSales,
    this.availableForPurchase,
    this.trackInventory,
    this.isActive,
  })  : _stocks = stocks,
        _stock = stock,
        _images = images;

  factory Item.fromJson(Map<String, dynamic> json, [bool isWishList = false]) {
    final imagesValue = isWishList ? json["item"]["images"] : json["images"];
    final brandValue = isWishList ? json["item"]["brand"] : json["brand"];
    final taxValue = isWishList ? json["item"]["tax"] : json["tax"];
    final manufacturerValue =
        isWishList ? json["item"]["manufacturer"] : json["manufacturer"];
    final stockList =
        isWishList ? json['nearestStoreInventory'][0]['stock'] : json['stocks'];
    List<Stock> stocksValue = stockList == null || stockList is! List
        ? <Stock>[]
        : List<Stock>.from(stockList.map((x) => Stock.fromJson(x)));
    // ignore: cascade_invocations
    stocksValue.sort(
      (a, b) => a.availableQuantity.compareTo(b.availableQuantity),
    );
    stocksValue = stocksValue.reversed.toList();
    // ignore: cascade_invocations
    stocksValue.sort(
      (a, b) => (a.sellingPrice ?? 0).compareTo(b.sellingPrice ?? 0),
    );

    return Item(
      id: isWishList ? json["item"]["_id"] : json["_id"],
      name: isWishList ? json["item"]["name"] : json["name"],
      description:
          isWishList ? json["item"]["description"] : json["description"],
      unit: isWishList ? json["item"]["unit"] : json["unit"],
      weight: isWishList ? json["item"]["weight"] : json["weight"],
      sku: isWishList ? json["item"]["SKU"] : json["SKU"],
      upc: isWishList ? json["item"]["UPC"] : json["UPC"],
      ean: isWishList ? json["item"]["EAN"] : json["EAN"],
      mpn: isWishList ? json["item"]["MPN"] : json["MPN"],
      isbn: isWishList ? json["item"]["ISBN"] : json["ISBN"],
      dimension: isWishList ? json["item"]["dimension"] : json["dimension"],
      tax: taxValue == null
          ? []
          : List<Tax>.from(taxValue!.map((x) => Tax.fromJson(x))),
      images: imagesValue == null
          ? []
          : List<String>.from(imagesValue!.map((x) => x.toString())),
      returnable: isWishList ? json["item"]["returnable"] : json["returnable"],
      manufacturer: manufacturerValue == null
          ? null
          : CustomObject.fromJson(manufacturerValue),
      brand: brandValue == null ? null : CustomObject.fromJson(brandValue),
      stock: json['stocks'] == null || json['stocks'] is! Map
          ? null
          : Stock.fromJson(json['stocks']),
      stocks: stocksValue,
      availableForSales: isWishList
          ? json["item"]["availableForSales"]
          : json["availableForSales"],
      availableForPurchase: isWishList
          ? json["item"]["availableForPurchase"]
          : json["availableForPurchase"],
      trackInventory:
          isWishList ? json["item"]["trackInventory"] : json["trackInventory"],
      isActive: isWishList ? json["item"]["isActive"] : json["isActive"],
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "description": description,
        "unit": unit,
        "weight": weight,
        "SKU": sku,
        "UPC": upc,
        "EAN": ean,
        "MPN": mpn,
        "ISBN": isbn,
        "dimension": dimension,
        "tax": tax.map((x) => x.toJson()).toList(),
        "images": List.from(_images.map((x) => x)),
        "returnable": returnable,
        "manufacturer": manufacturer?.toJson(),
        "brand": brand?.toJson(),
        "availableForSales": availableForSales,
        "availableForPurchase": availableForPurchase,
        "trackInventory": trackInventory,
        "isActive": isActive,
        "stocks": _stock?.toJson() ?? _stocks.map((e) => e.toJson()).toList(),
      };
}

class CustomObject {
  String? id;
  String? name;
  String? phone;

  CustomObject({
    this.id,
    this.name,
    this.phone,
  });
  factory CustomObject.driverFromJson(Map<String, dynamic> json) =>
      CustomObject(
        id: json["_id"],
        name: json["name"],
        phone: json["phone"],
      );

  factory CustomObject.fromJson(Map<String, dynamic> json) => CustomObject(
        id: json["_id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      "_id": id,
      "name": name,
    };
    if (phone != null) {
      data['phone'] = phone;
    }
    return data;
  }
}

class Stock {
  final int availableQuantity;
  final String? purchaseOrderId;
  final int? sellingPrice;
  final int? mrp;
  final String? barcode;

  Stock({
    this.availableQuantity = 0,
    this.purchaseOrderId,
    this.sellingPrice,
    this.mrp,
    this.barcode,
  });

  factory Stock.fromJson(Map<String, dynamic> json) => Stock(
        availableQuantity: json["available_quantity"],
        purchaseOrderId: json["purchase_order_id"],
        sellingPrice: json["selling_price"],
        mrp: json["MRP"],
        barcode: json["barcode"],
      );

  Map<String, dynamic> toJson() => {
        "available_quantity": availableQuantity,
        "purchase_order_id": purchaseOrderId,
        "selling_price": sellingPrice,
        "MRP": mrp,
        "barcode": barcode,
      };
}
