import '../../utils/enumerations.dart';

class Discount {
  DiscountType? type;
  double? value;
  Discount({
    this.type,
    this.value,
  });
  factory Discount.fromJson(Map<String, dynamic> json) => Discount(
        type: json['type'] == "currency"
            ? DiscountType.currency
            : DiscountType.percent,
        value: double.tryParse(json['value'].toString()),
      );
  Map<String, dynamic> toJson() => {
        "type": type?.name,
        "value": value,
      };
}
