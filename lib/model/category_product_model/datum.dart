import 'dart:convert';

import 'image.dart';

class CategoryProducts {
  String? productId;
  String? name;
  String? description;
  String? category;
  int? categoryId;
  int? subCategoryId;
  String? subCategoryName;
  int? popularity;
  dynamic customerRating;
  List<Image>? images;
  // String? variantId;
  // String? variantName;
  // String? variantSku;
  double price;
  String? costPrice;
  double mrp;
  String? storingPrice;
  String? weight;
  // int? variantStock;
  // int? variantIsActive;
  int? isFavorite;

  CategoryProducts({
    this.productId,
    this.name,
    this.description,
    this.category,
    this.categoryId,
    this.subCategoryId,
    this.subCategoryName,
    this.popularity,
    this.customerRating,
    this.images,
    // this.variantId,
    // this.variantName,
    // this.variantSku,
    this.price = 0,
    this.costPrice,
    this.mrp = 0,
    this.storingPrice,
    this.weight,
    // this.variantStock,
    // this.variantIsActive,
    this.isFavorite,
  });

  // @override
  // String toString() {
  //   return 'CategoryProducts(productId: $productId, name: $name, description: $description, category: $category, categoryId: $categoryId, subCategoryId: $subCategoryId, subCategoryName: $subCategoryName, popularity: $popularity, customerRating: $customerRating, images: $images, variantId: $variantId, variantName: $variantName, variantSku: $variantSku, price: $price, costPrice: $costPrice, mrp: $mrp, storingPrice: $storingPrice, weight: $weight, variantStock: $variantStock, variantIsActive: $variantIsActive, isFavorite: $isFavorite)';
  // }

  factory CategoryProducts.fromMap(Map<String, dynamic> data) =>
      CategoryProducts(
        productId: data['_id'] as String?,
        name: data['name'] as String?,
        description: data['description'] as String?,
        category: data['category'] as String?,
        categoryId: data['category_id'] as int?,
        subCategoryId: data['sub_category_id'] as int?,
        subCategoryName: data['sub_category_name'] as String?,
        popularity: data['popularity'] as int?,
        customerRating: data['customer_rating'] as dynamic,
        images: (data['images'] as List<dynamic>?)
            ?.map((e) => Image.fromMap(e as Map<String, dynamic>))
            .toList(),
        // variantId: data['variant_id']?.toString(),
        // variantName: data['variant_name'] as String?,
        // variantSku: data['variant_sku'] as String?,
        price: data['price'] == null ||
                double.tryParse(data['price']?.toString() ?? "0") == 0
            ? (double.tryParse(data['mrp']?.toString() ?? "0") ?? 0)
            : (double.tryParse(data['price']?.toString() ?? "0") ?? 0),
        costPrice: data['cost_price'] as String?,
        mrp: double.tryParse(data['mrp']?.toString() ?? "0") ?? 0,
        storingPrice: data['storing_price'] as String?,
        weight: data['weight'] as String?,
        // variantStock: data['variant_stock'] as int?,
        // variantIsActive: data['variant_is_active'] as int?,
        isFavorite: data['is_favorite'] as int?,
      );

  Map<String, dynamic> toMap() => {
        '_id': productId,
        'name': name,
        'description': description,
        'category': category,
        'category_id': categoryId,
        'sub_category_id': subCategoryId,
        'sub_category_name': subCategoryName,
        'popularity': popularity,
        'customer_rating': customerRating,
        'images': images?.map((e) => e.toMap()).toList(),
        // 'variant_id': variantId,
        // 'variant_name': variantName,
        // 'variant_sku': variantSku,
        'price': price.toString(),
        'cost_price': costPrice,
        'mrp': mrp.toString(),
        'storing_price': storingPrice,
        'weight': weight,
        // 'variant_stock': variantStock,
        // 'variant_is_active': variantIsActive,
        'is_favorite': isFavorite,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [CategoryProducts].
  factory CategoryProducts.fromJson(String data) {
    return CategoryProducts.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [CategoryProducts] to a JSON string.
  String toJson() => json.encode(toMap());
}
