class ItemNotInStock {
  final String? itemId;
  final int? quantity;
  final int? availableStock;

  ItemNotInStock({
    this.itemId,
    this.quantity,
    this.availableStock,
  });

  factory ItemNotInStock.fromJson(Map<String, dynamic> json) => ItemNotInStock(
        itemId: json["item_id"],
        quantity: json["quantity"],
        availableStock: json["availableStock"],
      );

  Map<String, dynamic> toJson() => {
        "item_id": itemId,
        "quantity": quantity,
        "availableStock": availableStock,
      };
}
