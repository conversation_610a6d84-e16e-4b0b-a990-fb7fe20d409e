class LocalOrder {
  final int? id;
  final int? addressID;
  final int? storeID;
  final double? subTotal;
  final double? grandTotal;
  final double? discount;
  final double? deliveryCharge;
  final double? tax;
  final int? orderID;
  final int? offerID;
  final double? minOrder;
  final String? couponCode;

  LocalOrder({
    this.id,
    this.addressID,
    this.deliveryCharge,
    this.discount,
    this.subTotal,
    this.grandTotal,
    this.storeID,
    this.tax,
    this.orderID,
    this.offerID,
    this.minOrder,
    this.couponCode,
  });

  factory LocalOrder.fromMap(Map<String, dynamic> json) => LocalOrder(
        id: json['id'],
        addressID: json['addressID'],
        deliveryCharge: json['deliveryCharge'],
        discount: json['discount'],
        subTotal: json['subTotal'],
        tax: json['tax'],
        grandTotal: json['grandTotal'],
        storeID: json['storeID'],
        orderID: json['orderID'],
        offerID: json['offerID'],
        minOrder: json['minOrder'],
        couponCode: json['couponCode'],
      );
  factory LocalOrder.fromMap1(Map<String, dynamic> json) => LocalOrder(
      id: json['id'],
      addressID: json['addressID'],
      deliveryCharge: json['deliveryCharge'],
      discount: json['discount'],
      subTotal: json['subTotal'],
      tax: json['tax'],
      grandTotal: json['grandTotal'],
      storeID: json['storeID'],
      orderID: json['orderID'],
      offerID: json['offerID'],
      minOrder: json['minOrder'],
      couponCode: json['couponCode']);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'addressID': addressID,
      'storeID': storeID,
      'deliveryCharge': deliveryCharge,
      'discount': discount,
      'subTotal': subTotal,
      'grandTotal': grandTotal,
      'tax': tax,
      'orderID': orderID,
      'offerID': offerID,
      'minOrder': minOrder,
      'couponCode': couponCode
    };
  }
}
