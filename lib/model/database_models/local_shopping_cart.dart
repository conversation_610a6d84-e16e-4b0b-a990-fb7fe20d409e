class LocalShoppingCart {
  final int? id;
  final String? name;
  final int? qty;
  final String productID;
  final String purchaseOrderID;
  final String? imageURL;
  final double? weight;
  final double? price;
  final double? mrp;
  final int? availableStock;

  /// in rupees
  final double? discount;

  /// in rupees
  final double? tax;

  final int? dateTime;

  LocalShoppingCart({
    this.id,
    this.name,
    this.qty,
    required this.productID,
    required this.purchaseOrderID,
    this.imageURL,
    this.weight,
    this.price,
    this.mrp,
    this.availableStock,
    this.discount,
    this.tax,
    dateTime,
  }) : dateTime = (dateTime ?? DateTime.now().millisecondsSinceEpoch);

  factory LocalShoppingCart.fromMap(Map<String, dynamic> json) =>
      LocalShoppingCart(
        id: json['id'],
        dateTime: json["date"] ?? DateTime.now(),
        name: json['name'],
        qty: json['qty'],
        productID: json['productID'],
        purchaseOrderID: json['purchaseOrderID'],
        imageURL: json['imageURL'],
        weight: json['weight'],
        price: json['price'],
        mrp: json['mrp'],
        availableStock: json["availableStock"],
        discount: json['discount'],
        tax: json['tax'],
      );

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': dateTime,
      'name': name,
      'qty': qty,
      'productID': productID,
      'purchaseOrderID': purchaseOrderID,
      'imageURL': imageURL,
      'weight': weight,
      'price': price,
      'mrp': mrp,
      'availableStock': availableStock,
      'discount': discount,
      'tax': tax,
    };
  }
}
