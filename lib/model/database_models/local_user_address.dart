class LocalUserAddress {
  final int? id;
  final String? name;
  final String? addressType;
  final int? isDefault;
  final String? phone;
  final String? address1;
  final String? address2;
  final String? city;
  final String? state;
  final String? pincode;
  final String? latitude;
  final String? longitude;
  final int? addressID;

  LocalUserAddress({
    this.id,
    this.name,
    this.address1,
    this.address2,
    this.addressID,
    this.addressType,
    this.city,
    this.isDefault,
    this.latitude,
    this.longitude,
    this.phone,
    this.pincode,
    this.state,
  });

  factory LocalUserAddress.fromMap(Map<String, dynamic> json) =>
      LocalUserAddress(
        id: json['id'],
        name: json['name'],
        address1: json['address1'],
        address2: json['address2'],
        addressID: json['addressID'],
        addressType: json['addressType'],
        city: json['city'],
        isDefault: json['isDefault'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        phone: json['phone'],
        pincode: json['pincode'],
        state: json['state'],
      );

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address1': address1,
      'address2': address2,
      'addressID': addressID,
      'addressType': addressType,
      'city': city,
      'isDefault': isDefault,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'pincode': pincode,
      'state': state,
    };
  }
}
