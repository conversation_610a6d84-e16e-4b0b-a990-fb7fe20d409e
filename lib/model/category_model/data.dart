import '../../utils/constants.dart';

class CategoryItemModel {
  CategoryItemModel({
    this.id,
    this.name,
    String? imageName,
    this.subCategories = const [],
  }) : _imageName = imageName;

  String? id;
  String? name;
  String? _imageName;
  String? get imageUrl => _imageName == null ? null : '$baseURL/api$_imageName';
  List<CategoryItemModel> subCategories;

  factory CategoryItemModel.fromJson(Map<String, dynamic> json) =>
      CategoryItemModel(
        id: json["_id"],
        name: json["name"],
        imageName: json["image"],
        subCategories: json['subCategories'] == null
            ? []
            : List.from((json['subCategories'] as List?)?.map(
                  (e) => CategoryItemModel.fromJson(e),
                ) ??
                []),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "image": _imageName,
        'subCategories': subCategories.map(
          (e) => e.toJson(),
        ),
      };
}
