import 'dart:convert';

import 'data.dart';

CategoryModel categoryModelFromJson(String str) =>
    CategoryModel.fromJson(json.decode(str));

String categoryModelToJson(CategoryModel data) => json.encode(data.toJson());

class CategoryModel {
  CategoryModel({
    this.data,
  });

  List<CategoryItemModel>? data;

  factory CategoryModel.fromJson(Map<String, dynamic> json) => CategoryModel(
        data: List<CategoryItemModel>.from(
            json["docs"].map((x) => CategoryItemModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}
