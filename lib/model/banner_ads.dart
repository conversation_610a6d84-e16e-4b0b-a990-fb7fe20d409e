import '../utils/constants.dart';

class BannerAd {
  final String? id;
  final String? title;
  final String? position;
  final String? linkPage;
  final String? description;
  final DateTime? start;
  final DateTime? end;
  final String? _image;

  String? get imageUrl => _image == null ? null : "$baseURL/api$_image";

  BannerAd({
    this.id,
    this.title,
    this.position,
    this.linkPage,
    this.description,
    this.start,
    this.end,
    String? image,
  }) : _image = image;

  factory BannerAd.fromJson(Map<String, dynamic> json) => BannerAd(
        id: json["_id"],
        title: json["title"],
        position: json["position"],
        linkPage: json["link_page"],
        description: json["description"],
        start: json["start"] == null
            ? null
            : DateTime.parse(json["start"].toString()),
        end:
            json["end"] == null ? null : DateTime.parse(json["end"].toString()),
        image: json["image"].toString(),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "position": position,
        "link_page": linkPage,
        "description": description,
        "start": start?.toIso8601String(),
        "end": end?.toIso8601String(),
        "image": _image,
      };
}
