class AddressModel {
  final bool? success;
  final String? msg;
  final List<Address>? data;
  final AddressMeta? meta;

  AddressModel({
    this.success,
    this.msg,
    this.data,
    this.meta,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
        success: json["success"],
        msg: json["msg"],
        data: json["data"] == null
            ? []
            : List<Address>.from(json["data"]!.map((x) => Address.fromJson(x))),
        meta: json["meta"] == null ? null : AddressMeta.fromJson(json["meta"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "meta": meta?.toJson(),
      };
}

class Address {
  final GeoLocation? geoLocation;
  final String? id;
  final String? name;
  final String? phone;
  final String? type;
  final String? line1;
  final String? line2;
  final String? landmark;
  final String? state;
  final String? city;
  final String? pincode;
  final int? v;

  Address({
    this.geoLocation,
    this.id,
    this.name,
    this.phone,
    this.type,
    this.line1,
    this.line2,
    this.landmark,
    this.state,
    this.city,
    this.pincode,
    this.v,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        geoLocation: json["geoLocation"] == null
            ? null
            : GeoLocation.fromJson(json["geoLocation"]),
        id: json["_id"],
        name: json["name"],
        phone: json["phone"],
        type: json["type"],
        line1: json["line1"],
        line2: json["line2"],
        landmark: json["landmark"],
        state: json["state"],
        city: json["city"],
        pincode: json["pincode"],
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "geoLocation": geoLocation?.toJson(),
        "_id": id,
        "name": name,
        "phone": phone,
        "type": type,
        "line1": line1,
        "line2": line2,
        "landmark": landmark,
        "state": state,
        "city": city,
        "pincode": pincode,
        "__v": v,
      };

  @override
  String toString() {
    return "$line1, $line2, $landmark, $city, $state, $pincode";
  }
}

class GeoLocation {
  /// [longitude, latitude]
  final List<double>? coordinates;
  final String? type;

  GeoLocation({
    this.coordinates,
    this.type,
  });

  factory GeoLocation.fromJson(Map<String, dynamic> json) => GeoLocation(
        coordinates: json["coordinates"] == null
            ? []
            : List<double>.from(json["coordinates"]!.map((x) => x?.toDouble())),
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "coordinates": coordinates == null
            ? []
            : List<dynamic>.from(coordinates!.map((x) => x)),
        "type": type,
      };

  GeoLocation copyWith(
    List<double>? coordinates,
    String? type,
  ) =>
      GeoLocation(
        coordinates: coordinates ?? this.coordinates,
        type: type ?? this.type,
      );
}

class AddressMeta {
  final int? totalDocs;
  final int? limit;
  final int? totalPages;
  final int? page;
  final int? pagingCounter;
  final bool? hasPrevPage;
  final bool? hasNextPage;
  final dynamic prevPage;
  final dynamic nextPage;

  AddressMeta({
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory AddressMeta.fromJson(Map<String, dynamic> json) => AddressMeta(
        totalDocs: json["totalDocs"],
        limit: json["limit"],
        totalPages: json["totalPages"],
        page: json["page"],
        pagingCounter: json["pagingCounter"],
        hasPrevPage: json["hasPrevPage"],
        hasNextPage: json["hasNextPage"],
        prevPage: json["prevPage"],
        nextPage: json["nextPage"],
      );

  Map<String, dynamic> toJson() => {
        "totalDocs": totalDocs,
        "limit": limit,
        "totalPages": totalPages,
        "page": page,
        "pagingCounter": pagingCounter,
        "hasPrevPage": hasPrevPage,
        "hasNextPage": hasNextPage,
        "prevPage": prevPage,
        "nextPage": nextPage,
      };
}
