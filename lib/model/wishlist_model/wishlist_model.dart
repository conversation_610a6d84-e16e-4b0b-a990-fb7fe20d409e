// To parse this JSON data, do
//
//     final wishlistModel = wishlistModelFromJson(jsonString?);

import 'dart:convert';

WishlistModel wishlistModelFromJson(String str) =>
    WishlistModel.fromJson(json.decode(str));

String? wishlistModelToJson(WishlistModel data) => json.encode(data.toJson());

class WishlistModel {
  WishlistModel({
    this.success,
    this.msg,
    this.data = const <WishlistItem>[],
    this.meta,
  });

  bool? success;
  String? msg;
  List<WishlistItem> data;
  Meta? meta;

  factory WishlistModel.fromJson(Map<String?, dynamic> json) => WishlistModel(
        success: json["success"],
        msg: json["msg"],
        data: List<WishlistItem>.from(
            json["data"]?.map((x) => WishlistItem.fromJson(x)) ?? []),
        meta: json["meta"] == null ? null : Meta.fromJson(json["meta"]),
      );

  Map<String?, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "meta": meta?.toJson(),
      };
}

class WishlistItem {
  WishlistItem({
    this.wishlistId,
    this.createdAt,
    this.name,
    this.description,
    this.productId,
    // this.variantId,
    this.stock,
    this.weight,
    this.price = 0,
    this.mrp = 0,
    this.costPrice,
    this.image,
  });

  dynamic wishlistId;
  DateTime? createdAt;
  String? name;
  String? description;
  dynamic productId;
  // dynamic variantId;
  dynamic stock;
  String? weight;
  double price;
  double mrp;
  String? costPrice;
  WishlistImage? image;

  double get discountPercent =>
      mrp == 0 ? 0 : ((100 * (mrp - price)) / mrp).floorToDouble();

  factory WishlistItem.fromJson(Map<String?, dynamic> json) => WishlistItem(
        wishlistId: json["wishlist_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        name: json["name"],
        description: json["description"],
        productId: json["product_id"],
        // variantId: json["variant_id"],
        stock: json["stock"],
        weight: json["weight"],
        price: double.tryParse(json["price"]?.toString() ?? "") ?? 0,
        mrp: double.tryParse(json["mrp"]?.toString() ?? "") ?? 0,
        costPrice: json["cost_price"],
        image: json["image"] == null
            ? null
            : WishlistImage.fromJson(json["image"]),
      );

  Map<String?, dynamic> toJson() => {
        "wishlist_id": wishlistId,
        "created_at": createdAt?.toIso8601String(),
        "name": name,
        "description": description,
        "product_id": productId,
        // "variant_id": variantId,
        "stock": stock,
        "weight": weight,
        "price": price,
        "mrp": mrp,
        "cost_price": costPrice,
        "image": image?.toJson(),
      };
}

class WishlistImage {
  WishlistImage({
    this.url,
    this.imageId,
  });

  String? url;
  dynamic imageId;

  factory WishlistImage.fromJson(Map<String?, dynamic> json) => WishlistImage(
        url: json["url"],
        imageId: json["image_id"],
      );

  Map<String?, dynamic> toJson() => {
        "url": url,
        "image_id": imageId,
      };
}

class Meta {
  Meta({
    this.total,
  });

  dynamic total;

  factory Meta.fromJson(Map<String?, dynamic> json) => Meta(
        total: json["total"],
      );

  Map<String?, dynamic> toJson() => {
        "total": total,
      };
}
