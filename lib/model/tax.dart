class Tax {
  final String? taxType;
  final int? value;
  final String? id;

  Tax({
    this.taxType,
    this.value,
    this.id,
  });

  factory Tax.fromJson(Map<String, dynamic> json) => Tax(
        taxType: json["taxType"],
        value: json["value"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "taxType": taxType,
        "value": value,
        "_id": id,
      };
}
