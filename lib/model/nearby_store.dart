import 'address_model.dart';

class NearbyStore {
  final String? id;
  final String? name;
  final StoreLocation? location;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;
  final double? distance;

  NearbyStore({
    this.id,
    this.name,
    this.location,
    this.createdAt,
    this.updatedAt,
    this.v,
    this.distance,
  });

  factory NearbyStore.fromJson(Map<String, dynamic> json) => NearbyStore(
        id: json["_id"],
        name: json["name"],
        location: json["location"] == null
            ? null
            : StoreLocation.fromJson(json["location"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
        distance: json["distance"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "location": location?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
        "distance": distance,
      };
}

class StoreLocation {
  final StoreAddress? address;
  final GeoLocation? geoLocation;

  /// in meters
  final int? serviceableAreaRadius;

  StoreLocation({
    this.address,
    this.geoLocation,
    this.serviceableAreaRadius,
  });

  factory StoreLocation.fromJson(Map<String, dynamic> json) => StoreLocation(
        address: json["address"] == null
            ? null
            : StoreAddress.fromJson(json["address"]),
        geoLocation: json["geoLocation"] == null
            ? null
            : GeoLocation.fromJson(json["geoLocation"]),
        serviceableAreaRadius: json["serviceableAreaRadius"],
      );

  Map<String, dynamic> toJson() => {
        "address": address?.toJson(),
        "geoLocation": geoLocation?.toJson(),
        "serviceableAreaRadius": serviceableAreaRadius,
      };
}

class StoreAddress {
  final String? line1;
  final String? line2;
  final String? state;
  final String? city;
  final String? pincode;

  StoreAddress({
    this.line1,
    this.line2,
    this.state,
    this.city,
    this.pincode,
  });

  factory StoreAddress.fromJson(Map<String, dynamic> json) => StoreAddress(
        line1: json["line1"],
        line2: json["line2"],
        state: json["state"],
        city: json["city"],
        pincode: json["pincode"],
      );

  Map<String, String?> toJson() => {
        "line1": line1,
        "line2": line2,
        "state": state,
        "city": city,
        "pincode": pincode,
      };
}
