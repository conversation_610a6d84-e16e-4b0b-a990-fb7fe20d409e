class CouponListModel {
  final List<Coupon> couponList;
  final int? totalDocs;
  final int? limit;
  final int? totalPages;
  final int? page;
  final int? pagingCounter;
  final bool? hasPrevPage;
  final bool? hasNextPage;
  final dynamic prevPage;
  final dynamic nextPage;

  CouponListModel({
    this.couponList = const [],
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory CouponListModel.fromJson(Map<String, dynamic> json) =>
      CouponListModel(
        couponList: json["docs"] == null
            ? []
            : List<Coupon>.from(json["docs"]!.map((x) => Coupon.fromJson(x))),
        totalDocs: json["totalDocs"],
        limit: json["limit"],
        totalPages: json["totalPages"],
        page: json["page"],
        pagingCounter: json["pagingCounter"],
        hasPrevPage: json["hasPrevPage"],
        hasNextPage: json["hasNextPage"],
        prevPage: json["prevPage"],
        nextPage: json["nextPage"],
      );

  Map<String, dynamic> toJson() => {
        "docs": List<dynamic>.from(couponList.map((x) => x.toJson())),
        "totalDocs": totalDocs,
        "limit": limit,
        "totalPages": totalPages,
        "page": page,
        "pagingCounter": pagingCounter,
        "hasPrevPage": hasPrevPage,
        "hasNextPage": hasNextPage,
        "prevPage": prevPage,
        "nextPage": nextPage,
      };
}

class Coupon {
  final String? id;
  final String? code;
  final String? minPurchaseAmount;
  final int? offerAmount;
  final int? noOfUses;
  final String? description;
  final String? offerBy;
  final List<Brand>? brands;
  final List<dynamic>? manufacturers;
  final List<dynamic>? categories;
  final List<Brand>? items;
  final List<Brand>? stores;
  final DateTime? from;
  final DateTime? to;
  final int? v;

  Coupon({
    this.id,
    this.code,
    this.minPurchaseAmount,
    this.offerAmount,
    this.noOfUses,
    this.description,
    this.offerBy,
    this.brands,
    this.manufacturers,
    this.categories,
    this.items,
    this.stores,
    this.from,
    this.to,
    this.v,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) => Coupon(
        id: json["_id"],
        code: json["code"],
        minPurchaseAmount: json["min_purchase_amount"],
        offerAmount: json["offer_amount"],
        noOfUses: json["no_of_uses"],
        description: json["description"],
        offerBy: json["offer_by"],
        brands: json["brands"] == null
            ? []
            : List<Brand>.from(json["brands"]!.map((x) => Brand.fromJson(x))),
        manufacturers: json["manufacturers"] == null
            ? []
            : List<dynamic>.from(json["manufacturers"]!.map((x) => x)),
        categories: json["categories"] == null
            ? []
            : List<dynamic>.from(json["categories"]!.map((x) => x)),
        items: json["items"] == null
            ? []
            : List<Brand>.from(json["items"]!.map((x) => Brand.fromJson(x))),
        stores: json["stores"] == null
            ? []
            : List<Brand>.from(json["stores"]!.map((x) => Brand.fromJson(x))),
        from: json["from"] == null ? null : DateTime.parse(json["from"]),
        to: json["to"] == null ? null : DateTime.parse(json["to"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "code": code,
        "min_purchase_amount": minPurchaseAmount,
        "offer_amount": offerAmount,
        "no_of_uses": noOfUses,
        "description": description,
        "offer_by": offerBy,
        "brands": brands == null
            ? []
            : List<dynamic>.from(brands!.map((x) => x.toJson())),
        "manufacturers": manufacturers == null
            ? []
            : List<dynamic>.from(manufacturers!.map((x) => x)),
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "stores": stores == null
            ? []
            : List<dynamic>.from(stores!.map((x) => x.toJson())),
        "from": from?.toIso8601String(),
        "to": to?.toIso8601String(),
        "__v": v,
      };

  @override
  // ignore: hash_and_equals
  bool operator ==(Object other) {
    return other is Coupon && other.id == id && other.code == code;
  }
}

class Brand {
  final String? id;
  final String? name;

  Brand({
    this.id,
    this.name,
  });

  factory Brand.fromJson(Map<String, dynamic> json) => Brand(
        id: json["_id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
      };
}
