// import 'dart:convert';

// import 'datum.dart';

// class HomePageProducts {
//   bool? success;
//   String? msg;
//   List<HomePageCategoryData>? data;

//   HomePageProducts({this.success, this.msg, this.data});

//   factory HomePageProducts.fromMap(Map<String, dynamic> data) {
//     return HomePageProducts(
//       success: data['success'] as bool?,
//       msg: data['msg'] as String?,
//       data: (data['data'] as List<dynamic>?)
//           ?.map((e) => HomePageCategoryData.fromMap(e as Map<String, dynamic>))
//           .toList(),
//     );
//   }

//   Map<String, dynamic> toMap() => {
//         'success': success,
//         'msg': msg,
//         'data': data?.map((e) => e.toMap()).toList(),
//       };

//   /// `dart:convert`
//   ///
//   /// Parses the string and returns the resulting Json object as [HomePageProducts].
//   factory HomePageProducts.fromJson(String data) {
//     return HomePageProducts.fromMap(json.decode(data) as Map<String, dynamic>);
//   }

//   /// `dart:convert`
//   ///
//   /// Converts [HomePageProducts] to a JSON string.
//   String toJson() => json.encode(toMap());
// }
