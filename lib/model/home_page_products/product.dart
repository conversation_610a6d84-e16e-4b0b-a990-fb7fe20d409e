class Product {
  int? mrp;
  int? price;
  String? images;
  // double? weight;
  String? product;
  String? category;
  int? costPrice;
  String? productId;
  // String? variantId;
  String? categoryId;
  int? isFavorite;
  // String? variantSku;
  // String? variantName;
  int? subCategoryId;
  String? subCategoryName;
  final int stock;

  Product({
    this.mrp,
    this.price,
    this.images,
    // this.weight,
    this.product,
    this.category,
    this.costPrice,
    this.productId,
    // this.variantId,
    this.categoryId,
    this.isFavorite,
    // this.variantSku,
    // this.variantName,
    this.subCategoryId,
    this.subCategoryName,
    this.stock = 0,
  });

  factory Product.fromMap(Map<String, dynamic> data) => Product(
        mrp: data['mrp'] as int?,
        price: data["price"] == 0 ? data["mrp"] : data['price'] as int?,
        images: data['images'] as String?,
        // weight: double.parse(data['weight'].toString()),
        product: data['product'] as String?,
        category: data['category'] as String?,
        costPrice: data['cost_price'] as int?,
        productId: data['_id'] as String?,
        // variantId: data['variant_id']?.toString(),
        categoryId: data['category_id'] as String?,
        isFavorite: data['is_favorite'] as int?,
        // variantSku: data['variant_sku'] as String?,
        // variantName: data['variant_name'] as String?,
        subCategoryId: data['sub_category_id'] as int?,
        subCategoryName: data['sub_category_name'] as String?,
        stock: (data["stock"] as int?) ?? 0,
      );

  Map<String, dynamic> toMap() => {
        'mrp': mrp,
        'price': price,
        'images': images,
        // 'weight': weight,
        'product': product,
        'category': category,
        'cost_price': costPrice,
        '_id': productId,
        // 'variant_id': variantId,
        'category_id': categoryId,
        'is_favorite': isFavorite,
        // 'variant_sku': variantSku,
        // 'variant_name': variantName,
        'sub_category_id': subCategoryId,
        'sub_category_name': subCategoryName,
        "stock": stock,
      };
}
