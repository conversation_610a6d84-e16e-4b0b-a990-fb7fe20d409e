// import 'dart:convert';

// import 'data.dart';

// class Products {
//   bool? success;
//   String? msg;
//   Data? data;

//   Products({this.success, this.msg, this.data});

//   factory Products.fromMap(Map<String, dynamic> data) => Products(
//         success: data['success'] as bool?,
//         msg: data['msg'] as String?,
//         data: data['data'] == null
//             ? null
//             : Data.fromMap(data['data'] as Map<String, dynamic>),
//       );

//   Map<String, dynamic> toMap() => {
//         'success': success,
//         'msg': msg,
//         'data': data?.toMap(),
//       };

//   /// `dart:convert`
//   ///
//   /// Parses the string and returns the resulting Json object as [Products].
//   factory Products.fromJson(String data) {
//     return Products.fromMap(json.decode(data) as Map<String, dynamic>);
//   }

//   /// `dart:convert`
//   ///
//   /// Converts [Products] to a JSON string.
//   String toJson() => json.encode(toMap());
// }
