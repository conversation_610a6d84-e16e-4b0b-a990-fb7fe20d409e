// import 'dart:convert';

// import 'image.dart';

// class Variant {
//   String? mrp;
//   String? price;
//   List<Image>? images;
//   String? weight;
//   String? costPrice;
//   int? variantId;
//   String? variantSku;
//   String? variantName;
//   int? variantStock;
//   String? variantStatus;
//   int? storeIsActive;
//   int? variantIsActive;

//   Variant({
//     this.mrp,
//     this.price,
//     this.images,
//     this.weight,
//     this.costPrice,
//     this.variantId,
//     this.variantSku,
//     this.variantName,
//     this.variantStock,
//     this.variantStatus,
//     this.storeIsActive,
//     this.variantIsActive,
//   });

//   factory Variant.fromMap(Map<String, dynamic> data) => Variant(
//         mrp: data['mrp'] as String?,
//         price: data['price'] == "0.00" ? data['mrp'] : data['price'] as String?,
//         images: (data['images'] as List<dynamic>?)
//             ?.map((e) => Image.fromMap(e as Map<String, dynamic>))
//             .toList(),
//         weight: data['weight'] as String?,
//         costPrice: data['cost_price'] as String?,
//         variantId: data['variant_id'] as int?,
//         variantSku: data['variant_sku'] as String?,
//         variantName: data['variant_name'] as String?,
//         variantStock: data['variant_stock'] as int?,
//         variantStatus: data['variant_status'] as String?,
//         storeIsActive: data['store_is_active'] as int?,
//         variantIsActive: data['variant_is_active'] as int?,
//       );

//   Map<String, dynamic> toMap() => {
//         'mrp': mrp,
//         'price': price,
//         'images': images?.map((e) => e.toMap()).toList(),
//         'weight': weight,
//         'cost_price': costPrice,
//         'variant_id': variantId,
//         'variant_sku': variantSku,
//         'variant_name': variantName,
//         'variant_stock': variantStock,
//         'variant_status': variantStatus,
//         'store_is_active': storeIsActive,
//         'variant_is_active': variantIsActive,
//       };

//   /// `dart:convert`
//   ///
//   /// Parses the string and returns the resulting Json object as [Variant].
//   factory Variant.fromJson(String data) {
//     return Variant.fromMap(json.decode(data) as Map<String, dynamic>);
//   }

//   /// `dart:convert`
//   ///
//   /// Converts [Variant] to a JSON string.
//   String toJson() => json.encode(toMap());
// }
