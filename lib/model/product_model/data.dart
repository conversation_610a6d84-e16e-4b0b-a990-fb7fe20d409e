// import 'dart:convert';

// import 'variant.dart';

// class Data {
//   String? productId;
//   String? name;
//   String? description;
//   String? unit;
//   String? sku;
//   String? upc;
//   String? ean;
//   String? mpn;
//   String? isbn;
//   String? type;
//   List<dynamic> images;
//   bool? isActive;
//   // dynamic meta;
//   String? category;
//   String? categoryId;
//   // int? subCategoryId;
//   // String? subCategoryName;
//   String? brandName;
//   // dynamic subCategoryGroupId;
//   // dynamic subCategoryGroupName;
//   List<Variant>? variants;
//   // String? isReviewed;
//   int? isFavorite;
//   // String? avgRate;
//   // dynamic rating;
//   // int? ratingcount;
//   // bool? canreview;
//   // int? revieworderid;
//   bool? returnable;

//   Data({
//     this.productId,
//     this.name,
//     this.description,
//     this.unit,
//     this.sku,
//     this.upc,
//     this.ean,
//     this.mpn,
//     this.isbn,
//     this.type,
//     this.images = const [],
//     this.isActive,
//     // this.meta,
//     this.category,
//     this.categoryId,
//     // this.subCategoryId,
//     // this.subCategoryName,
//     this.brandName,
//     // this.subCategoryGroupId,
//     // this.subCategoryGroupName,
//     this.variants,
//     // this.isReviewed,
//     this.isFavorite,
//     // this.avgRate,
//     // this.rating,
//     // this.canreview,
//     // this.ratingcount,
//     // this.revieworderid,
//     this.returnable,
//   });

//   factory Data.fromMap(Map<String, dynamic> data) => Data(
//         productId: data['_id'] as String?,
//         name: data['name'] as String?,
//         description: data['description'] as String?,
//         unit: data['unit'] as String?,
//         sku: data['SKU'] as String?,
//         upc: data['UPC'] as String?,
//         ean: data['EAN'] as String?,
//         mpn: data['MPN'] as String?,
//         isbn: data['ISBN'] as String?,
//         type: data['type'] as String?,
//         images: (data['images'] as List?) ?? [],
//         isActive: data['isActive'] as bool?,
//         // meta: data['meta'] as dynamic,
//         category: data['category']['name'] as String?,
//         categoryId: data['category']['_id'] as String?,
//         // subCategoryId: data['sub_category_id'] as int?,
//         // subCategoryName: data['sub_category_name'] as String?,
//         brandName: data['brand']['name'] as String?,
//         // subCategoryGroupId: data['sub_category_group_id'] as dynamic,
//         // subCategoryGroupName: data['sub_category_group_name'] as dynamic,
//         variants: (data['variants'] as List<dynamic>?)
//             ?.map((e) => Variant.fromMap(e as Map<String, dynamic>))
//             .toList(),
//         // isReviewed: data['is_reviewed'] as String?,
//         isFavorite: data['is_favorite'] as int?,
//         // avgRate: data['avg_rate'] as String?,
//         // rating: data['rating'] as dynamic,
//         // ratingcount: data['rating_count'] as int?,
//         // canreview: data['can_review'] == 1 ? true : false,
//         // revieworderid: data['review_order_id'] as int?,
//         returnable: data['returnable'] as bool?,
//       );

//   Map<String, dynamic> toMap() => {
//         '_id': productId,
//         'name': name,
//         'description': description,
//         'unit': unit,
//         'SKU': sku,
//         'UPC': upc,
//         'EAN': ean,
//         'MPN': mpn,
//         'ISBN': isbn,
//         'type': type,
//         'images': images,
//         'isActive': isActive,
//         // 'meta': meta,
//         'category': category,
//         'category_id': categoryId,
//         // 'sub_category_id': subCategoryId,
//         // 'sub_category_name': subCategoryName,
//         'brand': brandName,
//         // 'sub_category_group_id': subCategoryGroupId,
//         // 'sub_category_group_name': subCategoryGroupName,
//         'variants': variants?.map((e) => e.toMap()).toList(),
//         // 'is_reviewed': isReviewed,
//         'is_favorite': isFavorite,
//         // 'avg_rate': avgRate,
//         // 'rating': rating,
//         // 'rating_count': ratingcount,
//         // 'can_review': canreview,
//         // 'review_order_id': revieworderid
//         'returnable': returnable,
//       };

//   /// `dart:convert`
//   ///
//   /// Parses the string and returns the resulting Json object as [Data].
//   factory Data.fromJson(String data) {
//     return Data.fromMap(json.decode(data) as Map<String, dynamic>);
//   }

//   /// `dart:convert`
//   ///
//   /// Converts [Data] to a JSON string.
//   String toJson() => json.encode(toMap());
// }
