import 'dart:convert';

import 'autocomplete_prediction.dart';

class PlaceAutocompleteResponse {
  final String? status;
  final List<AutocompletePrediction>? predictions;

  PlaceAutocompleteResponse({this.status, this.predictions});

  factory PlaceAutocompleteResponse.fromJson(Map<String, dynamic> json) {
    return PlaceAutocompleteResponse(
      status: json['status'] as String?,
      predictions:
          List<AutocompletePrediction>.from((json["predictions"] as List?)
                  ?.map<AutocompletePrediction>(
                    (e) => AutocompletePrediction.fromJson(e),
                  )
                  .toList() ??
              []),
      // predictions: json['predictions']
      //     ?.map<AutocompletePrediction>(
      //         (json) => AutocompletePrediction.fromJson(json))
      //     .toList(),
    );
  }

  static PlaceAutocompleteResponse parseAutocompleteResult(
      String responseBody) {
    final parsed = json.decode(responseBody).cast<String, dynamic>();

    return PlaceAutocompleteResponse.fromJson(parsed);
  }
}
