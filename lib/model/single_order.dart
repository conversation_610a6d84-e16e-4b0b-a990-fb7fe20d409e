import 'address_model.dart';
import 'item_list_model/discount.dart';
import 'item_list_model/item_list_model.dart';
import 'tax.dart';

class SingleOrder {
  final SingleOrderAddress? address;
  final String? id;
  final String? customer;
  final String? salesOrderNumber;
  final DateTime? orderDate;
  final List<SingleOrderItem>? items;
  final double? total;
  final String? status;
  final String? paymentMethod;
  final CustomObject? deliveryAgent;
  final int? estDeliveryTime;
  final DateTime? deliveredTime;
  final DateTime? deliveryAgentAssignedTime;
  final List<String> statusList;

  Duration? get deliveredIn =>
      deliveredTime?.difference(deliveryAgentAssignedTime!);

  SingleOrder({
    this.address,
    this.id,
    this.customer,
    this.salesOrderNumber,
    this.orderDate,
    this.items,
    this.total,
    this.status,
    this.paymentMethod,
    this.deliveryAgent,
    this.estDeliveryTime,
    this.deliveredTime,
    this.deliveryAgentAssignedTime,
    this.statusList = const <String>[],
  });

  factory SingleOrder.fromJson(Map<String, dynamic> json) => SingleOrder(
        address: json["address"] == null
            ? null
            : SingleOrderAddress.fromJson(json["address"]),
        id: json["_id"],
        customer: json["customer"],
        salesOrderNumber: json["sales_order_number"],
        orderDate: json["order_date"] == null
            ? null
            : DateTime.parse(json["order_date"]),
        items: json["items"] == null
            ? []
            : List<SingleOrderItem>.from(
                json["items"]!.map((x) => SingleOrderItem.fromJson(x))),
        total: json["total"]?.toDouble(),
        status: json["status"],
        paymentMethod: json["payment_method"],
        deliveryAgent: json['delivery_agent_id'] == null
            ? null
            : CustomObject.driverFromJson(json['delivery_agent_id']),
        estDeliveryTime: int.tryParse(json['est_delivery_time'].toString()),
        deliveredTime:
            DateTime.tryParse(json['delivered_time'].toString())?.toLocal(),
        deliveryAgentAssignedTime:
            DateTime.tryParse(json['delivery_agent_assigned_time'].toString())
                ?.toLocal(),
        statusList: json['statusList'] is List
            ? (json['statusList'] as List)
                .map(
                  (e) => e.toString(),
                )
                .toList()
            : <String>[],
      );

  String get getETAbyStatus {
    return switch (status?.toLowerCase()) {
      'received' => '36',
      'packing' => '30',
      'packed' => '24',
      'delivery_agent_assigned' => '18',
      'out_for_delivery' => '12',
      'out_for_delivery_delayed' => '6',
      _ => '0',
    };
  }

  Map<String, dynamic> toJson() => {
        "address": address?.toJson(),
        "_id": id,
        "customer": customer,
        "sales_order_number": salesOrderNumber,
        "order_date": orderDate?.toIso8601String(),
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "total": total,
        "status": status,
        "payment_method": paymentMethod,
        'delivery_agent_id': deliveryAgent?.toJson(),
        'est_delivery_time': estDeliveryTime,
        'delivered_time': deliveredTime?.toUtc().toIso8601String(),
        'delivery_agent_assigned_time':
            deliveryAgentAssignedTime?.toUtc().toIso8601String(),
        'statusList': statusList,
      };

  SingleOrder copyWith({
    SingleOrderAddress? address,
    String? id,
    String? customer,
    String? salesOrderNumber,
    DateTime? orderDate,
    List<SingleOrderItem>? items,
    double? total,
    String? status,
    String? paymentMethod,
    CustomObject? deliveryAgent,
    int? estDeliveryTime,
    DateTime? deliveredTime,
    DateTime? deliveryAgentAssignedTime,
    List<String>? statusList,
  }) =>
      SingleOrder(
        address: address ?? this.address,
        id: id ?? this.id,
        customer: customer ?? this.customer,
        salesOrderNumber: salesOrderNumber ?? this.salesOrderNumber,
        orderDate: orderDate ?? this.orderDate,
        items: items ?? this.items,
        total: total ?? this.total,
        status: status ?? this.status,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        deliveryAgent: deliveryAgent ?? this.deliveryAgent,
        estDeliveryTime: estDeliveryTime ?? this.estDeliveryTime,
        deliveredTime: deliveredTime ?? this.deliveredTime,
        deliveryAgentAssignedTime:
            deliveryAgentAssignedTime ?? this.deliveryAgentAssignedTime,
        statusList: statusList ?? this.statusList,
      );
}

class SingleOrderAddress {
  final GeoLocation? geoLocation;
  final String? street;
  final String? house;
  final String? city;
  final String? landmark;
  final String? pincode;
  final String? type;

  SingleOrderAddress({
    this.geoLocation,
    this.street,
    this.house,
    this.city,
    this.landmark,
    this.pincode,
    this.type,
  });

  factory SingleOrderAddress.fromJson(Map<String, dynamic> json) =>
      SingleOrderAddress(
        geoLocation: json["geoLocation"] == null
            ? null
            : GeoLocation.fromJson(json["geoLocation"]),
        street: json["street"],
        house: json["house"],
        city: json["city"],
        landmark: json["landmark"],
        pincode: json["pincode"],
        type: json['type'],
      );

  Map<String, dynamic> toJson() => {
        "geoLocation": geoLocation?.toJson(),
        "street": street,
        "house": house,
        "city": city,
        "landmark": landmark,
        "pincode": pincode,
        "type": type,
      };

  SingleOrderAddress copyWith(
    GeoLocation? geoLocation,
    String? street,
    String? house,
    String? city,
    String? landmark,
    String? pincode,
    String? type,
  ) =>
      SingleOrderAddress(
        geoLocation: geoLocation ?? this.geoLocation,
        street: street ?? this.street,
        house: house ?? this.house,
        city: city ?? this.city,
        landmark: landmark ?? this.landmark,
        pincode: pincode ?? this.pincode,
        type: type ?? this.type,
      );
}

class SingleOrderItem {
  final Discount? discount;
  final ItemId? itemId;
  final int? quantity;
  final int? rate;
  final List<Tax> tax;
  final double? total;
  final String? id;

  SingleOrderItem({
    this.discount,
    this.itemId,
    this.quantity,
    this.rate,
    this.tax = const <Tax>[],
    this.total,
    this.id,
  });

  factory SingleOrderItem.fromJson(Map<String, dynamic> json) =>
      SingleOrderItem(
        discount: json["discount"] == null
            ? null
            : Discount.fromJson(json["discount"]),
        itemId:
            json["item_id"] == null ? null : ItemId.fromJson(json["item_id"]),
        quantity: json["quantity"],
        rate: json["rate"],
        tax: json["tax"] == null
            ? []
            : List<Tax>.from(json["tax"]!.map((x) => Tax.fromJson(x))),
        total: json["total"]?.toDouble(),
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "discount": discount?.toJson(),
        "item_id": itemId?.toJson(),
        "quantity": quantity,
        "rate": rate,
        "tax":
            List<dynamic>.from(tax.map((x) => x.toJson())),
        "total": total,
        "_id": id,
      };
  SingleOrderItem copyWith(
    Discount? discount,
    ItemId? itemId,
    int? quantity,
    int? rate,
    List<Tax>? tax,
    double? total,
    String? id,
  ) =>
      SingleOrderItem(
        discount: discount ?? this.discount,
        itemId: itemId ?? this.itemId,
        quantity: quantity ?? this.quantity,
        rate: rate ?? this.rate,
        tax: tax ?? this.tax,
        total: total ?? this.total,
        id: id ?? this.id,
      );
}

class ItemId {
  final String? id;
  final String? name;
  final String? description;
  final String? unit;
  final String? sku;
  final String? upc;
  final String? ean;
  final String? mpn;
  final String? isbn;
  final String? hsn;
  final String? dimension;
  final String? weight;
  final List<Tax>? tax;
  final String? type;
  final List<dynamic>? variants;
  final List<String>? images;
  final String? category;
  final bool? returnable;
  final String? manufacturer;
  final String? brand;
  final List<SingleOrderSalesInfo>? salesInfo;
  final List<SingleOrderInventoryInfo>? inventoryInfo;
  final bool? availableForSales;
  final bool? availableForPurchase;
  final bool? trackInventory;
  final bool? isActive;
  final List<String>? salesChannels;
  final List<dynamic>? attributeOptions;
  final List<dynamic>? variantAttributes;
  final List<dynamic>? items;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ItemId({
    this.id,
    this.name,
    this.description,
    this.unit,
    this.sku,
    this.upc,
    this.ean,
    this.mpn,
    this.isbn,
    this.hsn,
    this.dimension,
    this.weight,
    this.tax,
    this.type,
    this.variants,
    this.images,
    this.category,
    this.returnable,
    this.manufacturer,
    this.brand,
    this.salesInfo,
    this.inventoryInfo,
    this.availableForSales,
    this.availableForPurchase,
    this.trackInventory,
    this.isActive,
    this.salesChannels,
    this.attributeOptions,
    this.variantAttributes,
    this.items,
    this.createdAt,
    this.updatedAt,
  });

  factory ItemId.fromJson(Map<String, dynamic> json) => ItemId(
        id: json["_id"],
        name: json["name"],
        description: json["description"],
        unit: json["unit"],
        sku: json["SKU"],
        upc: json["UPC"],
        ean: json["EAN"],
        mpn: json["MPN"],
        isbn: json["ISBN"],
        hsn: json["HSN"],
        dimension: json["dimension"],
        weight: json["weight"],
        tax: json["tax"] == null
            ? []
            : List<Tax>.from(json["tax"]!.map((x) => Tax.fromJson(x))),
        type: json["type"],
        variants: json["variants"] == null
            ? []
            : List<dynamic>.from(json["variants"]!.map((x) => x)),
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        category: json["category"],
        returnable: json["returnable"],
        manufacturer: json["manufacturer"],
        brand: json["brand"],
        salesInfo: json["salesInfo"] == null
            ? []
            : List<SingleOrderSalesInfo>.from(json["salesInfo"]!
                .map((x) => SingleOrderSalesInfo.fromJson(x))),
        inventoryInfo: json["inventoryInfo"] == null
            ? []
            : List<SingleOrderInventoryInfo>.from(json["inventoryInfo"]!
                .map((x) => SingleOrderInventoryInfo.fromJson(x))),
        availableForSales: json["availableForSales"],
        availableForPurchase: json["availableForPurchase"],
        trackInventory: json["trackInventory"],
        isActive: json["isActive"],
        salesChannels: json["salesChannels"] == null
            ? []
            : List<String>.from(json["salesChannels"]!.map((x) => x)),
        attributeOptions: json["attributeOptions"] == null
            ? []
            : List<dynamic>.from(json["attributeOptions"]!.map((x) => x)),
        variantAttributes: json["variantAttributes"] == null
            ? []
            : List<dynamic>.from(json["variantAttributes"]!.map((x) => x)),
        items: json["items"] == null
            ? []
            : List<dynamic>.from(json["items"]!.map((x) => x)),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "description": description,
        "unit": unit,
        "SKU": sku,
        "UPC": upc,
        "EAN": ean,
        "MPN": mpn,
        "ISBN": isbn,
        "HSN": hsn,
        "dimension": dimension,
        "weight": weight,
        "tax":
            tax == null ? [] : List<dynamic>.from(tax!.map((x) => x.toJson())),
        "type": type,
        "variants":
            variants == null ? [] : List<dynamic>.from(variants!.map((x) => x)),
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "category": category,
        "returnable": returnable,
        "manufacturer": manufacturer,
        "brand": brand,
        "salesInfo": salesInfo == null
            ? []
            : List<dynamic>.from(salesInfo!.map((x) => x.toJson())),
        "inventoryInfo": inventoryInfo == null
            ? []
            : List<dynamic>.from(inventoryInfo!.map((x) => x.toJson())),
        "availableForSales": availableForSales,
        "availableForPurchase": availableForPurchase,
        "trackInventory": trackInventory,
        "isActive": isActive,
        "salesChannels": salesChannels == null
            ? []
            : List<dynamic>.from(salesChannels!.map((x) => x)),
        "attributeOptions": attributeOptions == null
            ? []
            : List<dynamic>.from(attributeOptions!.map((x) => x)),
        "variantAttributes": variantAttributes == null
            ? []
            : List<dynamic>.from(variantAttributes!.map((x) => x)),
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x)),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}

class SingleOrderInventoryInfo {
  final String? storeId;
  final String? inventoryAccount;
  final int? openingStock;
  final int? ratePerUnit;
  final int? reorderPoint;
  final int? availableStock;
  final String? id;

  SingleOrderInventoryInfo({
    this.storeId,
    this.inventoryAccount,
    this.openingStock,
    this.ratePerUnit,
    this.reorderPoint,
    this.availableStock,
    this.id,
  });

  factory SingleOrderInventoryInfo.fromJson(Map<String, dynamic> json) =>
      SingleOrderInventoryInfo(
        storeId: json["storeId"],
        inventoryAccount: json["inventoryAccount"],
        openingStock: json["openingStock"],
        ratePerUnit: json["ratePerUnit"],
        reorderPoint: json["reorderPoint"],
        availableStock: json["availableStock"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "storeId": storeId,
        "inventoryAccount": inventoryAccount,
        "openingStock": openingStock,
        "ratePerUnit": ratePerUnit,
        "reorderPoint": reorderPoint,
        "availableStock": availableStock,
        "_id": id,
      };
}

class Vendor {
  final String? id;
  final int? costPrice;

  Vendor({
    this.id,
    this.costPrice,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        id: json["_id"],
        costPrice: json["costPrice"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "costPrice": costPrice,
      };
}

class SingleOrderSalesInfo {
  final Discount? discount;
  final String? storeId;
  final int? sellingPrice;
  final int? mrp;
  final String? salesAccount;
  final String? description;
  final String? id;

  SingleOrderSalesInfo({
    this.discount,
    this.storeId,
    this.sellingPrice,
    this.mrp,
    this.salesAccount,
    this.description,
    this.id,
  });

  factory SingleOrderSalesInfo.fromJson(Map<String, dynamic> json) =>
      SingleOrderSalesInfo(
        discount: json["discount"] == null
            ? null
            : Discount.fromJson(json["discount"]),
        storeId: json["storeId"],
        sellingPrice: json["sellingPrice"],
        mrp: json["MRP"],
        salesAccount: json["salesAccount"],
        description: json["description"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "discount": discount?.toJson(),
        "storeId": storeId,
        "sellingPrice": sellingPrice,
        "MRP": mrp,
        "salesAccount": salesAccount,
        "description": description,
        "_id": id,
      };
}
