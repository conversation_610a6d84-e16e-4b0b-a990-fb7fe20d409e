import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'services/firebase_services.dart';
import 'utils/bindings.dart';
import 'utils/theme.dart';
import 'view/screens/splash/splash_screen.dart';
import 'view/widgets/keyboard_hider.dart';

class Rapsap extends StatelessWidget {
  const Rapsap({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: GetMaterialApp(
        // onInit: () => NotificationService().setupInteractedMessage(),
        navigatorObservers: FirebaseService.analyticsObserver,
        defaultTransition: Transition.rightToLeft,
        initialBinding: HomeBinding(),
        theme: RapsapTheme.theme,
        debugShowCheckedModeBanner: false,
        home: const SplashScreen(),
      ),
    );
  }
}
