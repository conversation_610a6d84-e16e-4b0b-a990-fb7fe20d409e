// ignore_for_file: unused_local_variable

import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

import '../controllers/cart_controlller.dart';
import '../model/database_models/database_models.dart';

class DatabaseHelper {
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;
  Future<Database> get database async => _database ??= await _initDatabase();

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'rapsap.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE products(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date INTEGER,
          name TEXT,
          qty INTEGER,
          productID TEXT,
          purchaseOrderID TEXT,
          imageURL TEXT,
          weight FLOAT,
          price FLOAT,
          isFree INTEGER,
          mrp FLOAT,
          availableStock INTEGER,
          discount FLOAT,
          tax FLOAT
      )
      ''');

    await db.execute('''
      CREATE TABLE orders(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          addressID INTEGER,
          storeID INTEGER,
          subTotal FLOAT,
          grandTotal FLOAT,
          discount FLOAT,
          deliveryCharge FLOAT,
          tax FLOAT,
          orderID INTEGER,
          offerID INTEGER,
          minOrder FLOAT,
          couponCode TEXT
      )
      ''');

    await db.execute('''
      CREATE TABLE payment(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          razorOrderID TEXT,
          razorInvoiceID INTEGER,
          status TEXT,
          razorKey TEXT
      )
      ''');

    await db.execute('''
      CREATE TABLE userAddress(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT,
          addressType TEXT,
          isDefault INTEGER,
          phone TEXT,
          address1 TEXT,
          address2 TEXT,
          city TEXT,
          state TEXT,
          pincode TEXT,
          latitude TEXT,
          longitude TEXT,
          addressID INTEGER
      )
      ''');
  }

  Future<List<LocalShoppingCart>> getGroceries() async {
    Database db = await instance.database;
    final products = await db.query('products', orderBy: 'id');
    List<LocalShoppingCart> groceryList = products.isNotEmpty
        ? products.map((c) => LocalShoppingCart.fromMap(c)).toList()
        : [];
    final CartController cartController = Get.find();
    cartController.myCartItems.value = groceryList;
    cartController.update();
    return groceryList;
  }

  // Future<int> add(LocalShoppingCart cart) async {
  //   Database db = await instance.database;
  //   return await db.insert('products', cart.toMap());
  // }

  Future<int> insertAddress(LocalUserAddress address) async {
    Database db = await instance.database;
    await db.rawQuery('DELETE FROM userAddress');
    return await db.insert('userAddress', address.toMap());
  }

  Future<List<LocalUserAddress>> getUserDbAddress() async {
    Database db = await instance.database;
    final address = await db.query('userAddress', orderBy: 'name');
    List<LocalUserAddress> addressList = address.isNotEmpty
        ? address.map((c) => LocalUserAddress.fromMap(c)).toList()
        : [];
    return addressList;
  }

  Future<int> addUpdate(LocalShoppingCart cart) async {
    // check existing
    Database db = await instance.database;
    List<Map> check = await db.rawQuery(
        'select qty from products where productID=? and purchaseOrderID=?',
        [cart.productID, cart.purchaseOrderID]);

    if (check.isNotEmpty) {
      // updated here
      List<Map> list = await db.rawQuery(
          'update products set qty =? where productID=? and purchaseOrderID=?',
          [check[0]['qty'] + 1, cart.productID, cart.purchaseOrderID]);
      await DatabaseHelper.instance.getGroceries();
      return 1;
    } else {
      // to create new
      // Database db = await instance.database;
      final value = await db.insert('products', cart.toMap());
      DatabaseHelper.instance.getGroceries();

      return value;
    }
  }

  Future<int> removeUpdate(LocalShoppingCart cart) async {
    // check existing
    Database db = await instance.database;
    List<Map> check = await db.rawQuery(
        'select qty from products where productID=? and purchaseOrderID=?',
        [cart.productID, cart.purchaseOrderID]);
    if (check[0]['qty'] > 1) {
      // updated here
      List<Map> list = await db.rawQuery(
          'update products set qty =? where productID=? and purchaseOrderID=?',
          [check[0]['qty'] - 1, cart.productID, cart.purchaseOrderID]);
      DatabaseHelper.instance.getGroceries();

      return 1;
    } else {
      await db.delete('products',
          where: 'productID=? and purchaseOrderID=?',
          whereArgs: [cart.productID, cart.purchaseOrderID]);
      DatabaseHelper.instance.getGroceries();

      // to create new
      return 0;
    }
  }

  Future<int> remove(LocalShoppingCart cart) async {
    Database db = await instance.database;
    // return await db.delete('products', where: 'id = ?', whereArgs: [id]);
    final result = await db.delete('products',
        where: 'productID = ? and purchaseOrderID=?',
        whereArgs: [cart.productID, cart.purchaseOrderID]);
    DatabaseHelper.instance.getGroceries();
    return result;
  }

  Future<int> removeFreeItem() async {
    Database db = await instance.database;
    // return await db.delete('products', where: 'id = ?', whereArgs: [id]);
    return await db.delete('products', where: 'isFree = ?', whereArgs: [1]);
  }

  // Future<int> update(LocalShoppingCart cart) async {
  //   Database db = await instance.database;
  //   return await db.update('products', cart.toMap(),
  //       where: "productID = ?", whereArgs: [cart.productID]);
  // }

  Future increseQty(LocalShoppingCart cart) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update products set qty =? where productID=? and purchaseOrderID=?',
        [(cart.qty ?? 1) + 1, cart.productID, cart.purchaseOrderID]);
    DatabaseHelper.instance.getGroceries();

    return list.isNotEmpty ? list[0]['qty'] : 0;
  }

  Future decreseQty(LocalShoppingCart cart) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update products set qty =? where productID=? and purchaseOrderID=?',
        [(cart.qty ?? 1) - 1, cart.productID, cart.purchaseOrderID]);
    DatabaseHelper.instance.getGroceries();

    return list.isNotEmpty ? list[0]['qty'] : 0;
  }

  Future<int> getQty(LocalShoppingCart cart) async {
    Database db = await instance.database;
    List<Map>? list;
    try {
      list = await db.rawQuery(
          'select qty from products where productID=? and purchaseOrderID=?',
          [cart.productID, cart.purchaseOrderID]);
    } catch (e) {
      log(e.toString());
    }

    return list?.firstOrNull?['qty'] ?? 0;
  }

  Future<double> getSubTotal() async {
    Database db = await instance.database;
    List<Map> list =
        await db.rawQuery('select sum(price * qty) as subtotal from products');
    // print('subtotal ${list[0]}');
    if (list[0]['subtotal'] == null) {
      await db.rawQuery('update orders set subTotal =?', [0.0]);
      return 0.0;
    } else {
      final sub = list.isNotEmpty ? list[0]['subtotal'] : 0.0;
      log(sub.toString());
      final pr = await db.rawQuery("select * from orders");

      if (pr.isNotEmpty) {
        // print('object');
        // print('sub=$sub');
        await db.rawQuery(
            "update orders set subTotal =? where id=?", [sub, pr.first['id']]);
        final olist = await db.query('orders');
        // print("orderlist=$olist");
      } else {
        await db.rawQuery("insert into orders (subTotal) values(?) ", [sub]);
      }
      // final data = await db.rawQuery('update orders set subTotal =?', [sub]);
      // final pr = await db.rawQuery("select * from orders");
      // log("orders=${pr.toString()}");
      // await db.rawQuery('update orders set subTotal =?', [sub]);
      return sub;
    }
  }

  Future<int> orderAddUpdate(LocalOrder orders) async {
    // check existing
    Database db = await instance.database;

    return await db.insert('orders', orders.toMap());
  }

  Future<List<LocalOrder>> getDbOrder() async {
    await Future.delayed(const Duration(milliseconds: 200));
    Database db = await instance.database;
    final orders = await db.query('orders', orderBy: 'id');
    List<LocalOrder> ordersList = orders.isNotEmpty
        ? orders.map((c) => LocalOrder.fromMap1(c)).toList()
        : [];
    return ordersList;
  }

  Future<List<LocalOrder>> getDbOrder1() async {
    await Future.delayed(const Duration(milliseconds: 200));
    Database db = await instance.database;
    final orders = await db.query('orders', orderBy: 'id');
    List<LocalOrder> ordersList = orders.isNotEmpty
        ? orders.map((c) => LocalOrder.fromMap1(c)).toList()
        : [];
    return ordersList;
  }

  Future<List<LocalOrder>> getDbOrder2() async {
    Database db = await instance.database;
    final orders = await db.query('orders', orderBy: 'id');
    List<LocalOrder> ordersList = orders.isNotEmpty
        ? orders.map((c) => LocalOrder.fromMap1(c)).toList()
        : [];
    return ordersList;
  }

  Future setOrderID(int id) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('update orders set orderID =?', [id]);

    return list.isNotEmpty ? list[0]['orderID'] : 0;
  }

  Future setAddressID(int id) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('update orders set addressID =?', [id]);

    return list.isNotEmpty ? list[0]['addressID'] : 0;
  }

  Future setDiscountValue(
      double discount, int offerID, double minOrder, String couponCode) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update orders set discount =?, offerID=?, minOrder=?,couponCode=?',
        [discount, offerID, minOrder, couponCode]);

    getGroceries();

    return list.isNotEmpty ? list[0]['discount'] : 0;
  }

  Future setStoreID(int storeid) async {
    Database db = await instance.database;
    List<Map> list =
        await db.rawQuery('update orders set storeID =?', [storeid]);

    return list.isNotEmpty ? list[0]['storeID'] : 0;
  }

  Future clearShopingCart() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM products');

    return 1;
  }

  Future clearAllOrders() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM orders');

    return 1;
  }

  Future setPaymentDetails(
      String orderid, String invID, String status, String key) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'insert into payment (razorOrderID,razorInvoiceID,status, razorKey) values (?,?,?,?)',
        [orderid, invID, status, key]);
    return true;
  }

  Future getPaymentDetails() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('select * from payment');
    return list;
  }

  Future clearPayment() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM payment');
    return 1;
  }

  Future clearAddress() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM address');
    return 1;
  }
}
