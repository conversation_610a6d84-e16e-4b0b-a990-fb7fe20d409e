import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../controllers/user_controller.dart';
import '../model/coupon_list_model.dart';
import '../model/items_not_in_stock.dart';
import '../model/model.dart';
import '../utils/constants.dart';
import '../utils/exception_handler.dart';

class CartService {
  final Dio _dio;
  CartService(this._dio);

  Future<ResponseModel<List<ItemNotInStock>>?> loadCart({
    required String? storeId,
    required List<LocalShoppingCart> items,
  }) async {
    if (items.isEmpty) {
      return null;
    }
    String url = '$baseURL/api/on-demand-delivery/customer/load/cart';
    Map<String, dynamic> body = {
      "store": storeId,
      "items": items
          .map(
            (e) => {
              "item_id": e.productID,
              "quantity": e.qty,
              "purchase_order_id": e.purchaseOrderID,
            },
          )
          .toList(),
    };
    String jwt = Get.find<UserController>().userdata.value?.token ?? '';
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt'
    };
    try {
      final res = await _dio.post(url,
          data: body,
          options: Options(
            headers: headers,
            validateStatus: (status) => status == 200 || status == 409,
          ));
      if (res.statusCode == 200) {
        return null;
      } else if (res.statusCode == 409) {
        return ResponseModel<List<ItemNotInStock>>(
          success: res.data['success'],
          message: res.data['msg'],
          data: res.data['data'] == null
              ? []
              : res.data['itemsNotInStock'] == null
                  ? []
                  : List<ItemNotInStock>.from(
                      (res.data['itemsNotInStock'] as List).map(
                        (e) => ItemNotInStock.fromJson(e),
                      ),
                    ),
        );
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<CouponListModel>> getOffers({
    int page = 1,
    int limit = 10,
  }) async {
    String url = '$baseURL/api/on-demand-delivery/customer/get/offers';
    String jwt = Get.find<UserController>().userdata.value?.token ?? '';
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt'
    };
    Map<String, dynamic> queryParameters = {"page": page, "limit": limit};
    try {
      final res = await _dio.get(url,
          queryParameters: queryParameters,
          options: Options(
            headers: headers,
            validateStatus: (status) => status == 200,
          ));
      return ResponseModel<CouponListModel>(
        message: res.data['msg']?.toString(),
        success: res.statusCode == 200,
        data: CouponListModel.fromJson(res.data['data']),
      );
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }
}
