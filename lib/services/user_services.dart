// ignore_for_file: empty_catches, prefer_typing_uninitialized_variables

import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import '../controllers/user_controller.dart';
import '../model/model.dart';
import '../utils/exception_handler.dart';
import '../utils/utils.dart';
import '../model/user_model/user_model.dart';
import '../utils/constants.dart';

class UserService {
  final Dio _dio;
  UserService(this._dio);
  static final _client = http.Client();

  //Email Login
  // static Future<UserData?> emailLogin(params) async {
  //   String url = '${baseURL}api/v1/mobile/loginUser';
  //   Map<String, String> body = {
  //     'username': params['email'],
  //     'password': params['password']
  //   };
  //   UserData? userModel;
  //   try {
  //     var response = await _client
  //         .post(Uri.parse(url), body: body)
  //         .then((value) => Utils.logResponse(value, body));
  //     if (response.statusCode == 200) {
  //       var jsonRes = response.body;
  //       var jsonMap = json.decode(jsonRes);
  //       // storage.write('mobile', jsonMap['data']['mobile'].toString());
  //       // storage.write('jwt', jsonMap['data']['token']);
  //       userModel = UserData.fromJson(jsonMap);
  //     }
  //     if (userModel == null) {
  //       Get.back();
  //     }
  //     return userModel;
  //   } catch (e) {}
  //   return userModel;
  // }

  //GoogleSigin

  static Future onGoogleSignin(params) async {
    String url = '${baseURL}api/v1/mobile/loginWithGoogleIdToken';

    // print('body1$requestBody');

    UserData? userModel;
    try {
      final response = await _client
          .post(Uri.parse(url), body: params)
          .then((value) => Utils.logResponse(value, params));
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        // final storage = new FlutterSecureStorage();
        // await storage.write(key: "jwt", value: jsonMap['data']['token']);

        // String? jwt = await storage.read(key: "jwt");
        // print(jwt);
        // print(jsonMap['data']['token']);
        // userModel = User.fromJson(jsonMap);
        userModel = UserData.fromJson(jsonMap);
      }
      return userModel;
    } catch (e) {}
    return userModel;
  }

  Future<ResponseModel<UserData>> verifyOtp({
    required String phone,
    required String otp,
    required String fcmToken,
  }) async {
    String url = '$baseURL/api/on-demand-delivery/customer/public/verify/otp';
    Map<String, dynamic> body = {
      "phone": phone,
      "otp": otp,
      "fcmToken": fcmToken,
      "orgDBName": orgDBName,
    };

    try {
      final response = await _dio.post(url, data: body);

      if (response.statusCode == 200) {
        return ResponseModel<UserData>(
          success: true,
          message: response.data['msg'],
          data: UserData.fromJson(response.data['data']),
        );
      }
      throw response.data['msg'] ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<String> updateUserDetails(
      {required String name, required String email}) async {
    String url =
        '$baseURL/api/on-demand-delivery/customer/update/profile/details';
    Map<String, String> body = {
      "name": name,
      "email": email,
    };
    String jwt = Get.find<UserController>().userdata.value?.token ?? '';
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt'
    };
    try {
      final res =
          await _dio.put(url, data: body, options: Options(headers: headers));
      if (res.statusCode == 200) {
        return res.data['msg'].toString();
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<void>> sendOTP(String phone) async {
    String url = '$baseURL/api/on-demand-delivery/customer/public/send/otp';
    Map<String, String> body = {
      "phone": phone,
      "orgDBName": orgDBName,
    };

    try {
      final response = await _dio.post(url, data: body);

      if (response.statusCode == 200) {
        return ResponseModel<void>(
          success: true,
          message: response.data['msg'],
          data: null,
        );
      }
      throw response.data['msg'] ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<NearbyStore?> getNearbyStore(List<double>? coordinates) async {
    String url =
        '$baseURL/api/on-demand-delivery/customer/get/nearby/store?coordinates[0]=${coordinates?[0]}&coordinates[1]=${coordinates?[1]}';
    String jwt = Get.find<UserController>().userdata.value?.token ?? '';
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt'
    };
    try {
      if (coordinates == null) {
        throw 'location not found';
      }
      final res = await _dio.get(url,
          options: Options(headers: headers, validateStatus: (_) => true));
      if (res.statusCode == 200) {
        final store = res.data['data']['nearbyStore'];
        return store == null ? null : NearbyStore.fromJson(store);
      }
      throw res.data['msg'].toString();
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }
}
