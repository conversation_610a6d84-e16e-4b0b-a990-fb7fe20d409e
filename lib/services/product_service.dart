// ignore_for_file: empty_catches

import 'dart:io';

import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';

import '../model/model.dart';
// import '../model/product_review_model/product_review_model.dart';
// import '../controllers/rating_screen_controller.dart';
import '../controllers/user_controller.dart';
import '../utils/constants.dart';
import '../utils/exception_handler.dart';

class ProductService {
  final Dio _dio;
  ProductService(this._dio);

  Future<Item> getProductbyID({required String productId}) async {
    String url =
        "$baseURL/api/on-demand-delivery/customer/get/item?_id=$productId";
    final headers = {
      HttpHeaders.authorizationHeader:
          'Bearer ${Get.find<UserController>().userdata.value?.token}'
    };

    try {
      final res = await _dio.get(url, options: Options(headers: headers));

      if (res.statusCode == 200) {
        return Item.fromJson(res.data);
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<ItemListModel?>> getItems({
    int page = 1,
    int limit = 10,
    String? searchQuery,
    String? categoryId,
  }) async {
    Map<String, dynamic> body = {
      "page": page,
      "limit": limit,
    };
    body["store"] = Get.find<UserController>().nearbyStore?.id;
    if (searchQuery != null) {
      body['search'] = searchQuery;
    }
    if (categoryId != null) {
      body['categories[0]'] = [categoryId];
    }
    final headers = {
      HttpHeaders.authorizationHeader:
          "bearer ${Get.find<UserController>().userdata.value?.token}",
    };

    try {
      final res = await _dio.get(
        "/api/on-demand-delivery/customer/get/items",
        queryParameters: body,
        options: Options(headers: headers),
      );

      if (res.statusCode == 200) {
        final response = ResponseModel<ItemListModel?>(
          success: res.data['success'],
          message: res.data['msg'],
          data: res.data['data'] == null
              ? null
              : ItemListModel.fromJson(res.data['data']),
        );
        return response;
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ItemListModel> getWishListItems({
    int page = 1,
    int limit = 10,
    required String storeId,
  }) async {
    Map<String, dynamic> queryParameters = {
      'store': storeId,
      'page': page,
      'limit': limit,
    };

    final headers = {
      HttpHeaders.authorizationHeader:
          "Bearer ${Get.find<UserController>().userdata.value?.token}",
    };

    try {
      final res = await _dio.get(
        "/api/on-demand-delivery/customer/get/wishlist",
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      if (res.statusCode == 200) {
        final response = ItemListModel.fromJson(res.data["data"], true);
        return response;
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<void>> addToWishList({required String itemId}) async {
    Map<String, dynamic> data = {'_id': itemId};

    final headers = {
      HttpHeaders.authorizationHeader:
          "Bearer ${Get.find<UserController>().userdata.value?.token}",
    };

    try {
      final res = await _dio.post(
        "/api/on-demand-delivery/customer/add/item/to/wishlist",
        data: data,
        options: Options(headers: headers),
      );

      if (res.statusCode == 200) {
        return ResponseModel<void>(
          data: null,
          message: res.data['msg'].toString(),
          success: true,
        );
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<void>> removeFromWishList(
      {required String itemId}) async {
    Map<String, dynamic> data = {'_id': itemId};

    final headers = {
      HttpHeaders.authorizationHeader:
          "Bearer ${Get.find<UserController>().userdata.value?.token}",
    };

    try {
      final res = await _dio.delete(
        "/api/on-demand-delivery/customer/remove/item/from/wishlist",
        data: data,
        options: Options(headers: headers),
      );

      if (res.statusCode == 200) {
        return ResponseModel<void>(
          data: null,
          message: res.data['msg'].toString(),
          success: true,
        );
      }
      throw res.data['msg']?.toString() ?? 'Something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }
}
