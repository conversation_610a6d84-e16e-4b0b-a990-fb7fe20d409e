import 'dart:convert';
import 'dart:developer';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import '../controllers/order_controller.dart';
import '../firebase_options.dart';
import '../view/screens/tracking_order/tracking_order.dart';
import 'notification_service.dart';

@pragma("vm:entry-point")
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log("BACKGROUND MESSAGE received:\n[_onBackgroundMessage]\nREMOTE message:\n${const JsonEncoder.withIndent(" ").convert(message.toMap())}");
  _handleMessageForTrackingOrder(message);
}

void _onMessageOpenedApp(RemoteMessage event) {
  log("MESSAGE tapped from background state(not terminated):\n[_onMessageOpenedApp]\nREMOTE message:\n${const JsonEncoder.withIndent(" ").convert(event.toMap())}");
  if (Get.currentRoute != '/TrackOrderScreen') {
    final json = jsonDecode(event.data['data']);
    Get.to(() => TrackOrderScreen(
          orderId: json['order_id'],
        ));
  }
  _handleMessageForTrackingOrder(event);
}

class FirebaseService {
  static final FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.instance;

  static List<NavigatorObserver> analyticsObserver = [
    FirebaseAnalyticsObserver(analytics: FirebaseService.firebaseAnalytics),
  ];

  static Future<void> init() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    NotificationServiceImpl().initLocalNotification();
    FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
    await firebaseMessaging.requestPermission();

    FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    FirebaseMessaging.onMessage.listen((message) {
      log("FOREGROUND MESSAGE received:\n[onMessage]\nREMOTE message:\n${const JsonEncoder.withIndent(" ").convert(message.toMap())}");
      _handleMessageForTrackingOrder(message);

      NotificationServiceImpl().showNotification(message);
    });
    FirebaseMessaging.onMessageOpenedApp.listen(_onMessageOpenedApp);
    crashlytics();
  }

  static void crashlytics() {
    if (!kDebugMode) {
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };
      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
  }
}

void _handleMessageForTrackingOrder(RemoteMessage message) {
  if (message.data['data'] != null) {
    final result = jsonDecode(message.data['data']);
    final orderId = result['order_id'].toString();
    final status = result['status'].toString().toLowerCase();
    OrderController.instance().updateCurrentOrderStatus(orderId, status).then(
      (value) {
        OrderController.instance().update();
      },
    );
  }
}
