import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../model/address_model.dart';
import '../controllers/user_controller.dart';
import '../utils/constants.dart';
import '../utils/exception_handler.dart';

class AccountService {
  final Dio _dio;
  AccountService(this._dio);
  static final UserController userController = Get.find<UserController>();

  Future<bool> createAddress({
    String? id,
    String? name,
    String? phone,
    String? type,
    String? line1,
    String? line2,
    String? landmark,
    String? state,
    String? city,
    String? pincode,
    double? longitude,
    double? latitude,
  }) async {
    final String url =
        '$baseURL/api/on-demand-delivery/customer/create/or/update/address';

    final String? jwt = userController.userdata.value?.token;

    final Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    Map<String, dynamic> body = {
      "name": name,
      "phone": phone,
      "type": type,
      "line1": line1,
      "line2": line2,
      "landmark": landmark,
      "state": state,
      "city": city,
      "pincode": pincode,
      "coordinates": [longitude, latitude],
    };
    if (id != null) {
      body['_id'] = id;
    }

    try {
      final response =
          await _dio.post(url, data: body, options: Options(headers: headers));

      if (response.data['success'] == true) {
        return true;
      }
      throw response.data['msg']?.toString() ?? "something went wrong";
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> deleteAddress(String id) async {
    final String url =
        '$baseURL/api/on-demand-delivery/customer/delete/address';

    final String? jwt = Get.find<UserController>().userdata.value?.token;

    final Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    final Map<String, dynamic> body = {
      "_id": id,
    };

    try {
      final response = await _dio.delete(url,
          data: body, options: Options(headers: headers));

      if (response.statusCode == 200) {
        return;
      }
      throw response.data['msg']?.toString() ?? "Something went wrong";
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<AddressModel?> getAddress() async {
    final String url =
        '$baseURL/api/on-demand-delivery/customer/get/addresses?page=1&limit=10';

    final String? jwt = Get.find<UserController>().userdata.value?.token;

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    try {
      final response = await _dio.get(url, options: Options(headers: headers));

      if (response.data['success'] == true) {
        return AddressModel.fromJson(response.data);
      }
      return null;
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }
}
