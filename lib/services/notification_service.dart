import 'dart:math' as math;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationServiceImpl {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  void initLocalNotification() async {
    AndroidInitializationSettings androidInitializationSettings =
        const AndroidInitializationSettings("@mipmap/ic_launcher");
    DarwinInitializationSettings darwinInitializationSettings =
        const DarwinInitializationSettings();
    InitializationSettings initializationSettings = InitializationSettings(
        android: androidInitializationSettings,
        iOS: darwinInitializationSettings);
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
    );
  }

  Future<void> showNotification(RemoteMessage message) async {
    AndroidNotificationChannel channel = AndroidNotificationChannel(
        math.Random.secure().nextInt(1000000).toString(),
        "High Importance notfication",
        importance: Importance.max);

    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      channel.id,
      channel.name,
      channelDescription: "Description",
      importance: Importance.high,
      priority: Priority.high,
      ticker: "ticker",
    );

    DarwinNotificationDetails darwinNotificationDetails =
        const DarwinNotificationDetails(
            presentAlert: true, presentBadge: true, presentSound: true);
    NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, iOS: darwinNotificationDetails);

    Future.delayed(Duration.zero, () {
      _flutterLocalNotificationsPlugin.show(
          0,
          message.notification?.title.toString(),
          message.notification?.body.toString(),
          notificationDetails);
    });
  }
}


// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();
// @pragma('vm:entry-point')
// Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   NotificationService().triggerNotification(message);
//   await Firebase.initializeApp();
// }
// @pragma('vm:entry-point')
// Future<void> notificationData(NotificationResponse value) async {
//   if (value.payload != null) {
//     await handlenavigation(payload: value.payload!);
//   }
// }
// class NotificationService {
//   final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();
//   Future<void> setupInteractedMessage() async {
//     if (Platform.isIOS) {
//       RemoteMessage? initialMessage =
//           await FirebaseMessaging.instance.getInitialMessage();
//       if (initialMessage != null) {
//         _handleMessage(initialMessage);
//       }
//       FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
//     }
//   }
//   void _handleMessage(RemoteMessage message) {
//     String payload = getPayload(message.data);
//     GetStorage('payload').write('payload', payload.toString());
//   }
//   Future<void> init() async {
//     AndroidInitializationSettings androidInitializationSettings =
//         const AndroidInitializationSettings("@mipmap/ic_launcher");
//     DarwinInitializationSettings darwinInitializationSettings =
//         const DarwinInitializationSettings();
//     InitializationSettings initializationSettings = InitializationSettings(
//         android: androidInitializationSettings,
//         iOS: darwinInitializationSettings);
//     await _flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//     );
//     if (Platform.isAndroid) {
//       FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
//       FirebaseMessaging.onMessageOpenedApp.listen(getMessages);
//       FirebaseMessaging.onMessage.listen(getMessages);
//     } else {
//       FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
//       FirebaseMessaging.onMessageOpenedApp.listen(onMessageOpenedAppFn);
//     }
//     // await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
//     // await flutterLocalNotificationsPlugin
//     //     .resolvePlatformSpecificImplementation<
//     //         AndroidFlutterLocalNotificationsPlugin>()
//     //     ?.createNotificationChannel(channel);
//     // await flutterLocalNotificationsPlugin
//     //     .resolvePlatformSpecificImplementation<
//     //         IOSFlutterLocalNotificationsPlugin>()
//     //     ?.requestPermissions(alert: true, badge: true, sound: true);
//     // // ignore: unused_local_variable
//     // final List<ActiveNotification>? activeNotifications =
//     //     await flutterLocalNotificationsPlugin
//     //         .resolvePlatformSpecificImplementation<
//     //             AndroidFlutterLocalNotificationsPlugin>()
//     //         ?.getActiveNotifications();
//     // // log('activeNotifications $activeNotifications');
//     // await flutterLocalNotificationsPlugin
//     //     .resolvePlatformSpecificImplementation<
//     //         AndroidFlutterLocalNotificationsPlugin>()
//     //     ?.createNotificationChannel(channel);
//     // await FirebaseMessaging.instance
//     //     .setForegroundNotificationPresentationOptions(
//     //         alert: true, badge: false, sound: true);
//     // const AndroidInitializationSettings initializationSettingsAndroid =
//     //     AndroidInitializationSettings('@mipmap/ic_launcher');
//     // // IOSInitializationSettings initializationSettingsIOS =
//     // //     const IOSInitializationSettings();
//     // final DarwinInitializationSettings initializationSettingsIOS =
//     //     DarwinInitializationSettings(
//     //   requestSoundPermission: false,
//     //   requestBadgePermission: false,
//     //   requestAlertPermission: false,
//     //   defaultPresentAlert: true,
//     //   defaultPresentSound: true,
//     //   notificationCategories: [
//     //     DarwinNotificationCategory(
//     //       'category',
//     //       options: {
//     //         DarwinNotificationCategoryOption.allowAnnouncement,
//     //       },
//     //       actions: [
//     //         DarwinNotificationAction.plain(
//     //           'snoozeAction',
//     //           'snooze',
//     //         ),
//     //         DarwinNotificationAction.plain(
//     //           'confirmAction',
//     //           'confirm',
//     //           options: {
//     //             DarwinNotificationActionOption.authenticationRequired,
//     //           },
//     //         ),
//     //       ],
//     //     ),
//     //   ],
//     // );
//     // final InitializationSettings initializationSettings =
//     //     InitializationSettings(
//     //         android: initializationSettingsAndroid,
//     //         iOS: initializationSettingsIOS);
//     // await flutterLocalNotificationsPlugin.initialize(
//     //   initializationSettings,
//     //   onDidReceiveBackgroundNotificationResponse: notificationData,
//     //   onDidReceiveNotificationResponse: (NotificationResponse value) async {
//     //     if (value.payload != null) {
//     //       handlenavigation(payload: value.payload!);
//     //     }
//     //   },
//     // );
//     // final notificationOnLaunchDetails =
//     //     await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
//     // if (notificationOnLaunchDetails?.didNotificationLaunchApp ?? false) {
//     //   if (notificationOnLaunchDetails!.notificationResponse!.payload != null) {
//     //     GetStorage('payload').write(
//     //         'payload',
//     //         notificationOnLaunchDetails.notificationResponse!.payload
//     //             .toString());
//     //   }
//     // }
//   }
//   AndroidNotificationChannel channel = const AndroidNotificationChannel(
//     'high_importance_channel', // id
//     'High Importance Notifications', // title
//     // description
//     importance: Importance.high,
//     playSound: true,
//   );
//   void onMessageOpenedAppFn(RemoteMessage message) {
//     log("onMessageOpenedAppFn");
//     String payload = getPayload(message.data);
//     handlenavigation(payload: payload).then((value) {
//       GetStorage('payload').write('payload', "");
//     });
//   }
//   void getMessages(RemoteMessage message) {
//     // if (kDebugMode) {
//     log('Got a message whilst in the foreground!');
//     log("Message data: ${message.data}");
//     // }
//     triggerNotification(message);
//   }
//   void triggerNotification(RemoteMessage message) {
//     // RemoteNotification? notification = message.notification;
//     // AndroidNotification? android = message.notification?.android;
//     // if (android != null && notification != null) {
//     //   log('android:: ${notification.android}');
//     //   log('apple:: ${notification.apple}');
//     //   flutterLocalNotificationsPlugin.show(
//     //     message.messageId.hashCode,
//     //     notification.title,
//     //     notification.body,
//     //     NotificationDetails(
//     //       android: AndroidNotificationDetails(
//     //         channel.id,
//     //         channel.name,
//     //         playSound: true,
//     //         importance: Importance.high,
//     //         icon: '@mipmap/ic_launcher',
//     //         fullScreenIntent: true,
//     //         channelShowBadge: true,
//     //       ),
//     //       iOS: const DarwinNotificationDetails(
//     //           interruptionLevel: InterruptionLevel.active,
//     //           presentSound: true,
//     //           presentAlert: true),
//     //     ),
//     //     payload: getPayload(message.data),
//     //   );
//     // } else {
//     //   log("custom notification");
//     //   flutterLocalNotificationsPlugin.show(
//     //     message.messageId.hashCode,
//     //     message.data['title'],
//     //     message.data['body'],
//     //     NotificationDetails(
//     //         android: AndroidNotificationDetails(
//     //           channel.id,
//     //           channel.name,
//     //           playSound: true,
//     //           importance: Importance.high,
//     //           icon: '@mipmap/ic_launcher',
//     //           fullScreenIntent: true,
//     //           channelShowBadge: true,
//     //         ),
//     //         iOS: const DarwinNotificationDetails(
//     //             interruptionLevel: InterruptionLevel.active,
//     //             presentSound: true,
//     //             presentAlert: true)),
//     //     payload: getPayload(message.data),
//     //   );
//     //   return;
//     // }
//     AndroidNotificationChannel channel = AndroidNotificationChannel(
//         math.Random.secure().nextInt(1000000).toString(),
//         "High Importance notfication",
//         importance: Importance.max);
//     AndroidNotificationDetails androidNotificationDetails =
//         AndroidNotificationDetails(
//       channel.id,
//       channel.name,
//       channelDescription: "Description",
//       importance: Importance.high,
//       priority: Priority.high,
//       ticker: "ticker",
//     );
//     DarwinNotificationDetails darwinNotificationDetails =
//         const DarwinNotificationDetails(
//             presentAlert: true, presentBadge: true, presentSound: true);
//     NotificationDetails notificationDetails = NotificationDetails(
//         android: androidNotificationDetails, iOS: darwinNotificationDetails);
//     Future.delayed(Duration.zero, () {
//       _flutterLocalNotificationsPlugin.show(
//           0,
//           message.notification?.title.toString(),
//           message.notification?.body.toString(),
//           notificationDetails);
//     });
//   }
//   String getPayload(Map<String, dynamic> data) {
//     if (data['type'] == "order") {
//       return "${data['type']}+${data['order_id']}";
//     } else if (data['type'] == "category") {
//       return "${data['type']}+${data['category_id']}";
//     } else if (data['type'] == "subcategory") {
//       return "${data['type']}+${data['subcategory_id']}+${data['category_id']}";
//     } else if (data['type'] == "product") {
//       return "${data['type']}+${data['product_id']}";
//     } else {
//       return data['type'];
//     }
//   }
// }
// // message.data['order_id']
// Future<void> handlenavigation({required String payload}) async {
//   GetStorage('payload').erase();
//   if (payload.split('+').first == "order") {
//     if (storage.read('userdata') != null) {
//       return Get.to(() => OrderDetailScreen(
//             orderId: payload.split('+').last,
//           ));
//     }
//   }
//   // if (payload.split('+').first == "category") {
//   //   return Get.to(() => CategoryScreen(
//   //         categoryid: payload.split('+').last,
//   //         categoryname: '',
//   //       ));
//   // }
//   // if (payload.split('+').first == "subcategory") {
//   //   return Get.to(
//   //     () => CategoryScreen(
//   //       categoryid: payload.split('+').last,
//   //       categoryname: '',
//   //       subcategoryid: int.parse(
//   //         payload.split('+').elementAt(1),
//   //       ),
//   //     ),
//   //   );
//   // }
//   if (payload.split('+').first == "product") {
//     return Get.to(() => Scaffold(
//           appBar: AppBar(
//             elevation: 0,
//             foregroundColor: kblack,
//             backgroundColor: Colors.white,
//           ),
//           //TODO: Show product screen for appropriate product by id here
//           body: const Center(
//             child: Text('Item doesn\'t exist'),
//           ),
//           // body: ProductScreen(productId: payload.split('+').last),
//         ));
//   }
//   if (payload == "profile") {
//     if (storage.read('userdata') != null) {
//       return Get.to(() => const EditProfileScreen());
//     }
//   }
//   if (payload == "cart") {
//     return Get.to(() => const CartScreen());
//   }
// }
// Future<void> dynamiclinknavigation({required String payload}) async {
//   if (payload.split('/').last == "order") {
//     if (storage.read('userdata') != null) {
//       return Get.to(() => OrderDetailScreen(
//             orderId: payload.split('/')[payload.split('/').length - 2],
//           ));
//     }
//   }
//   // if (payload.split('/').first == "category") {
//   //   return Get.to(() => CategoryScreen(
//   //         categoryid: payload.split('/')[payload.split('/').length - 2],
//   //         categoryname: '',
//   //       ));
//   // }
//   // if (payload.split('/').first == "subcategory") {
//   //   return Get.to(
//   //     () => CategoryScreen(
//   //       categoryid: payload.split('/')[payload.split('/').length - 2],
//   //       categoryname: '',
//   //       subcategoryid: int.parse(
//   //         payload.split('/')[payload.split('/').length - 3],
//   //       ),
//   //     ),
//   //   );
//   // }
//   if (payload.split('/').last == "product") {
//     return Get.to(
//       () => Scaffold(
//         appBar: AppBar(
//           elevation: 0,
//           foregroundColor: kblack,
//           backgroundColor: Colors.white,
//         ),
//         //TODO: Show product screen for appropriate product by id here
//         body: const Center(
//           child: Text('Item doesn\'t exist'),
//         ),
//         // body: ProductScreen(
//         //   productId: payload.split('/')[payload.split('/').length - 2],
//         // ),
//       ),
//     );
//   }
//   if (payload.split('/').last == "profile") {
//     if (storage.read('userdata') != null) {
//       return Get.to(() => const EditProfileScreen());
//     }
//   }
//   if (payload.split('/').last == "me") {
//     if (storage.read('userdata') != null) {
//       return Get.to(() => const EditProfileScreen());
//     }
//   }
//   if (payload.split('/').last == "cart") {
//     return Get.to(() => const CartScreen());
//   }
// }