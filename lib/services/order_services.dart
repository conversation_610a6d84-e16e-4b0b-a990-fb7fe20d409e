import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
// import 'package:http/http.dart' as http;

import '../model/model.dart';
import '../model/single_order.dart';
import '../utils/exception_handler.dart';
import '../controllers/user_controller.dart';
import '../model/order/order_list_model/order_list_model.dart';
import '../utils/constants.dart';

class OrderServices {
  final Dio _dio;
  OrderServices(this._dio);
  static final UserController userController = Get.find<UserController>();
  //static var client = http.Client();

  // static Future initiatePayment(params) async {
  //   String url = '${baseURL}api/v1/ecomm/initiatePayment';
  //   GetStorage storage = GetStorage();
  //   String jwt = storage.read('JWT') ?? '';
  //   Map<String, String> headers = {
  //     'Authorization': 'Bearer $jwt',
  //     'Content-type': 'application/json'
  //   };
  //   // ignore: prefer_typing_uninitialized_variables
  //   var result;
  //   try {
  //     var response = await client
  //         .post(Uri.parse(url), body: json.encode(params), headers: headers)
  //         .then((value) => Utils.logResponse(value, params));
  //     var jsonRes = response.body;
  //     var jsonResponse = json.decode(jsonRes);
  //     // print('here--- ${jsonResponse['data']}');
  //     result = jsonResponse;
  //     return result;
  //   } catch (e, stackTrace) {
  //     if (kDebugMode) {
  //       print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
  //     }
  //     return result;
  //   }
  // }

  Future<ResponseModel> createOrder({
    required List<LocalShoppingCart> items,
    required Address address,
    required String paymentMethod,
    required String storeId,
  }) async {
    String jwt = Get.find<UserController>().userdata.value?.token ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    Map<String, dynamic> data = {
      "items": items
          .map(
            (e) => {
              "item_id": e.productID,
              "quantity": e.qty,
              "purchase_order_id": e.purchaseOrderID,
            },
          )
          .toList(),
      "address": {
        "type": address.type,
        "street": address.line2,
        "house": address.line1,
        "city": address.city,
        "landmark": address.landmark,
        "pincode": address.pincode,
        "coordinates": address.geoLocation?.coordinates // [long, lat]
      },
      "payment_method": paymentMethod, //['COD', 'UPI', 'cash'],
      "store": storeId,
    };

    try {
      final response = await _dio.post(
        '/api/on-demand-delivery/customer/create/order',
        data: data,
        options: Options(
          headers: headers,
          validateStatus: (status) => status == 200,
        ),
      );

      return ResponseModel(
        data: response.data['data']["_id"],
        message: response.data['msg'].toString(),
        success: true,
      );
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<OrderListModel?> getOrderHistory({
    int page = 1,
    int limit = 10,
  }) async {
    String url =
        '$baseURL/api/on-demand-delivery/customer/get/orders?page=$page&limit=$limit';

    String? jwt = Get.find<UserController>().userdata.value?.token;

    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      final response = await _dio.get(url, options: Options(headers: headers));

      if (response.statusCode == 200) {
        return OrderListModel.fromJson(response.data);
      }
      throw response.data['msg']?.toString() ?? "Something went wrong";
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  // static Future vaidateCouponCode(params) async {
  //   String url = '${baseURL}api/v1/ecomm/validateDiscount';
  //   String jwt = Get.find<UserController>().userdata.value?.token ?? '';
  //   Map<String, String> body = {
  //     'coupon_code': params['coupon_code'].toString(),
  //     'sub_total': params['sub_total'].toString(),
  //   };
  //   Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
  //   dynamic coupon;
  //   try {
  //     var response = await client
  //         .post(Uri.parse(url), body: body, headers: headers)
  //         .then((value) => Utils.logResponse(value, body));
  //     var jsonRes = response.body;
  //     var jsonResponse = json.decode(jsonRes);
  //     coupon = jsonResponse;
  //     return coupon;
  //   } catch (e) {
  //     return coupon;
  //   }
  // }

  Future<ResponseModel<SingleOrder?>> getSingleOrder(String? orderid) async {
    final url = '$baseURL/api/on-demand-delivery/customer/get/order';

    final queryParameters = {"_id": orderid};

    final jwt = Get.find<UserController>().userdata.value?.token;

    final headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      final response = await _dio.get(
        url,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      if (response.statusCode == 200) {
        return ResponseModel<SingleOrder?>(
          success: true,
          message: response.data['msg']?.toString(),
          data: response.data['data'] == null
              ? null
              : SingleOrder.fromJson(response.data['data']),
        );
      }
      throw response.data['msg']?.toString() ?? 'something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<ResponseModel<List<SingleOrder>>> getAllOngoingOrders() async {
    final url =
        '$baseURL/api/on-demand-delivery/customer/get/all/ongoing/orders';

    final jwt = Get.find<UserController>().userdata.value?.token;

    final headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    try {
      final response = await _dio.get(
        url,
        options: Options(headers: headers),
      );
      if (response.statusCode == 200) {
        return ResponseModel<List<SingleOrder>>(
          success: true,
          message: response.data['msg']?.toString(),
          data: response.data['data'] == null
              ? []
              : List<SingleOrder>.from(
                  (response.data['data'] as List).map(
                    (e) => SingleOrder.fromJson(e),
                  ),
                ),
        );
      }
      throw response.data['msg']?.toString() ?? 'something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<ResponseModel<void>> cancelOrder(String orderId) async {
    final url = '$baseURL/api/on-demand-delivery/customer/cancel/order';

    final jwt = Get.find<UserController>().userdata.value?.token;

    final headers = {
      HttpHeaders.authorizationHeader: 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    final data = {
      "_id": orderId,
    };
    try {
      final response = await _dio.put(
        url,
        data: data,
        options: Options(headers: headers),
      );
      if (response.statusCode == 200) {
        return ResponseModel<void>(
          data: null,
          message: response.data['msg'].toString(),
          success: response.data['success'],
        );
      }
      throw response.data['msg']?.toString() ?? 'something went wrong';
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }
}
