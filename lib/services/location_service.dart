import 'dart:convert';

import '../model/google_places/place_autocomplete_response.dart';
import '../utils/constants.dart';
import "package:http/http.dart" as http;

import '../utils/utils.dart';

class LocationService {
  LocationService._();

  static Future<PlaceAutocompleteResponse> placesAutoComplete(
      String place) async {
    Uri uri = Uri.https(
      "maps.googleapis.com",
      "maps/api/place/autocomplete/json", // unencoded path
      {
        "input": place,
        "key": mapsApiKey,
        "radius": "5000", //
      },
    );
    try {
      http.Response response =
          await http.get(uri).then((value) => Utils.logResponse(value));
      final body = json.decode(response.body);
      if (response.statusCode == 200) {
        if (body["status"] != null && body["status"] is String) {
          if (body["status"] == "OK" || body["status"] == "ZERO_RESULTS") {
            return PlaceAutocompleteResponse.fromJson(body);
          }
        }
        throw body["error_message"] ?? "something went wrong";
      }
      throw "Couldn't get search results";
    } catch (_) {
      rethrow;
    }
  }
}
