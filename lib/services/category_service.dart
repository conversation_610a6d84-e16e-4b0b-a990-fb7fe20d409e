import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../model/category_model/category_model.dart';
import '../controllers/user_controller.dart';
import '../model/model.dart';
import '../utils/constants.dart';
import '../utils/exception_handler.dart';

class CategoryService {
  final Dio _dio;
  CategoryService(this._dio);
//Get.find<UserController>().nearbyStore?.id ?? ''
  Future<ResponseModel<CategoryModel?>> getCategoryList(
      {required String storeId}) async {
    String url =
        '$baseURL/api/on-demand-delivery/customer/get/categories?store=$storeId';

    Map<String, String> headers = {
      HttpHeaders.authorizationHeader:
          'Bearer ${Get.find<UserController>().userdata.value?.token}'
    };

    try {
      final response = await _dio.get(url, options: Options(headers: headers));

      if (response.statusCode == 200) {
        return ResponseModel<CategoryModel?>(
          success: response.data['success'],
          message: response.data['msg'],
          data: response.data['data'] == null
              ? null
              : CategoryModel.fromJson(response.data['data']),
        );
      }
      throw response.data['msg']?.toString() ?? "Something went wrong";
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      rethrow;
    }
  }

  Future<ResponseModel<List<BannerAd>>> getBannerAds() async {
    Map<String, dynamic> headers = {
      HttpHeaders.authorizationHeader:
          'Bearer ${Get.find<UserController>().userdata.value?.token}'
    };
    try {
      final response = await _dio.get(
        '$baseURL/api/on-demand-delivery/customer/get/banners',
        options: Options(headers: headers),
      );
      if (response.statusCode == 200) {
        return ResponseModel<List<BannerAd>>(
            success: response.data['success'],
            message: response.data['msg'],
            data: response.data['data'] == null
                ? []
                : List<BannerAd>.from((response.data['data']['docs'] as List)
                    .map((e) => BannerAd.fromJson(e))));
      }
      throw response.data['msg']?.toString() ?? "something went wrong";
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (_) {
      rethrow;
    }
  }
}
