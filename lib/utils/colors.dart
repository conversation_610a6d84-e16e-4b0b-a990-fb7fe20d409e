import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'utils.dart';

abstract class AppColors {
  static final Color ctaTextColor =
      dotenv.env['ctaTextColor']?.toString().toColor() ?? Colors.white;
  static final Color ctaBackgroundColor =
      dotenv.env['ctaBackgroundColor']?.toString().toColor() ??
          const Color(0xFF161616);

  static final Color pageBackgroundColor =
      dotenv.env['pageBackgroundColor']?.toString().toColor() ??
          const Color(0xfff6f8ff);
  static final Color kgrey =
      dotenv.env['kgrey']?.toString().toColor() ?? const Color(0xFFC5C8CD);
  static final Color kgreyDark =
      dotenv.env['kgreyDark']?.toString().toColor() ?? const Color(0xFF808080);
  static final Color primaryColor =
      dotenv.env['primaryColor']?.toString().toColor() ??
          const Color(0xFF5074F3);
  static final Color kDecoratedCardBorder =
      dotenv.env['kDecoratedCardBorder']?.toString().toColor() ??
          const Color(0xffeaeaea);
  static final Color ktealGrey =
      dotenv.env['ktealGrey']?.toString().toColor() ?? const Color(0xff556f80);
  static final Color kHomeAppBarBG =
      dotenv.env['kHomeAppBarBG']?.toString().toColor() ??
          const Color(0xffedf1fe);
  static const Color kParentCategoryCardBG = Color(0xffF2EFED);
  static final Color secondaryBtnBorderColor =
      dotenv.env['secondaryBtnBorderColor']?.toString().toColor() ??
          const Color(0xff5074F3);
  static final Color secondaryBtnForegroundColor =
      dotenv.env['secondaryBtnForegroundColor']?.toString().toColor() ??
          const Color(0xff5074F3);

  static final Color splashBackgroundColor =
      dotenv.env['splashBackgroundColor']?.toString().toColor() ??
          const Color(0xff000000);
  static final Color homeAppBarIconColor =
      dotenv.env['homeAppBarIconColor']?.toString().toColor() ??
          const Color(0xff5074F3);
}
