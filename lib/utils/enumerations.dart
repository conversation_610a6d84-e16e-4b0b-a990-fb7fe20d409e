enum DiscountType { currency, percent }

enum PaymentType {
  creditCard,
  debitCard,
  upi,
  wallet,
  cashhOnDelivery;

  String get name => switch (this) {
        PaymentType.creditCard => "Credit Card",
        PaymentType.debitCard => "Debit Card",
        PaymentType.upi => "UPI",
        PaymentType.wallet => "Wallet",
        PaymentType.cashhOnDelivery => "Cash on Delivery",
      };

  String get svgAssetPath => switch (this) {
        PaymentType.creditCard => "assets/svg/quill_creditcard.svg",
        PaymentType.debitCard => "assets/svg/quill_creditcard.svg",
        PaymentType.upi => "assets/svg/upi_logo.svg",
        PaymentType.wallet => "assets/svg/wallet_icon.svg",
        PaymentType.cashhOnDelivery => "",
      };
  static List<PaymentType> get addableValues => [
        PaymentType.creditCard,
        PaymentType.upi,
        PaymentType.wallet,
      ];
}