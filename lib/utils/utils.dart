import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

class Utils {
  static double getScreenHeightByPercentage(double percentage) =>
      Get.height * (percentage / 100);

  static double getScreenWidthByPercentage(double percentage) =>
      Get.height * (percentage / 100);

  static http.Response logResponse(
    http.Response res, [
    Map payload = const {},
  ]) {
    const encoder = JsonEncoder.withIndent(" ");
    const decoder = JsonDecoder();

    try {
      log("---------- Request ----------\n"
          "URL: ${res.request?.url} ,\n"
          "METHOD: ${res.request?.method} ,\n"
          "PAYLOAD: ${encoder.convert(payload)} ,\n"
          "---------- Response ----------\n"
          "STATUS CODE: ${res.statusCode} ,\n"
          "REQUEST HEADERS: ${encoder.convert(res.request?.headers)}");

      if (res.statusCode == 200) {
        log("BODY: ${encoder.convert(decoder.convert(res.body))}");
      } else {
        log("BODY: ${res.body} ,\n");
      }
    } catch (e) {
      log("Error: $e\n");
      log("BODY length: ${res.body.length} ,\n");
    }
    return res;
  }

  static Future<bool?> customToast({
    required String message,
    ToastGravity gravity = ToastGravity.BOTTOM,
    Color? backgroundColor,
    int timeInSecForIosWeb = 1,
  }) async {
    await Fluttertoast.cancel();

    return Fluttertoast.showToast(
      msg: message,
      toastLength:
          timeInSecForIosWeb < 2 ? Toast.LENGTH_SHORT : Toast.LENGTH_LONG,
      gravity: gravity,
      backgroundColor: backgroundColor,
      timeInSecForIosWeb: timeInSecForIosWeb,
      textColor: Colors.white,
      fontSize: 12.0,
    );
  }
}

extension ColorExtension on String {
  Color? toColor() {
    try {
      if (length == 6 || length == 7) {
        return Color.fromARGB(
          255, // Alpha channel (opacity)
          int.parse(substring(1, 3), radix: 16), // Red component
          int.parse(substring(3, 5), radix: 16), // Green component
          int.parse(substring(5, 7), radix: 16), // Blue component
        );
      }
    } catch (e, st) {
      log('Invalid hexcode\nError: $e, Stacktrace: $st');
    }
    return null;
    // final buffer = StringBuffer();
    // if (hexString.length == 6 || hexString.length == 7) {
    //   buffer.write('ff');
    // }
    // buffer.write(hexString.replaceFirst('#', ''));

    // return Color(int.parse(buffer.toString(), radix: 16));
  }
}
