import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../controllers/account_controller.dart';

/// returns (statusCode, exception-message)
ServiceException handleDioException(DioException exception) {
  if (exception.response?.statusCode == 401) {
    (Get.isRegistered<AccountController>()
            ? Get.find<AccountController>()
            : Get.put<AccountController>(AccountController()))
        .logoutUser();
    return ServiceException(
      401,
      exception.response?.data['msg'].toString() ?? 'Something went wrong',
    );
  }
  if (exception.response?.data['msg'] is String) {
    return ServiceException(
      exception.response?.statusCode,
      exception.response?.data['msg'].toString() ?? 'Something went wrong',
    );
  }
  if (exception.response?.data['error'] is Map<String, dynamic>) {
    if (exception.response?.data['error']['errors'] is List) {
      return ServiceException(
        exception.response?.statusCode,
        (exception.response?.data['error']['errors'] as List)
            .map<String>(
              (e) => e['msg'].toString(),
            )
            .join('\n'),
      );
    }
  }

  return ServiceException(
    exception.response?.statusCode,
    "Error: ${exception.response?.statusCode?.toString() ?? 'Something went wrong'}",
  );
}

class ServiceException implements Exception {
  final int? statusCode;
  final String msg;
  ServiceException(this.statusCode, this.msg);

  @override
  String toString() => msg;
}
