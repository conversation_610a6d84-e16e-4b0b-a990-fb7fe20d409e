import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'colors.dart';

class RapsapTheme {
  static final theme = ThemeData(
    useMaterial3: true,
    scaffoldBackgroundColor: AppColors.pageBackgroundColor,
    primaryColorLight: AppColors.primaryColor,
    primaryColor: AppColors.primaryColor,
    fontFamily: GoogleFonts.dmSans().fontFamily,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        foregroundColor: AppColors.ctaTextColor,
        backgroundColor: AppColors.ctaBackgroundColor,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
      ),
    ),
  );
}
