import 'package:get/get.dart';

import '../controllers/category_controller.dart';
import '../controllers/location_controller.dart';
import '../controllers/wishlist_controller.dart';
import '../controllers/account_controller.dart';
import '../controllers/cart_controlller.dart';
import '../controllers/home_view_controller.dart';
import '../controllers/order_controller.dart';
import '../controllers/product_view_controller.dart';
import '../controllers/user_controller.dart';

class HomeBinding implements Bindings {
  @override
  void dependencies() {
    Get
      ..put<UserController>(UserController())
      ..put<HomeViewController>(HomeViewController())
      ..put<CartController>(CartController())
      ..put<ProductViewController>(ProductViewController())
      ..put<OrderController>(OrderController.instance())
      ..put<AccountController>(AccountController())
      ..put<WishlistController>(WishlistController())
      ..put<CategoryController>(CategoryController())
      ..put<LocationController>(LocationController());
  }
}
