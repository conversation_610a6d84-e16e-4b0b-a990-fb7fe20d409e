import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import '../model/category_color.dart';

//asset
const String splashbackground = 'assets/images/splash_background.webp';
const String logo = 'assets/images/logo.svg';
const String loginhead = 'assets/images/loginheadImg.webp';
const String loginheadtitle = 'assets/images/logo_login_head.svg';

final baseURL = dotenv.env['API_URL']?.toString() ?? "https://your-api-url.com";
const String mapsApiKey = "YOUR_GOOGLE_MAPS_API_KEY";

final String orgDBName = dotenv.env['ORG_DB_NAME']?.toString() ?? '';

const categorycolorlist = [
  CategoryColor(Color(0xffF1FFEF), Color(0xff0D9E00)),
  CategoryColor(Color(0xffFFE6E6), Color(0xffFF0000)),
  CategoryColor(Color(0xffE6EFFF), Color(0xff005DFF)),
  CategoryColor(Color(0xffFFF6E6), Color(0xffB77200)),
  CategoryColor(Color(0xffF2EFED), Color(0xff7A5649)),
  CategoryColor(Color(0xffFAFBE6), Color(0xff9AA000)),
  CategoryColor(Color(0xffFFEEE6), Color(0xffFD5100)),
  CategoryColor(Color(0xffF9E6EB), Color(0xffD40035)),
  CategoryColor(Color(0xffE6FAF4), Color(0xff00B881)),
];
