{"nm": "Flow 16", "ddd": 0, "h": 60, "w": 60, "meta": {"g": "LottieFiles Figma v64"}, "layers": [{"ty": 0, "nm": "trolley-_1_ 1", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": true, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 253}, {"s": [1, 1], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 253}, {"s": [100, 100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 30], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 30], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 30], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 30], "t": 253}, {"s": [29, 30], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "masksProperties": [{"nm": "", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 301}]}}], "w": 60, "h": 60, "refId": "1", "ind": 1}, {"ty": 4, "nm": "Ellipse 72", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 253}, {"s": [30, 30], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 253}, {"s": [100, 100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 253}, {"s": [30, 30], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 301}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 253}, {"s": [0.3157, 0.4562, 0.9542], "t": 301}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}], "ind": 2}], "v": "5.7.0", "fr": 60, "op": 253.19, "ip": 0, "assets": [{"nm": "[Asset] trolley-_1_ 1", "id": "1", "layers": [{"ty": 0, "nm": "Group", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 253}, {"s": [1, 0.92], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, -100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, -100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, -100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, -100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, -100], "t": 253}, {"s": [100, -100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1.08], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1.08], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 19.47], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 19.47], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1.08], "t": 253}, {"s": [1, 1.08], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "ef": [], "w": 60, "h": 60, "refId": "2", "ind": 1}, {"ty": 4, "nm": "trolley-_1_ 1 Bg", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 253}, {"s": [1, 1], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 253}, {"s": [100, 100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 253}, {"s": [1, 1], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 301}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 253}, {"s": [1, 1, 1], "t": 301}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}}], "ind": 2}]}, {"nm": "[Asset] Group", "id": "2", "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.11, 0.11], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.11, 0.11], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.98, 1.97], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.98, 1.97], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.11, 0.11], "t": 253}, {"s": [0.11, 0.11], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 253}, {"s": [100, 100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.11, 0.24], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.11, 0.24], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [19.96, 4.29], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [19.96, 4.29], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.11, 0.24], "t": 253}, {"s": [1.11, 0.24], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.05, 0.06], [-0.03, 0], [-0.02, -0.02], [0.06, -0.04], [0.01, 0], [0.02, 0.01]], "o": [[-0.07, -0.03], [0.02, -0.02], [0.03, 0], [0.06, 0.04], [-0.01, 0.01], [-0.02, 0.01], [0, 0]], "v": [[0.07, 0.21], [0.03, 0.04], [0.1, 0], [0.18, 0.02], [0.18, 0.2], [0.14, 0.21], [0.07, 0.21]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.05, 0.06], [-0.03, 0], [-0.02, -0.02], [0.06, -0.04], [0.01, 0], [0.02, 0.01]], "o": [[-0.07, -0.03], [0.02, -0.02], [0.03, 0], [0.06, 0.04], [-0.01, 0.01], [-0.02, 0.01], [0, 0]], "v": [[0.07, 0.21], [0.03, 0.04], [0.1, 0], [0.18, 0.02], [0.18, 0.2], [0.14, 0.21], [0.07, 0.21]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.89, 1.01], [-0.55, 0.05], [-0.44, -0.33], [1.01, -0.77], [0.15, -0.05], [0.36, 0.16]], "o": [[-1.23, -0.56], [0.37, -0.41], [0.56, -0.04], [1.01, 0.77], [-0.18, 0.14], [-0.41, 0.13], [0, 0]], "v": [[1.18, 3.78], [0.5, 0.66], [1.8, 0.01], [3.2, 0.41], [3.2, 3.52], [2.59, 3.85], [1.18, 3.78]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.89, 1.01], [-0.55, 0.05], [-0.44, -0.33], [1.01, -0.77], [0.15, -0.05], [0.36, 0.16]], "o": [[-1.23, -0.56], [0.37, -0.41], [0.56, -0.04], [1.01, 0.77], [-0.18, 0.14], [-0.41, 0.13], [0, 0]], "v": [[1.18, 3.78], [0.5, 0.66], [1.8, 0.01], [3.2, 0.41], [3.2, 3.52], [2.59, 3.85], [1.18, 3.78]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.05, 0.06], [-0.03, 0], [-0.02, -0.02], [0.06, -0.04], [0.01, 0], [0.02, 0.01]], "o": [[-0.07, -0.03], [0.02, -0.02], [0.03, 0], [0.06, 0.04], [-0.01, 0.01], [-0.02, 0.01], [0, 0]], "v": [[0.07, 0.21], [0.03, 0.04], [0.1, 0], [0.18, 0.02], [0.18, 0.2], [0.14, 0.21], [0.07, 0.21]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [-0.05, 0.06], [-0.03, 0], [-0.02, -0.02], [0.06, -0.04], [0.01, 0], [0.02, 0.01]], "o": [[-0.07, -0.03], [0.02, -0.02], [0.03, 0], [0.06, 0.04], [-0.01, 0.01], [-0.02, 0.01], [0, 0]], "v": [[0.07, 0.21], [0.03, 0.04], [0.1, 0], [0.18, 0.02], [0.18, 0.2], [0.14, 0.21], [0.07, 0.21]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, 0], [-0.02, -0.03], [-0.02, 0.01]], "o": [[0.04, -0.03], [-0.04, 0], [0.01, 0.02], [0, 0]], "v": [[0.14, 0.15], [0.11, 0.06], [0.06, 0.13], [0.14, 0.15]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, 0], [-0.02, -0.03], [-0.02, 0.01]], "o": [[0.04, -0.03], [-0.04, 0], [0.01, 0.02], [0, 0]], "v": [[0.14, 0.15], [0.11, 0.06], [0.06, 0.13], [0.14, 0.15]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.91, 0], [-0.32, -0.63], [-0.43, 0.25]], "o": [[0.78, -0.46], [-0.71, 0], [0.23, 0.44], [0, 0]], "v": [[2.45, 2.76], [1.99, 1.05], [1.15, 2.39], [2.45, 2.76]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.91, 0], [-0.32, -0.63], [-0.43, 0.25]], "o": [[0.78, -0.46], [-0.71, 0], [0.23, 0.44], [0, 0]], "v": [[2.45, 2.76], [1.99, 1.05], [1.15, 2.39], [2.45, 2.76]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, 0], [-0.02, -0.03], [-0.02, 0.01]], "o": [[0.04, -0.03], [-0.04, 0], [0.01, 0.02], [0, 0]], "v": [[0.14, 0.15], [0.11, 0.06], [0.06, 0.13], [0.14, 0.15]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0.05, 0], [-0.02, -0.03], [-0.02, 0.01]], "o": [[0.04, -0.03], [-0.04, 0], [0.01, 0.02], [0, 0]], "v": [[0.14, 0.15], [0.11, 0.06], [0.06, 0.13], [0.14, 0.15]]}], "t": 301}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 253}, {"s": [0.3157, 0.4562, 0.9542], "t": 301}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}], "ind": 1}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 254.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 253}, {"s": [1, 0.92], "t": 301}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 253}, {"s": [100, 100], "t": 301}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.98, 16.52], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.92], "t": 253}, {"s": [1, 0.92], "t": 301}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 253}, {"s": [0], "t": 301}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0.04, -0.01], [0.1, -0.03], [0.02, 0], [0.02, 0.01], [-0.05, 0.05], [-0.21, 0.07], [-0.01, 0], [-0.01, 0.02], [-0.12, 0.28], [0, 0], [0, 0], [-0.02, 0.07], [-0.06, 0.03], [-0.05, 0], [-0.04, -0.05], [-0.01, -0.04], [0, 0], [-0.06, -0.02], [0, 0], [0.01, -0.01], [0, 0], [0.05, 0.02], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [-0.11, -0.04], [-0.05, -0.02], [-0.01, 0], [0.03, 0.03], [0.07, 0.03], [0, 0], [-0.01, 0.01], [0, 0], [-0.02, -0.01], [0.04, -0.05], [0.03, 0], [0.04, 0.02], [0, 0], [0.03, -0.07], [0, 0], [0.01, 0], [0, 0.01], [-0.03, 0.07], [0, 0], [0.02, 0.01], [0.1, 0.04], [0, 0], [0, 0], [0.04, -0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0], [-0.03, 0.07], [0, 0.01], [-0.02, 0], [-0.05, -0.02], [-0.01, -0.01], [0, -0.02], [0.03, -0.06], [0, 0], [-0.04, -0.02], [0, 0], [-0.02, 0.05], [0, 0], [0, -0.02], [0.02, -0.04], [0.02, -0.01], [0.28, 0.11]], "o": [[-0.14, -0.06], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.03], [-0.01, 0.01], [-0.16, 0.05], [-0.02, 0], [-0.06, -0.04], [0.02, -0.01], [0.09, -0.03], [0.02, -0.01], [0.01, -0.01], [0, 0], [0, 0], [-0.04, -0.06], [0.02, -0.07], [0.04, -0.02], [0.06, 0], [0.03, 0.03], [0, 0.01], [0, 0], [0.06, 0.02], [0.01, 0.01], [-0.01, 0], [0, 0], [-0.05, -0.02], [0, 0], [0, 0.01], [-0.01, 0.01], [0, 0], [0.11, 0.04], [0.05, 0.02], [0.04, 0], [-0.01, -0.01], [-0.07, -0.03], [-0.01, -0.01], [0.01, -0.01], [0.01, 0], [0.05, 0.03], [-0.02, 0.02], [-0.02, 0], [-0.02, -0.01], [0, 0], [-0.03, 0.07], [0, 0.01], [-0.01, 0], [0, 0], [0.03, -0.07], [0, 0], [-0.02, -0.01], [0, 0], [0, 0], [-0.03, 0.02], [0, 0], [0, 0], [-0.05, 0.12], [0, 0], [0, 0], [0.02, -0.05], [0.01, -0.02], [0.02, 0], [0.03, 0.01], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0, 0], [0.05, 0.02], [0, 0], [0.02, -0.05], [0.02, -0.02], [0, 0.01], [-0.01, 0.02], [-0.04, 0.01], [0, 0]], "v": [[0.94, 1.73], [0.67, 1.62], [0.65, 1.6], [0.64, 1.59], [0.62, 1.62], [0.5, 1.69], [0.3, 1.76], [0.1, 1.83], [0.05, 1.81], [0.04, 1.64], [0.26, 1.56], [0.43, 1.5], [0.5, 1.43], [0.72, 0.9], [0.93, 0.4], [0.92, 0.38], [0.88, 0.18], [1, 0.03], [1.13, 0], [1.28, 0.07], [1.34, 0.19], [1.35, 0.22], [1.46, 0.27], [1.57, 0.32], [1.57, 0.36], [1.55, 0.37], [1.45, 0.33], [1.35, 0.29], [1.34, 0.31], [1.32, 0.34], [1.31, 0.37], [1.51, 0.45], [1.79, 0.57], [1.9, 0.61], [1.93, 0.53], [1.79, 0.47], [1.66, 0.41], [1.66, 0.37], [1.67, 0.36], [1.96, 0.48], [1.97, 0.63], [1.9, 0.67], [1.83, 0.65], [1.79, 0.63], [1.73, 0.75], [1.67, 0.89], [1.65, 0.89], [1.62, 0.87], [1.67, 0.74], [1.73, 0.61], [1.69, 0.59], [1.46, 0.5], [1.27, 0.42], [1.24, 0.43], [1.15, 0.47], [1.12, 0.48], [1.03, 0.7], [0.93, 0.94], [1.08, 1], [1.13, 0.91], [1.18, 0.8], [1.23, 0.78], [1.32, 0.8], [1.39, 0.84], [1.42, 0.89], [1.38, 1.01], [1.34, 1.11], [1.42, 1.14], [1.5, 1.17], [1.54, 1.07], [1.58, 0.97], [1.63, 0.99], [1.3, 1.78], [1.25, 1.83], [0.94, 1.73]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0.04, -0.01], [0.1, -0.03], [0.02, 0], [0.02, 0.01], [-0.05, 0.05], [-0.21, 0.07], [-0.01, 0], [-0.01, 0.02], [-0.12, 0.28], [0, 0], [0, 0], [-0.02, 0.07], [-0.06, 0.03], [-0.05, 0], [-0.04, -0.05], [-0.01, -0.04], [0, 0], [-0.06, -0.02], [0, 0], [0.01, -0.01], [0, 0], [0.05, 0.02], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [-0.11, -0.04], [-0.05, -0.02], [-0.01, 0], [0.03, 0.03], [0.07, 0.03], [0, 0], [-0.01, 0.01], [0, 0], [-0.02, -0.01], [0.04, -0.05], [0.03, 0], [0.04, 0.02], [0, 0], [0.03, -0.07], [0, 0], [0.01, 0], [0, 0.01], [-0.03, 0.07], [0, 0], [0.02, 0.01], [0.1, 0.04], [0, 0], [0, 0], [0.04, -0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0], [-0.03, 0.07], [0, 0.01], [-0.02, 0], [-0.05, -0.02], [-0.01, -0.01], [0, -0.02], [0.03, -0.06], [0, 0], [-0.04, -0.02], [0, 0], [-0.02, 0.05], [0, 0], [0, -0.02], [0.02, -0.04], [0.02, -0.01], [0.28, 0.11]], "o": [[-0.14, -0.06], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.03], [-0.01, 0.01], [-0.16, 0.05], [-0.02, 0], [-0.06, -0.04], [0.02, -0.01], [0.09, -0.03], [0.02, -0.01], [0.01, -0.01], [0, 0], [0, 0], [-0.04, -0.06], [0.02, -0.07], [0.04, -0.02], [0.06, 0], [0.03, 0.03], [0, 0.01], [0, 0], [0.06, 0.02], [0.01, 0.01], [-0.01, 0], [0, 0], [-0.05, -0.02], [0, 0], [0, 0.01], [-0.01, 0.01], [0, 0], [0.11, 0.04], [0.05, 0.02], [0.04, 0], [-0.01, -0.01], [-0.07, -0.03], [-0.01, -0.01], [0.01, -0.01], [0.01, 0], [0.05, 0.03], [-0.02, 0.02], [-0.02, 0], [-0.02, -0.01], [0, 0], [-0.03, 0.07], [0, 0.01], [-0.01, 0], [0, 0], [0.03, -0.07], [0, 0], [-0.02, -0.01], [0, 0], [0, 0], [-0.03, 0.02], [0, 0], [0, 0], [-0.05, 0.12], [0, 0], [0, 0], [0.02, -0.05], [0.01, -0.02], [0.02, 0], [0.03, 0.01], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0, 0], [0.05, 0.02], [0, 0], [0.02, -0.05], [0.02, -0.02], [0, 0.01], [-0.01, 0.02], [-0.04, 0.01], [0, 0]], "v": [[0.94, 1.73], [0.67, 1.62], [0.65, 1.6], [0.64, 1.59], [0.62, 1.62], [0.5, 1.69], [0.3, 1.76], [0.1, 1.83], [0.05, 1.81], [0.04, 1.64], [0.26, 1.56], [0.43, 1.5], [0.5, 1.43], [0.72, 0.9], [0.93, 0.4], [0.92, 0.38], [0.88, 0.18], [1, 0.03], [1.13, 0], [1.28, 0.07], [1.34, 0.19], [1.35, 0.22], [1.46, 0.27], [1.57, 0.32], [1.57, 0.36], [1.55, 0.37], [1.45, 0.33], [1.35, 0.29], [1.34, 0.31], [1.32, 0.34], [1.31, 0.37], [1.51, 0.45], [1.79, 0.57], [1.9, 0.61], [1.93, 0.53], [1.79, 0.47], [1.66, 0.41], [1.66, 0.37], [1.67, 0.36], [1.96, 0.48], [1.97, 0.63], [1.9, 0.67], [1.83, 0.65], [1.79, 0.63], [1.73, 0.75], [1.67, 0.89], [1.65, 0.89], [1.62, 0.87], [1.67, 0.74], [1.73, 0.61], [1.69, 0.59], [1.46, 0.5], [1.27, 0.42], [1.24, 0.43], [1.15, 0.47], [1.12, 0.48], [1.03, 0.7], [0.93, 0.94], [1.08, 1], [1.13, 0.91], [1.18, 0.8], [1.23, 0.78], [1.32, 0.8], [1.39, 0.84], [1.42, 0.89], [1.38, 1.01], [1.34, 1.11], [1.42, 1.14], [1.5, 1.17], [1.54, 1.07], [1.58, 0.97], [1.63, 0.99], [1.3, 1.78], [1.25, 1.83], [0.94, 1.73]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.13, 0.08], [0.07, 0.1], [0, 0], [0, 0], [0.64, -0.26], [1.72, -0.6], [0.44, -0.01], [0.34, 0.19], [-0.98, 0.86], [-3.77, 1.3], [-0.11, 0.06], [-0.2, 0.4], [-2.08, 5], [0, 0], [0, 0], [-0.31, 1.21], [-1.16, 0.56], [-0.84, -0.06], [-0.78, -0.82], [-0.12, -0.68], [-0.03, -0.05], [-1.06, -0.44], [-0.05, -0.06], [0.2, -0.16], [0.06, 0], [0.97, 0.41], [0.03, 0], [0.05, -0.16], [0.12, -0.22], [-0.04, -0.03], [-1.91, -0.79], [-0.89, -0.37], [-0.11, 0], [0.48, 0.48], [1.33, 0.55], [0.08, 0.08], [-0.23, 0.22], [-0.06, 0], [-0.33, -0.22], [0.7, -0.82], [0.56, -0.04], [0.73, 0.3], [0.03, 0], [0.5, -1.21], [0.08, -0.08], [0.1, 0], [0, 0.19], [-0.5, 1.21], [0, 0.04], [0.39, 0.16], [1.86, 0.77], [0, 0], [0, 0], [0.63, -0.1], [0, 0], [0, 0], [0, -0.04], [-0.28, -0.06], [-0.49, 1.17], [-0.07, 0.1], [-0.42, 0], [-0.9, -0.37], [-0.12, -0.1], [0, -0.33], [0.46, -1.09], [-0.03, -0.03], [-0.75, -0.31], [-0.04, 0.06], [-0.38, 0.91], [-0.07, 0.07], [0.01, -0.42], [0.33, -0.63], [0.35, -0.12], [4.96, 2.06]], "o": [[-2.51, -1.05], [-0.13, -0.08], [0, 0], [0, 0], [-0.54, 0.54], [-0.24, 0.1], [-2.83, 0.98], [-0.43, 0.01], [-1.14, -0.64], [0.29, -0.25], [1.64, -0.57], [0.37, -0.19], [0.11, -0.21], [0, 0], [0, 0], [-0.77, -1.08], [0.32, -1.29], [0.8, -0.39], [1.12, 0.06], [0.53, 0.56], [0.04, 0.25], [0.02, 0.05], [1.06, 0.44], [0.15, 0.21], [-0.11, 0.08], [-0.05, 0], [-0.97, -0.41], [-0.04, 0], [-0.04, 0.16], [-0.11, 0.23], [0.03, 0.03], [1.91, 0.79], [0.89, 0.37], [0.68, 0], [-0.1, -0.1], [-1.25, -0.51], [-0.22, -0.19], [0.09, -0.1], [0.2, 0], [0.86, 0.59], [-0.34, 0.39], [-0.42, 0.03], [-0.44, -0.18], [-0.04, 0], [-0.5, 1.2], [-0.08, 0.09], [-0.21, 0], [0, -0.08], [0.51, -1.22], [0, -0.05], [-0.4, -0.17], [0, 0], [0, 0], [-0.56, 0.38], [0, 0], [0, 0], [-0.93, 2.24], [0, 0.06], [0.07, 0.01], [0.39, -0.93], [0.19, -0.28], [0.29, 0], [0.63, 0.26], [0.26, 0.22], [0, 0.15], [-0.4, 0.97], [0.03, 0.02], [0.98, 0.41], [0.03, -0.04], [0.37, -0.91], [0.28, -0.3], [0, 0.17], [-0.17, 0.34], [-0.72, 0.25], [0, 0]], "v": [[16.9, 31.14], [12.11, 29.09], [11.73, 28.77], [11.59, 28.6], [11.11, 29.07], [9.03, 30.49], [5.47, 31.76], [1.85, 32.86], [0.97, 32.65], [0.65, 29.61], [4.62, 28.09], [7.81, 26.94], [8.98, 25.73], [12.96, 16.25], [16.74, 7.16], [16.53, 6.86], [15.8, 3.27], [18.08, 0.46], [20.26, 0.01], [23.08, 1.34], [24.18, 3.44], [24.31, 3.99], [26.28, 4.89], [28.31, 5.81], [28.21, 6.52], [27.92, 6.67], [26.06, 5.93], [24.24, 5.2], [24.09, 5.49], [23.81, 6.2], [23.66, 6.67], [27.2, 8.17], [32.3, 10.29], [34.13, 10.96], [34.69, 9.62], [32.25, 8.51], [29.82, 7.42], [29.83, 6.71], [30.12, 6.53], [35.2, 8.68], [35.52, 11.38], [34.23, 12], [32.97, 11.71], [32.13, 11.38], [31.17, 13.58], [30.1, 15.93], [29.76, 16.09], [29.23, 15.62], [30.15, 13.28], [31.06, 11], [30.35, 10.62], [26.25, 8.92], [22.86, 7.51], [22.4, 7.82], [20.65, 8.52], [20.19, 8.6], [18.48, 12.68], [16.79, 16.83], [19.52, 18.01], [20.33, 16.34], [21.17, 14.47], [22.2, 13.98], [23.69, 14.46], [25.04, 15.1], [25.51, 16.09], [24.77, 18.1], [24.09, 19.91], [25.5, 20.52], [26.92, 21], [27.66, 19.27], [28.47, 17.47], [29.33, 17.83], [23.43, 32.12], [22.5, 32.93], [16.9, 31.14]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.13, 0.08], [0.07, 0.1], [0, 0], [0, 0], [0.64, -0.26], [1.72, -0.6], [0.44, -0.01], [0.34, 0.19], [-0.98, 0.86], [-3.77, 1.3], [-0.11, 0.06], [-0.2, 0.4], [-2.08, 5], [0, 0], [0, 0], [-0.31, 1.21], [-1.16, 0.56], [-0.84, -0.06], [-0.78, -0.82], [-0.12, -0.68], [-0.03, -0.05], [-1.06, -0.44], [-0.05, -0.06], [0.2, -0.16], [0.06, 0], [0.97, 0.41], [0.03, 0], [0.05, -0.16], [0.12, -0.22], [-0.04, -0.03], [-1.91, -0.79], [-0.89, -0.37], [-0.11, 0], [0.48, 0.48], [1.33, 0.55], [0.08, 0.08], [-0.23, 0.22], [-0.06, 0], [-0.33, -0.22], [0.7, -0.82], [0.56, -0.04], [0.73, 0.3], [0.03, 0], [0.5, -1.21], [0.08, -0.08], [0.1, 0], [0, 0.19], [-0.5, 1.21], [0, 0.04], [0.39, 0.16], [1.86, 0.77], [0, 0], [0, 0], [0.63, -0.1], [0, 0], [0, 0], [0, -0.04], [-0.28, -0.06], [-0.49, 1.17], [-0.07, 0.1], [-0.42, 0], [-0.9, -0.37], [-0.12, -0.1], [0, -0.33], [0.46, -1.09], [-0.03, -0.03], [-0.75, -0.31], [-0.04, 0.06], [-0.38, 0.91], [-0.07, 0.07], [0.01, -0.42], [0.33, -0.63], [0.35, -0.12], [4.96, 2.06]], "o": [[-2.51, -1.05], [-0.13, -0.08], [0, 0], [0, 0], [-0.54, 0.54], [-0.24, 0.1], [-2.83, 0.98], [-0.43, 0.01], [-1.14, -0.64], [0.29, -0.25], [1.64, -0.57], [0.37, -0.19], [0.11, -0.21], [0, 0], [0, 0], [-0.77, -1.08], [0.32, -1.29], [0.8, -0.39], [1.12, 0.06], [0.53, 0.56], [0.04, 0.25], [0.02, 0.05], [1.06, 0.44], [0.15, 0.21], [-0.11, 0.08], [-0.05, 0], [-0.97, -0.41], [-0.04, 0], [-0.04, 0.16], [-0.11, 0.23], [0.03, 0.03], [1.91, 0.79], [0.89, 0.37], [0.68, 0], [-0.1, -0.1], [-1.25, -0.51], [-0.22, -0.19], [0.09, -0.1], [0.2, 0], [0.86, 0.59], [-0.34, 0.39], [-0.42, 0.03], [-0.44, -0.18], [-0.04, 0], [-0.5, 1.2], [-0.08, 0.09], [-0.21, 0], [0, -0.08], [0.51, -1.22], [0, -0.05], [-0.4, -0.17], [0, 0], [0, 0], [-0.56, 0.38], [0, 0], [0, 0], [-0.93, 2.24], [0, 0.06], [0.07, 0.01], [0.39, -0.93], [0.19, -0.28], [0.29, 0], [0.63, 0.26], [0.26, 0.22], [0, 0.15], [-0.4, 0.97], [0.03, 0.02], [0.98, 0.41], [0.03, -0.04], [0.37, -0.91], [0.28, -0.3], [0, 0.17], [-0.17, 0.34], [-0.72, 0.25], [0, 0]], "v": [[16.9, 31.14], [12.11, 29.09], [11.73, 28.77], [11.59, 28.6], [11.11, 29.07], [9.03, 30.49], [5.47, 31.76], [1.85, 32.86], [0.97, 32.65], [0.65, 29.61], [4.62, 28.09], [7.81, 26.94], [8.98, 25.73], [12.96, 16.25], [16.74, 7.16], [16.53, 6.86], [15.8, 3.27], [18.08, 0.46], [20.26, 0.01], [23.08, 1.34], [24.18, 3.44], [24.31, 3.99], [26.28, 4.89], [28.31, 5.81], [28.21, 6.52], [27.92, 6.67], [26.06, 5.93], [24.24, 5.2], [24.09, 5.49], [23.81, 6.2], [23.66, 6.67], [27.2, 8.17], [32.3, 10.29], [34.13, 10.96], [34.69, 9.62], [32.25, 8.51], [29.82, 7.42], [29.83, 6.71], [30.12, 6.53], [35.2, 8.68], [35.52, 11.38], [34.23, 12], [32.97, 11.71], [32.13, 11.38], [31.17, 13.58], [30.1, 15.93], [29.76, 16.09], [29.23, 15.62], [30.15, 13.28], [31.06, 11], [30.35, 10.62], [26.25, 8.92], [22.86, 7.51], [22.4, 7.82], [20.65, 8.52], [20.19, 8.6], [18.48, 12.68], [16.79, 16.83], [19.52, 18.01], [20.33, 16.34], [21.17, 14.47], [22.2, 13.98], [23.69, 14.46], [25.04, 15.1], [25.51, 16.09], [24.77, 18.1], [24.09, 19.91], [25.5, 20.52], [26.92, 21], [27.66, 19.27], [28.47, 17.47], [29.33, 17.83], [23.43, 32.12], [22.5, 32.93], [16.9, 31.14]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0.04, -0.01], [0.1, -0.03], [0.02, 0], [0.02, 0.01], [-0.05, 0.05], [-0.21, 0.07], [-0.01, 0], [-0.01, 0.02], [-0.12, 0.28], [0, 0], [0, 0], [-0.02, 0.07], [-0.06, 0.03], [-0.05, 0], [-0.04, -0.05], [-0.01, -0.04], [0, 0], [-0.06, -0.02], [0, 0], [0.01, -0.01], [0, 0], [0.05, 0.02], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [-0.11, -0.04], [-0.05, -0.02], [-0.01, 0], [0.03, 0.03], [0.07, 0.03], [0, 0], [-0.01, 0.01], [0, 0], [-0.02, -0.01], [0.04, -0.05], [0.03, 0], [0.04, 0.02], [0, 0], [0.03, -0.07], [0, 0], [0.01, 0], [0, 0.01], [-0.03, 0.07], [0, 0], [0.02, 0.01], [0.1, 0.04], [0, 0], [0, 0], [0.04, -0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0], [-0.03, 0.07], [0, 0.01], [-0.02, 0], [-0.05, -0.02], [-0.01, -0.01], [0, -0.02], [0.03, -0.06], [0, 0], [-0.04, -0.02], [0, 0], [-0.02, 0.05], [0, 0], [0, -0.02], [0.02, -0.04], [0.02, -0.01], [0.28, 0.11]], "o": [[-0.14, -0.06], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.03], [-0.01, 0.01], [-0.16, 0.05], [-0.02, 0], [-0.06, -0.04], [0.02, -0.01], [0.09, -0.03], [0.02, -0.01], [0.01, -0.01], [0, 0], [0, 0], [-0.04, -0.06], [0.02, -0.07], [0.04, -0.02], [0.06, 0], [0.03, 0.03], [0, 0.01], [0, 0], [0.06, 0.02], [0.01, 0.01], [-0.01, 0], [0, 0], [-0.05, -0.02], [0, 0], [0, 0.01], [-0.01, 0.01], [0, 0], [0.11, 0.04], [0.05, 0.02], [0.04, 0], [-0.01, -0.01], [-0.07, -0.03], [-0.01, -0.01], [0.01, -0.01], [0.01, 0], [0.05, 0.03], [-0.02, 0.02], [-0.02, 0], [-0.02, -0.01], [0, 0], [-0.03, 0.07], [0, 0.01], [-0.01, 0], [0, 0], [0.03, -0.07], [0, 0], [-0.02, -0.01], [0, 0], [0, 0], [-0.03, 0.02], [0, 0], [0, 0], [-0.05, 0.12], [0, 0], [0, 0], [0.02, -0.05], [0.01, -0.02], [0.02, 0], [0.03, 0.01], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0, 0], [0.05, 0.02], [0, 0], [0.02, -0.05], [0.02, -0.02], [0, 0.01], [-0.01, 0.02], [-0.04, 0.01], [0, 0]], "v": [[0.94, 1.73], [0.67, 1.62], [0.65, 1.6], [0.64, 1.59], [0.62, 1.62], [0.5, 1.69], [0.3, 1.76], [0.1, 1.83], [0.05, 1.81], [0.04, 1.64], [0.26, 1.56], [0.43, 1.5], [0.5, 1.43], [0.72, 0.9], [0.93, 0.4], [0.92, 0.38], [0.88, 0.18], [1, 0.03], [1.13, 0], [1.28, 0.07], [1.34, 0.19], [1.35, 0.22], [1.46, 0.27], [1.57, 0.32], [1.57, 0.36], [1.55, 0.37], [1.45, 0.33], [1.35, 0.29], [1.34, 0.31], [1.32, 0.34], [1.31, 0.37], [1.51, 0.45], [1.79, 0.57], [1.9, 0.61], [1.93, 0.53], [1.79, 0.47], [1.66, 0.41], [1.66, 0.37], [1.67, 0.36], [1.96, 0.48], [1.97, 0.63], [1.9, 0.67], [1.83, 0.65], [1.79, 0.63], [1.73, 0.75], [1.67, 0.89], [1.65, 0.89], [1.62, 0.87], [1.67, 0.74], [1.73, 0.61], [1.69, 0.59], [1.46, 0.5], [1.27, 0.42], [1.24, 0.43], [1.15, 0.47], [1.12, 0.48], [1.03, 0.7], [0.93, 0.94], [1.08, 1], [1.13, 0.91], [1.18, 0.8], [1.23, 0.78], [1.32, 0.8], [1.39, 0.84], [1.42, 0.89], [1.38, 1.01], [1.34, 1.11], [1.42, 1.14], [1.5, 1.17], [1.54, 1.07], [1.58, 0.97], [1.63, 0.99], [1.3, 1.78], [1.25, 1.83], [0.94, 1.73]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0.04, -0.01], [0.1, -0.03], [0.02, 0], [0.02, 0.01], [-0.05, 0.05], [-0.21, 0.07], [-0.01, 0], [-0.01, 0.02], [-0.12, 0.28], [0, 0], [0, 0], [-0.02, 0.07], [-0.06, 0.03], [-0.05, 0], [-0.04, -0.05], [-0.01, -0.04], [0, 0], [-0.06, -0.02], [0, 0], [0.01, -0.01], [0, 0], [0.05, 0.02], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [-0.11, -0.04], [-0.05, -0.02], [-0.01, 0], [0.03, 0.03], [0.07, 0.03], [0, 0], [-0.01, 0.01], [0, 0], [-0.02, -0.01], [0.04, -0.05], [0.03, 0], [0.04, 0.02], [0, 0], [0.03, -0.07], [0, 0], [0.01, 0], [0, 0.01], [-0.03, 0.07], [0, 0], [0.02, 0.01], [0.1, 0.04], [0, 0], [0, 0], [0.04, -0.01], [0, 0], [0, 0], [0, 0], [-0.02, 0], [-0.03, 0.07], [0, 0.01], [-0.02, 0], [-0.05, -0.02], [-0.01, -0.01], [0, -0.02], [0.03, -0.06], [0, 0], [-0.04, -0.02], [0, 0], [-0.02, 0.05], [0, 0], [0, -0.02], [0.02, -0.04], [0.02, -0.01], [0.28, 0.11]], "o": [[-0.14, -0.06], [-0.01, 0], [0, 0], [0, 0], [-0.03, 0.03], [-0.01, 0.01], [-0.16, 0.05], [-0.02, 0], [-0.06, -0.04], [0.02, -0.01], [0.09, -0.03], [0.02, -0.01], [0.01, -0.01], [0, 0], [0, 0], [-0.04, -0.06], [0.02, -0.07], [0.04, -0.02], [0.06, 0], [0.03, 0.03], [0, 0.01], [0, 0], [0.06, 0.02], [0.01, 0.01], [-0.01, 0], [0, 0], [-0.05, -0.02], [0, 0], [0, 0.01], [-0.01, 0.01], [0, 0], [0.11, 0.04], [0.05, 0.02], [0.04, 0], [-0.01, -0.01], [-0.07, -0.03], [-0.01, -0.01], [0.01, -0.01], [0.01, 0], [0.05, 0.03], [-0.02, 0.02], [-0.02, 0], [-0.02, -0.01], [0, 0], [-0.03, 0.07], [0, 0.01], [-0.01, 0], [0, 0], [0.03, -0.07], [0, 0], [-0.02, -0.01], [0, 0], [0, 0], [-0.03, 0.02], [0, 0], [0, 0], [-0.05, 0.12], [0, 0], [0, 0], [0.02, -0.05], [0.01, -0.02], [0.02, 0], [0.03, 0.01], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0, 0], [0.05, 0.02], [0, 0], [0.02, -0.05], [0.02, -0.02], [0, 0.01], [-0.01, 0.02], [-0.04, 0.01], [0, 0]], "v": [[0.94, 1.73], [0.67, 1.62], [0.65, 1.6], [0.64, 1.59], [0.62, 1.62], [0.5, 1.69], [0.3, 1.76], [0.1, 1.83], [0.05, 1.81], [0.04, 1.64], [0.26, 1.56], [0.43, 1.5], [0.5, 1.43], [0.72, 0.9], [0.93, 0.4], [0.92, 0.38], [0.88, 0.18], [1, 0.03], [1.13, 0], [1.28, 0.07], [1.34, 0.19], [1.35, 0.22], [1.46, 0.27], [1.57, 0.32], [1.57, 0.36], [1.55, 0.37], [1.45, 0.33], [1.35, 0.29], [1.34, 0.31], [1.32, 0.34], [1.31, 0.37], [1.51, 0.45], [1.79, 0.57], [1.9, 0.61], [1.93, 0.53], [1.79, 0.47], [1.66, 0.41], [1.66, 0.37], [1.67, 0.36], [1.96, 0.48], [1.97, 0.63], [1.9, 0.67], [1.83, 0.65], [1.79, 0.63], [1.73, 0.75], [1.67, 0.89], [1.65, 0.89], [1.62, 0.87], [1.67, 0.74], [1.73, 0.61], [1.69, 0.59], [1.46, 0.5], [1.27, 0.42], [1.24, 0.43], [1.15, 0.47], [1.12, 0.48], [1.03, 0.7], [0.93, 0.94], [1.08, 1], [1.13, 0.91], [1.18, 0.8], [1.23, 0.78], [1.32, 0.8], [1.39, 0.84], [1.42, 0.89], [1.38, 1.01], [1.34, 1.11], [1.42, 1.14], [1.5, 1.17], [1.54, 1.07], [1.58, 0.97], [1.63, 0.99], [1.3, 1.78], [1.25, 1.83], [0.94, 1.73]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.08, 0.2], [0, 0], [0.19, 0.08], [0, 0], [0.06, -0.15], [0, -0.01], [-0.01, -0.01], [0, 0], [-0.02, 0.05], [0, 0], [-0.02, 0], [-0.01, -0.01], [0, -0.02], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.06, -0.14], [0, 0], [-0.15, -0.06], [0, 0], [-0.07, 0.18], [0, 0.01], [0.01, 0.01], [0, 0], [0.02, -0.05], [0.02, -0.02], [0.02, 0], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0.01, 0.01], [0.01, 0], [0, 0]], "v": [[1.24, 1.77], [1.36, 1.5], [1.47, 1.23], [1.19, 1.11], [0.91, 0.99], [0.8, 1.26], [0.69, 1.54], [0.7, 1.56], [0.83, 1.62], [0.87, 1.52], [0.92, 1.42], [0.98, 1.39], [1.14, 1.46], [1.16, 1.51], [1.12, 1.63], [1.08, 1.72], [1.22, 1.78], [1.24, 1.77]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.08, 0.2], [0, 0], [0.19, 0.08], [0, 0], [0.06, -0.15], [0, -0.01], [-0.01, -0.01], [0, 0], [-0.02, 0.05], [0, 0], [-0.02, 0], [-0.01, -0.01], [0, -0.02], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.06, -0.14], [0, 0], [-0.15, -0.06], [0, 0], [-0.07, 0.18], [0, 0.01], [0.01, 0.01], [0, 0], [0.02, -0.05], [0.02, -0.02], [0.02, 0], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0.01, 0.01], [0.01, 0], [0, 0]], "v": [[1.24, 1.77], [1.36, 1.5], [1.47, 1.23], [1.19, 1.11], [0.91, 0.99], [0.8, 1.26], [0.69, 1.54], [0.7, 1.56], [0.83, 1.62], [0.87, 1.52], [0.92, 1.42], [0.98, 1.39], [1.14, 1.46], [1.16, 1.51], [1.12, 1.63], [1.08, 1.72], [1.22, 1.78], [1.24, 1.77]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.5, 3.64], [-0.01, 0.05], [3.42, 1.43], [0.01, -0.01], [1.09, -2.62], [0, -0.17], [-0.15, -0.13], [-0.06, 0.06], [-0.39, 0.95], [-0.08, 0.08], [-0.39, -0.04], [-0.26, -0.24], [0.01, -0.33], [0.51, -1.23], [-0.01, -0.01], [-0.16, -0.01], [-0.14, 0.13]], "o": [[0.16, -0.15], [1.06, -2.57], [0.01, -0.06], [-2.78, -1.16], [-0.01, 0.01], [-1.33, 3.21], [0, 0.19], [0.19, 0.18], [0.01, -0.01], [0.39, -0.95], [0.27, -0.3], [0.41, 0.04], [0.23, 0.22], [0, 0.2], [-0.4, 0.97], [0.18, 0.13], [0.15, 0], [0, 0]], "v": [[22.34, 31.79], [24.48, 26.92], [26.44, 22.16], [21.4, 19.95], [16.33, 17.87], [14.34, 22.65], [12.36, 27.67], [12.56, 28.1], [14.99, 29.17], [15.73, 27.41], [16.6, 25.53], [17.66, 25.11], [20.44, 26.24], [20.86, 27.24], [20.13, 29.27], [19.41, 31.04], [21.9, 31.98], [22.34, 31.79]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.5, 3.64], [-0.01, 0.05], [3.42, 1.43], [0.01, -0.01], [1.09, -2.62], [0, -0.17], [-0.15, -0.13], [-0.06, 0.06], [-0.39, 0.95], [-0.08, 0.08], [-0.39, -0.04], [-0.26, -0.24], [0.01, -0.33], [0.51, -1.23], [-0.01, -0.01], [-0.16, -0.01], [-0.14, 0.13]], "o": [[0.16, -0.15], [1.06, -2.57], [0.01, -0.06], [-2.78, -1.16], [-0.01, 0.01], [-1.33, 3.21], [0, 0.19], [0.19, 0.18], [0.01, -0.01], [0.39, -0.95], [0.27, -0.3], [0.41, 0.04], [0.23, 0.22], [0, 0.2], [-0.4, 0.97], [0.18, 0.13], [0.15, 0], [0, 0]], "v": [[22.34, 31.79], [24.48, 26.92], [26.44, 22.16], [21.4, 19.95], [16.33, 17.87], [14.34, 22.65], [12.36, 27.67], [12.56, 28.1], [14.99, 29.17], [15.73, 27.41], [16.6, 25.53], [17.66, 25.11], [20.44, 26.24], [20.86, 27.24], [20.13, 29.27], [19.41, 31.04], [21.9, 31.98], [22.34, 31.79]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.08, 0.2], [0, 0], [0.19, 0.08], [0, 0], [0.06, -0.15], [0, -0.01], [-0.01, -0.01], [0, 0], [-0.02, 0.05], [0, 0], [-0.02, 0], [-0.01, -0.01], [0, -0.02], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.06, -0.14], [0, 0], [-0.15, -0.06], [0, 0], [-0.07, 0.18], [0, 0.01], [0.01, 0.01], [0, 0], [0.02, -0.05], [0.02, -0.02], [0.02, 0], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0.01, 0.01], [0.01, 0], [0, 0]], "v": [[1.24, 1.77], [1.36, 1.5], [1.47, 1.23], [1.19, 1.11], [0.91, 0.99], [0.8, 1.26], [0.69, 1.54], [0.7, 1.56], [0.83, 1.62], [0.87, 1.52], [0.92, 1.42], [0.98, 1.39], [1.14, 1.46], [1.16, 1.51], [1.12, 1.63], [1.08, 1.72], [1.22, 1.78], [1.24, 1.77]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [-0.08, 0.2], [0, 0], [0.19, 0.08], [0, 0], [0.06, -0.15], [0, -0.01], [-0.01, -0.01], [0, 0], [-0.02, 0.05], [0, 0], [-0.02, 0], [-0.01, -0.01], [0, -0.02], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.06, -0.14], [0, 0], [-0.15, -0.06], [0, 0], [-0.07, 0.18], [0, 0.01], [0.01, 0.01], [0, 0], [0.02, -0.05], [0.02, -0.02], [0.02, 0], [0.01, 0.01], [0, 0.01], [-0.02, 0.05], [0.01, 0.01], [0.01, 0], [0, 0]], "v": [[1.24, 1.77], [1.36, 1.5], [1.47, 1.23], [1.19, 1.11], [0.91, 0.99], [0.8, 1.26], [0.69, 1.54], [0.7, 1.56], [0.83, 1.62], [0.87, 1.52], [0.92, 1.42], [0.98, 1.39], [1.14, 1.46], [1.16, 1.51], [1.12, 1.63], [1.08, 1.72], [1.22, 1.78], [1.24, 1.77]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0.02], [0.01, 0], [0.09, -0.03], [0.01, -0.01], [-0.03, 0], [-0.09, 0.03]], "o": [[0.09, -0.03], [0, 0], [-0.01, -0.04], [0, 0], [-0.15, 0.05], [-0.02, 0.03], [0, 0], [0, 0]], "v": [[0.27, 1.71], [0.43, 1.66], [0.42, 1.61], [0.39, 1.58], [0.23, 1.63], [0.07, 1.7], [0.1, 1.77], [0.27, 1.71]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0.02], [0.01, 0], [0.09, -0.03], [0.01, -0.01], [-0.03, 0], [-0.09, 0.03]], "o": [[0.09, -0.03], [0, 0], [-0.01, -0.04], [0, 0], [-0.15, 0.05], [-0.02, 0.03], [0, 0], [0, 0]], "v": [[0.27, 1.71], [0.43, 1.66], [0.42, 1.61], [0.39, 1.58], [0.23, 1.63], [0.07, 1.7], [0.1, 1.77], [0.27, 1.71]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.03, 0.02], [0.14, 0.4], [0.16, -0.04], [1.53, -0.53], [0.16, -0.24], [-0.6, -0.02], [-1.57, 0.54]], "o": [[1.57, -0.54], [0.04, -0.02], [-0.25, -0.7], [-0.08, 0.02], [-2.68, 0.93], [-0.34, 0.5], [0.06, 0.01], [0, 0]], "v": [[4.81, 30.86], [7.72, 29.84], [7.52, 29.07], [7.09, 28.36], [4.15, 29.37], [1.19, 30.58], [1.83, 31.84], [4.81, 30.86]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.03, 0.02], [0.14, 0.4], [0.16, -0.04], [1.53, -0.53], [0.16, -0.24], [-0.6, -0.02], [-1.57, 0.54]], "o": [[1.57, -0.54], [0.04, -0.02], [-0.25, -0.7], [-0.08, 0.02], [-2.68, 0.93], [-0.34, 0.5], [0.06, 0.01], [0, 0]], "v": [[4.81, 30.86], [7.72, 29.84], [7.52, 29.07], [7.09, 28.36], [4.15, 29.37], [1.19, 30.58], [1.83, 31.84], [4.81, 30.86]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0.02], [0.01, 0], [0.09, -0.03], [0.01, -0.01], [-0.03, 0], [-0.09, 0.03]], "o": [[0.09, -0.03], [0, 0], [-0.01, -0.04], [0, 0], [-0.15, 0.05], [-0.02, 0.03], [0, 0], [0, 0]], "v": [[0.27, 1.71], [0.43, 1.66], [0.42, 1.61], [0.39, 1.58], [0.23, 1.63], [0.07, 1.7], [0.1, 1.77], [0.27, 1.71]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0.02], [0.01, 0], [0.09, -0.03], [0.01, -0.01], [-0.03, 0], [-0.09, 0.03]], "o": [[0.09, -0.03], [0, 0], [-0.01, -0.04], [0, 0], [-0.15, 0.05], [-0.02, 0.03], [0, 0], [0, 0]], "v": [[0.27, 1.71], [0.43, 1.66], [0.42, 1.61], [0.39, 1.58], [0.23, 1.63], [0.07, 1.7], [0.1, 1.77], [0.27, 1.71]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.03, 0.07]], "o": [[0.02, -0.05], [0, -0.01], [-0.01, 0], [-0.02, 0.05], [0, 0], [0, 0], [0, 0]], "v": [[1.06, 1.61], [1.1, 1.51], [0.97, 1.45], [0.93, 1.55], [0.89, 1.64], [1.02, 1.7], [1.06, 1.61]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.03, 0.07]], "o": [[0.02, -0.05], [0, -0.01], [-0.01, 0], [-0.02, 0.05], [0, 0], [0, 0], [0, 0]], "v": [[1.06, 1.61], [1.1, 1.51], [0.97, 1.45], [0.93, 1.55], [0.89, 1.64], [1.02, 1.7], [1.06, 1.61]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0.06], [0.22, -0.03], [0.55, -1.31], [0, -0.04], [-0.2, -0.01], [-0.49, 1.17]], "o": [[0.38, -0.91], [-0.03, -0.14], [-0.11, 0.01], [-0.38, 0.91], [0, 0.08], [0.08, 0.01], [0, 0]], "v": [[19.13, 28.92], [19.8, 27.15], [17.53, 26.16], [16.7, 27.83], [16.02, 29.55], [18.34, 30.57], [19.13, 28.92]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0.06], [0.22, -0.03], [0.55, -1.31], [0, -0.04], [-0.2, -0.01], [-0.49, 1.17]], "o": [[0.38, -0.91], [-0.03, -0.14], [-0.11, 0.01], [-0.38, 0.91], [0, 0.08], [0.08, 0.01], [0, 0]], "v": [[19.13, 28.92], [19.8, 27.15], [17.53, 26.16], [16.7, 27.83], [16.02, 29.55], [18.34, 30.57], [19.13, 28.92]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.03, 0.07]], "o": [[0.02, -0.05], [0, -0.01], [-0.01, 0], [-0.02, 0.05], [0, 0], [0, 0], [0, 0]], "v": [[1.06, 1.61], [1.1, 1.51], [0.97, 1.45], [0.93, 1.55], [0.89, 1.64], [1.02, 1.7], [1.06, 1.61]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0.03, -0.07], [0, 0], [-0.01, 0], [-0.03, 0.07]], "o": [[0.02, -0.05], [0, -0.01], [-0.01, 0], [-0.02, 0.05], [0, 0], [0, 0], [0, 0]], "v": [[1.06, 1.61], [1.1, 1.51], [0.97, 1.45], [0.93, 1.55], [0.89, 1.64], [1.02, 1.7], [1.06, 1.61]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0.03], [0, 0.01], [0.02, 0.01], [0.01, 0], [0, 0], [0, 0], [0, -0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [-0.03, 0.02]], "o": [[0.03, -0.02], [0.01, -0.02], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [-0.11, 0.28], [-0.02, 0.04], [0, 0], [0, 0], [0.01, 0.02], [0, 0.01], [0, 0]], "v": [[0.53, 1.61], [0.62, 1.52], [1.06, 0.48], [1.03, 0.46], [0.99, 0.45], [0.98, 0.44], [0.77, 0.94], [0.55, 1.45], [0.47, 1.55], [0.46, 1.55], [0.47, 1.59], [0.49, 1.63], [0.53, 1.61]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0.03], [0, 0.01], [0.02, 0.01], [0.01, 0], [0, 0], [0, 0], [0, -0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [-0.03, 0.02]], "o": [[0.03, -0.02], [0.01, -0.02], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [-0.11, 0.28], [-0.02, 0.04], [0, 0], [0, 0], [0.01, 0.02], [0, 0.01], [0, 0]], "v": [[0.53, 1.61], [0.62, 1.52], [1.06, 0.48], [1.03, 0.46], [0.99, 0.45], [0.98, 0.44], [0.77, 0.94], [0.55, 1.45], [0.47, 1.55], [0.46, 1.55], [0.47, 1.59], [0.49, 1.63], [0.53, 1.61]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.32, 0.54], [-0.01, 0.23], [0.35, 0.12], [0.13, 0.08], [0, 0], [0, 0], [0.08, -0.15], [0.57, -0.3], [0, 0], [0, 0], [-0.01, -0.05], [-0.59, 0.4]], "o": [[0.5, -0.34], [0.27, -0.44], [0.01, -0.09], [-0.26, -0.09], [0, 0], [0, 0], [-2.06, 4.96], [-0.32, 0.63], [0, 0], [0, 0], [0.13, 0.37], [0.04, 0.11], [0, 0]], "v": [[9.62, 28.96], [11.19, 27.29], [19, 8.62], [18.53, 8.33], [17.82, 8.02], [17.58, 7.87], [13.84, 16.89], [9.95, 26.18], [8.37, 27.83], [8.22, 27.91], [8.46, 28.6], [8.74, 29.37], [9.62, 28.96]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.32, 0.54], [-0.01, 0.23], [0.35, 0.12], [0.13, 0.08], [0, 0], [0, 0], [0.08, -0.15], [0.57, -0.3], [0, 0], [0, 0], [-0.01, -0.05], [-0.59, 0.4]], "o": [[0.5, -0.34], [0.27, -0.44], [0.01, -0.09], [-0.26, -0.09], [0, 0], [0, 0], [-2.06, 4.96], [-0.32, 0.63], [0, 0], [0, 0], [0.13, 0.37], [0.04, 0.11], [0, 0]], "v": [[9.62, 28.96], [11.19, 27.29], [19, 8.62], [18.53, 8.33], [17.82, 8.02], [17.58, 7.87], [13.84, 16.89], [9.95, 26.18], [8.37, 27.83], [8.22, 27.91], [8.46, 28.6], [8.74, 29.37], [9.62, 28.96]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0.03], [0, 0.01], [0.02, 0.01], [0.01, 0], [0, 0], [0, 0], [0, -0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [-0.03, 0.02]], "o": [[0.03, -0.02], [0.01, -0.02], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [-0.11, 0.28], [-0.02, 0.04], [0, 0], [0, 0], [0.01, 0.02], [0, 0.01], [0, 0]], "v": [[0.53, 1.61], [0.62, 1.52], [1.06, 0.48], [1.03, 0.46], [0.99, 0.45], [0.98, 0.44], [0.77, 0.94], [0.55, 1.45], [0.47, 1.55], [0.46, 1.55], [0.47, 1.59], [0.49, 1.63], [0.53, 1.61]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [-0.02, 0.03], [0, 0.01], [0.02, 0.01], [0.01, 0], [0, 0], [0, 0], [0, -0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [-0.03, 0.02]], "o": [[0.03, -0.02], [0.01, -0.02], [0, -0.01], [-0.01, -0.01], [0, 0], [0, 0], [-0.11, 0.28], [-0.02, 0.04], [0, 0], [0, 0], [0.01, 0.02], [0, 0.01], [0, 0]], "v": [[0.53, 1.61], [0.62, 1.52], [1.06, 0.48], [1.03, 0.46], [0.99, 0.45], [0.98, 0.44], [0.77, 0.94], [0.55, 1.45], [0.47, 1.55], [0.46, 1.55], [0.47, 1.59], [0.49, 1.63], [0.53, 1.61]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.05]], "o": [[0.03, -0.07], [0, -0.01], [0, 0], [-0.01, 0.01], [0, 0], [0, 0], [0, 0]], "v": [[1.32, 0.99], [1.36, 0.89], [1.23, 0.84], [1.22, 0.84], [1.15, 1.03], [1.28, 1.08], [1.32, 0.99]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.05]], "o": [[0.03, -0.07], [0, -0.01], [0, 0], [-0.01, 0.01], [0, 0], [0, 0], [0, 0]], "v": [[1.32, 0.99], [1.36, 0.89], [1.23, 0.84], [1.22, 0.84], [1.15, 1.03], [1.28, 1.08], [1.32, 0.99]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.04, 0.08], [0.22, 0.01], [0.04, -0.04], [-0.02, -0.02], [-0.06, 0.01], [-0.39, 0.92]], "o": [[0.49, -1.19], [-0.08, -0.12], [-0.08, 0], [-0.09, 0.15], [0.06, 0.06], [0.04, -0.01], [0, 0]], "v": [[23.78, 17.75], [24.41, 15.97], [22.22, 15.04], [22.01, 15.12], [20.62, 18.47], [23.01, 19.44], [23.78, 17.75]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.04, 0.08], [0.22, 0.01], [0.04, -0.04], [-0.02, -0.02], [-0.06, 0.01], [-0.39, 0.92]], "o": [[0.49, -1.19], [-0.08, -0.12], [-0.08, 0], [-0.09, 0.15], [0.06, 0.06], [0.04, -0.01], [0, 0]], "v": [[23.78, 17.75], [24.41, 15.97], [22.22, 15.04], [22.01, 15.12], [20.62, 18.47], [23.01, 19.44], [23.78, 17.75]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.05]], "o": [[0.03, -0.07], [0, -0.01], [0, 0], [-0.01, 0.01], [0, 0], [0, 0], [0, 0]], "v": [[1.32, 0.99], [1.36, 0.89], [1.23, 0.84], [1.22, 0.84], [1.15, 1.03], [1.28, 1.08], [1.32, 0.99]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.05]], "o": [[0.03, -0.07], [0, -0.01], [0, 0], [-0.01, 0.01], [0, 0], [0, 0], [0, 0]], "v": [[1.32, 0.99], [1.36, 0.89], [1.23, 0.84], [1.22, 0.84], [1.15, 1.03], [1.28, 1.08], [1.32, 0.99]]}], "t": 301}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.08], [0.07, 0.03], [0.05, -0.03], [-0.02, -0.07], [-0.02, -0.03], [-0.07, 0.02]], "o": [[0.08, -0.03], [0, -0.07], [-0.05, -0.03], [-0.07, 0.04], [0.01, 0.03], [0.04, 0.06], [0, 0]], "v": [[1.16, 0.41], [1.29, 0.24], [1.18, 0.07], [1.01, 0.09], [0.94, 0.28], [0.97, 0.35], [1.16, 0.41]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.08], [0.07, 0.03], [0.05, -0.03], [-0.02, -0.07], [-0.02, -0.03], [-0.07, 0.02]], "o": [[0.08, -0.03], [0, -0.07], [-0.05, -0.03], [-0.07, 0.04], [0.01, 0.03], [0.04, 0.06], [0, 0]], "v": [[1.16, 0.41], [1.29, 0.24], [1.18, 0.07], [1.01, 0.09], [0.94, 0.28], [0.97, 0.35], [1.16, 0.41]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.46], [1.2, 0.57], [0.9, -0.57], [-0.35, -1.35], [-0.36, -0.47], [-1.22, 0.41]], "o": [[1.38, -0.46], [0, -1.31], [-0.96, -0.46], [-1.2, 0.76], [0.13, 0.53], [0.77, 1.02], [0, 0]], "v": [[20.96, 7.37], [23.19, 4.27], [21.32, 1.35], [18.25, 1.53], [16.83, 5.08], [17.44, 6.3], [20.96, 7.37]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.46], [1.2, 0.57], [0.9, -0.57], [-0.35, -1.35], [-0.36, -0.47], [-1.22, 0.41]], "o": [[1.38, -0.46], [0, -1.31], [-0.96, -0.46], [-1.2, 0.76], [0.13, 0.53], [0.77, 1.02], [0, 0]], "v": [[20.96, 7.37], [23.19, 4.27], [21.32, 1.35], [18.25, 1.53], [16.83, 5.08], [17.44, 6.3], [20.96, 7.37]]}], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.08], [0.07, 0.03], [0.05, -0.03], [-0.02, -0.07], [-0.02, -0.03], [-0.07, 0.02]], "o": [[0.08, -0.03], [0, -0.07], [-0.05, -0.03], [-0.07, 0.04], [0.01, 0.03], [0.04, 0.06], [0, 0]], "v": [[1.16, 0.41], [1.29, 0.24], [1.18, 0.07], [1.01, 0.09], [0.94, 0.28], [0.97, 0.35], [1.16, 0.41]]}], "t": 253}, {"s": [{"c": true, "i": [[0, 0], [0, 0.08], [0.07, 0.03], [0.05, -0.03], [-0.02, -0.07], [-0.02, -0.03], [-0.07, 0.02]], "o": [[0.08, -0.03], [0, -0.07], [-0.05, -0.03], [-0.07, 0.04], [0.01, 0.03], [0.04, 0.06], [0, 0]], "v": [[1.16, 0.41], [1.29, 0.24], [1.18, 0.07], [1.01, 0.09], [0.94, 0.28], [0.97, 0.35], [1.16, 0.41]]}], "t": 301}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 253}, {"s": [0.3157, 0.4562, 0.9542], "t": 301}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 192}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 253}, {"s": [100], "t": 301}]}}], "ind": 2}]}]}