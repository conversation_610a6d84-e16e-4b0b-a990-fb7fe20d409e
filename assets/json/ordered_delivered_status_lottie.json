{"nm": "Flow 13", "ddd": 0, "h": 60, "w": 60, "meta": {"g": "LottieFiles Figma v57"}, "layers": [{"ty": 0, "nm": "Frame 14102", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": true, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "masksProperties": [{"nm": "", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}], "w": 60, "h": 60, "refId": "1", "ind": 1}, {"ty": 4, "nm": "Ellipse 60", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 2}], "v": "5.7.0", "fr": 60, "op": 217.19, "ip": 0, "assets": [{"nm": "[Asset] Frame 14102", "id": "1", "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 0.75], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 0.75], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.65, 13.44], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.65, 13.44], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 0.75], "t": 217}, {"s": [0.98, 0.75], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.65, 1.08], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.65, 1.08], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.65, 19.44], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.65, 19.44], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.65, 1.08], "t": 217}, {"s": [0.65, 1.08], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.03, 0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0.05, -0.05], [0.04, -0.02], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.05, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, 0.05], [0, 0], [0, 0], [0, 0.04], [0, 0], [-0.05, -0.01], [0, 0], [-0.04, 0], [-0.07, 0.05], [0, 0], [0.03, 0.06]], "o": [[-0.01, -0.03], [-0.03, -0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.03, 0.04], [0, 0], [0, -0.05], [0, 0], [0, 0], [0, -0.05], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, 0.02], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.04, 0], [0, 0], [0.05, -0.03], [0, 0], [0.04, 0.01], [0.08, 0], [0, 0], [0.05, -0.04], [0, 0]], "v": [[1.95, 0.82], [1.87, 0.75], [1.77, 0.76], [1.75, 0.77], [1.75, 0.25], [1.82, 0.25], [1.85, 0.22], [1.85, 0.03], [1.82, 0], [0.82, 0], [0.8, 0.03], [0.8, 0.22], [0.82, 0.25], [0.89, 0.25], [0.89, 0.67], [0.78, 0.64], [0.58, 0.7], [0.47, 0.78], [0.47, 0.78], [0.38, 0.69], [0.33, 0.69], [0.33, 0.69], [0.25, 0.6], [0.03, 0.6], [0, 0.63], [0.03, 0.66], [0.25, 0.66], [0.28, 0.69], [0.28, 0.72], [0.28, 1.38], [0.28, 1.41], [0.25, 1.44], [0.03, 1.44], [0, 1.47], [0.03, 1.49], [0.25, 1.49], [0.33, 1.41], [0.33, 1.4], [0.38, 1.4], [0.47, 1.33], [0.56, 1.27], [0.71, 1.25], [1.08, 1.34], [1.19, 1.35], [1.42, 1.28], [1.9, 0.99], [1.95, 0.82]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.03, 0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0.05, -0.05], [0.04, -0.02], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.05, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, 0.05], [0, 0], [0, 0], [0, 0.04], [0, 0], [-0.05, -0.01], [0, 0], [-0.04, 0], [-0.07, 0.05], [0, 0], [0.03, 0.06]], "o": [[-0.01, -0.03], [-0.03, -0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.03, 0.04], [0, 0], [0, -0.05], [0, 0], [0, 0], [0, -0.05], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, 0.02], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.04, 0], [0, 0], [0.05, -0.03], [0, 0], [0.04, 0.01], [0.08, 0], [0, 0], [0.05, -0.04], [0, 0]], "v": [[1.95, 0.82], [1.87, 0.75], [1.77, 0.76], [1.75, 0.77], [1.75, 0.25], [1.82, 0.25], [1.85, 0.22], [1.85, 0.03], [1.82, 0], [0.82, 0], [0.8, 0.03], [0.8, 0.22], [0.82, 0.25], [0.89, 0.25], [0.89, 0.67], [0.78, 0.64], [0.58, 0.7], [0.47, 0.78], [0.47, 0.78], [0.38, 0.69], [0.33, 0.69], [0.33, 0.69], [0.25, 0.6], [0.03, 0.6], [0, 0.63], [0.03, 0.66], [0.25, 0.66], [0.28, 0.69], [0.28, 0.72], [0.28, 1.38], [0.28, 1.41], [0.25, 1.44], [0.03, 1.44], [0, 1.47], [0.03, 1.49], [0.25, 1.49], [0.33, 1.41], [0.33, 1.4], [0.38, 1.4], [0.47, 1.33], [0.56, 1.27], [0.71, 1.25], [1.08, 1.34], [1.19, 1.35], [1.42, 1.28], [1.9, 0.99], [1.95, 0.82]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.6, 0.21], [0.56, -0.28], [0, 0], [0, 0], [0, 0], [0, 0.26], [0, 0], [0.26, 0], [0, 0], [0, -0.26], [0, 0], [-0.26, 0], [0, 0], [0, 0], [0, 0], [0.9, -0.97], [0.78, -0.35], [0, 0], [0.83, 0], [0, 0], [0, 0], [0.83, 0], [0, 0], [0, -0.26], [-0.26, 0], [0, 0], [0, -0.31], [0, 0], [0, 0], [0, 0], [0.31, 0], [0, 0], [0, -0.26], [-0.26, 0], [0, 0], [0, 0.83], [0, 0], [0, 0], [-0.06, 0.78], [0, 0], [-0.96, -0.24], [0, 0], [-0.64, 0], [-1.29, 0.81], [0, 0], [0.48, 1.06]], "o": [[-0.26, -0.58], [-0.6, -0.21], [0, 0], [0, 0], [0, 0], [0.26, 0], [0, 0], [0, -0.26], [0, 0], [-0.26, 0], [0, 0], [0, 0.26], [0, 0], [0, 0], [0, 0], [-1.29, -0.37], [-0.6, 0.65], [0, 0], [0, -0.83], [0, 0], [0, 0], [0, -0.83], [0, 0], [-0.26, 0], [0, 0.26], [0, 0], [0.31, 0], [0, 0], [0, 0], [0, 0], [0, 0.31], [0, 0], [-0.26, 0], [0, 0.26], [0, 0], [0.83, 0], [0, 0], [0, 0], [0.79, 0], [0, 0], [0.85, -0.51], [0, 0], [0.64, 0.16], [1.51, 0], [0, 0], [0.98, -0.63], [0, 0]], "v": [[35.09, 14.79], [33.75, 13.56], [31.94, 13.66], [31.59, 13.82], [31.59, 4.48], [32.75, 4.48], [33.22, 4.01], [33.22, 0.47], [32.75, 0], [14.83, 0], [14.35, 0.47], [14.35, 4.02], [14.83, 4.49], [16.05, 4.49], [16.05, 12.13], [14.06, 11.55], [10.51, 12.53], [8.44, 14.01], [8.44, 13.99], [6.92, 12.48], [5.94, 12.48], [5.94, 12.39], [4.43, 10.88], [0.47, 10.88], [0, 11.35], [0.47, 11.82], [4.43, 11.82], [5, 12.4], [5, 12.95], [5, 24.81], [5, 25.36], [4.43, 25.94], [0.47, 25.94], [0, 26.41], [0.47, 26.88], [4.43, 26.88], [5.94, 25.37], [5.94, 25.28], [6.92, 25.28], [8.42, 23.88], [10.06, 22.9], [12.86, 22.49], [19.42, 24.13], [21.34, 24.36], [25.63, 23.12], [34.23, 17.75], [35.09, 14.79]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.6, 0.21], [0.56, -0.28], [0, 0], [0, 0], [0, 0], [0, 0.26], [0, 0], [0.26, 0], [0, 0], [0, -0.26], [0, 0], [-0.26, 0], [0, 0], [0, 0], [0, 0], [0.9, -0.97], [0.78, -0.35], [0, 0], [0.83, 0], [0, 0], [0, 0], [0.83, 0], [0, 0], [0, -0.26], [-0.26, 0], [0, 0], [0, -0.31], [0, 0], [0, 0], [0, 0], [0.31, 0], [0, 0], [0, -0.26], [-0.26, 0], [0, 0], [0, 0.83], [0, 0], [0, 0], [-0.06, 0.78], [0, 0], [-0.96, -0.24], [0, 0], [-0.64, 0], [-1.29, 0.81], [0, 0], [0.48, 1.06]], "o": [[-0.26, -0.58], [-0.6, -0.21], [0, 0], [0, 0], [0, 0], [0.26, 0], [0, 0], [0, -0.26], [0, 0], [-0.26, 0], [0, 0], [0, 0.26], [0, 0], [0, 0], [0, 0], [-1.29, -0.37], [-0.6, 0.65], [0, 0], [0, -0.83], [0, 0], [0, 0], [0, -0.83], [0, 0], [-0.26, 0], [0, 0.26], [0, 0], [0.31, 0], [0, 0], [0, 0], [0, 0], [0, 0.31], [0, 0], [-0.26, 0], [0, 0.26], [0, 0], [0.83, 0], [0, 0], [0, 0], [0.79, 0], [0, 0], [0.85, -0.51], [0, 0], [0.64, 0.16], [1.51, 0], [0, 0], [0.98, -0.63], [0, 0]], "v": [[35.09, 14.79], [33.75, 13.56], [31.94, 13.66], [31.59, 13.82], [31.59, 4.48], [32.75, 4.48], [33.22, 4.01], [33.22, 0.47], [32.75, 0], [14.83, 0], [14.35, 0.47], [14.35, 4.02], [14.83, 4.49], [16.05, 4.49], [16.05, 12.13], [14.06, 11.55], [10.51, 12.53], [8.44, 14.01], [8.44, 13.99], [6.92, 12.48], [5.94, 12.48], [5.94, 12.39], [4.43, 10.88], [0.47, 10.88], [0, 11.35], [0.47, 11.82], [4.43, 11.82], [5, 12.4], [5, 12.95], [5, 24.81], [5, 25.36], [4.43, 25.94], [0.47, 25.94], [0, 26.41], [0.47, 26.88], [4.43, 26.88], [5.94, 25.37], [5.94, 25.28], [6.92, 25.28], [8.42, 23.88], [10.06, 22.9], [12.86, 22.49], [19.42, 24.13], [21.34, 24.36], [25.63, 23.12], [34.23, 17.75], [35.09, 14.79]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.03, 0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0.05, -0.05], [0.04, -0.02], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.05, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, 0.05], [0, 0], [0, 0], [0, 0.04], [0, 0], [-0.05, -0.01], [0, 0], [-0.04, 0], [-0.07, 0.05], [0, 0], [0.03, 0.06]], "o": [[-0.01, -0.03], [-0.03, -0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.03, 0.04], [0, 0], [0, -0.05], [0, 0], [0, 0], [0, -0.05], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, 0.02], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.04, 0], [0, 0], [0.05, -0.03], [0, 0], [0.04, 0.01], [0.08, 0], [0, 0], [0.05, -0.04], [0, 0]], "v": [[1.95, 0.82], [1.87, 0.75], [1.77, 0.76], [1.75, 0.77], [1.75, 0.25], [1.82, 0.25], [1.85, 0.22], [1.85, 0.03], [1.82, 0], [0.82, 0], [0.8, 0.03], [0.8, 0.22], [0.82, 0.25], [0.89, 0.25], [0.89, 0.67], [0.78, 0.64], [0.58, 0.7], [0.47, 0.78], [0.47, 0.78], [0.38, 0.69], [0.33, 0.69], [0.33, 0.69], [0.25, 0.6], [0.03, 0.6], [0, 0.63], [0.03, 0.66], [0.25, 0.66], [0.28, 0.69], [0.28, 0.72], [0.28, 1.38], [0.28, 1.41], [0.25, 1.44], [0.03, 1.44], [0, 1.47], [0.03, 1.49], [0.25, 1.49], [0.33, 1.41], [0.33, 1.4], [0.38, 1.4], [0.47, 1.33], [0.56, 1.27], [0.71, 1.25], [1.08, 1.34], [1.19, 1.35], [1.42, 1.28], [1.9, 0.99], [1.95, 0.82]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0.03, 0.01], [0.03, -0.02], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0.05, -0.05], [0.04, -0.02], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.05, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [0, 0.05], [0, 0], [0, 0], [0, 0.04], [0, 0], [-0.05, -0.01], [0, 0], [-0.04, 0], [-0.07, 0.05], [0, 0], [0.03, 0.06]], "o": [[-0.01, -0.03], [-0.03, -0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, -0.01], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [-0.07, -0.02], [-0.03, 0.04], [0, 0], [0, -0.05], [0, 0], [0, 0], [0, -0.05], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, 0.02], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.05, 0], [0, 0], [0, 0], [0.04, 0], [0, 0], [0.05, -0.03], [0, 0], [0.04, 0.01], [0.08, 0], [0, 0], [0.05, -0.04], [0, 0]], "v": [[1.95, 0.82], [1.87, 0.75], [1.77, 0.76], [1.75, 0.77], [1.75, 0.25], [1.82, 0.25], [1.85, 0.22], [1.85, 0.03], [1.82, 0], [0.82, 0], [0.8, 0.03], [0.8, 0.22], [0.82, 0.25], [0.89, 0.25], [0.89, 0.67], [0.78, 0.64], [0.58, 0.7], [0.47, 0.78], [0.47, 0.78], [0.38, 0.69], [0.33, 0.69], [0.33, 0.69], [0.25, 0.6], [0.03, 0.6], [0, 0.63], [0.03, 0.66], [0.25, 0.66], [0.28, 0.69], [0.28, 0.72], [0.28, 1.38], [0.28, 1.41], [0.25, 1.44], [0.03, 1.44], [0, 1.47], [0.03, 1.49], [0.25, 1.49], [0.33, 1.41], [0.33, 1.4], [0.38, 1.4], [0.47, 1.33], [0.56, 1.27], [0.71, 1.25], [1.08, 1.34], [1.19, 1.35], [1.42, 1.28], [1.9, 0.99], [1.95, 0.82]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.79, 0.2], [1.46, 0.2], [1.46, 0.05], [1.79, 0.05], [1.79, 0.2]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.79, 0.2], [1.46, 0.2], [1.46, 0.05], [1.79, 0.05], [1.79, 0.2]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.29, 3.55], [26.32, 3.55], [26.32, 0.94], [32.29, 0.94], [32.29, 3.55]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.29, 3.55], [26.32, 3.55], [26.32, 0.94], [32.29, 0.94], [32.29, 3.55]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.79, 0.2], [1.46, 0.2], [1.46, 0.05], [1.79, 0.05], [1.79, 0.2]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.79, 0.2], [1.46, 0.2], [1.46, 0.05], [1.79, 0.05], [1.79, 0.2]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.41, 0.22], [1.41, 0.32], [1.34, 0.27], [1.32, 0.26], [1.31, 0.27], [1.23, 0.32], [1.23, 0.22], [1.23, 0.05], [1.41, 0.05], [1.41, 0.22]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.41, 0.22], [1.41, 0.32], [1.34, 0.27], [1.32, 0.26], [1.31, 0.27], [1.23, 0.32], [1.23, 0.22], [1.23, 0.05], [1.41, 0.05], [1.41, 0.22]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.1, 0], [0.08, -0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.08, -0.06], [-0.1, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.39, 4.02], [25.39, 5.77], [24.06, 4.84], [23.79, 4.76], [23.52, 4.84], [22.2, 5.77], [22.2, 4.02], [22.2, 0.94], [25.39, 0.94], [25.39, 4.02]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.1, 0], [0.08, -0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.08, -0.06], [-0.1, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.39, 4.02], [25.39, 5.77], [24.06, 4.84], [23.79, 4.76], [23.52, 4.84], [22.2, 5.77], [22.2, 4.02], [22.2, 0.94], [25.39, 0.94], [25.39, 4.02]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.41, 0.22], [1.41, 0.32], [1.34, 0.27], [1.32, 0.26], [1.31, 0.27], [1.23, 0.32], [1.23, 0.22], [1.23, 0.05], [1.41, 0.05], [1.41, 0.22]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.41, 0.22], [1.41, 0.32], [1.34, 0.27], [1.32, 0.26], [1.31, 0.27], [1.23, 0.32], [1.23, 0.22], [1.23, 0.05], [1.41, 0.05], [1.41, 0.22]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.85, 0.05], [1.18, 0.05], [1.18, 0.2], [0.85, 0.2], [0.85, 0.05]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.85, 0.05], [1.18, 0.05], [1.18, 0.2], [0.85, 0.2], [0.85, 0.05]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[15.3, 0.94], [21.26, 0.94], [21.26, 3.55], [15.3, 3.55], [15.3, 0.94]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[15.3, 0.94], [21.26, 0.94], [21.26, 3.55], [15.3, 3.55], [15.3, 0.94]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.85, 0.05], [1.18, 0.05], [1.18, 0.2], [0.85, 0.2], [0.85, 0.05]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.85, 0.05], [1.18, 0.05], [1.18, 0.2], [0.85, 0.2], [0.85, 0.05]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, 0.03], [0.03, 0.01], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.01], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.03], [-0.02, -0.03], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.94, 0.25], [1.18, 0.25], [1.18, 0.37], [1.19, 0.39], [1.22, 0.39], [1.32, 0.32], [1.42, 0.39], [1.44, 0.4], [1.45, 0.39], [1.46, 0.37], [1.46, 0.25], [1.7, 0.25], [1.7, 0.79], [1.38, 0.94], [1.37, 0.85], [1.29, 0.79], [0.94, 0.69], [0.94, 0.25], [0.94, 0.25]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, 0.03], [0.03, 0.01], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.01], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.03], [-0.02, -0.03], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.94, 0.25], [1.18, 0.25], [1.18, 0.37], [1.19, 0.39], [1.22, 0.39], [1.32, 0.32], [1.42, 0.39], [1.44, 0.4], [1.45, 0.39], [1.46, 0.37], [1.46, 0.25], [1.7, 0.25], [1.7, 0.79], [1.38, 0.94], [1.37, 0.85], [1.29, 0.79], [0.94, 0.69], [0.94, 0.25], [0.94, 0.25]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.16, -0.08], [-0.15, 0.1], [0, 0], [0, 0], [-0.1, 0], [-0.07, 0.03], [0, 0.17], [0, 0], [0, 0], [0, 0], [0, 0], [0.28, 0.49], [0.6, 0.17], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.17], [0.16, 0.08], [0, 0], [0, 0], [0.08, 0.06], [0.07, 0], [0.16, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, -0.55], [-0.3, -0.55], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[16.99, 4.48], [21.25, 4.48], [21.25, 6.67], [21.51, 7.09], [22, 7.05], [23.79, 5.8], [25.59, 7.05], [25.86, 7.14], [26.08, 7.09], [26.33, 6.67], [26.33, 4.48], [30.66, 4.48], [30.66, 14.25], [24.84, 16.91], [24.61, 15.3], [23.22, 14.18], [17, 12.4], [17, 4.48], [16.99, 4.48]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.16, -0.08], [-0.15, 0.1], [0, 0], [0, 0], [-0.1, 0], [-0.07, 0.03], [0, 0.17], [0, 0], [0, 0], [0, 0], [0, 0], [0.28, 0.49], [0.6, 0.17], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.17], [0.16, 0.08], [0, 0], [0, 0], [0.08, 0.06], [0.07, 0], [0.16, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, -0.55], [-0.3, -0.55], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[16.99, 4.48], [21.25, 4.48], [21.25, 6.67], [21.51, 7.09], [22, 7.05], [23.79, 5.8], [25.59, 7.05], [25.86, 7.14], [26.08, 7.09], [26.33, 6.67], [26.33, 4.48], [30.66, 4.48], [30.66, 14.25], [24.84, 16.91], [24.61, 15.3], [23.22, 14.18], [17, 12.4], [17, 4.48], [16.99, 4.48]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, 0.03], [0.03, 0.01], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.01], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.03], [-0.02, -0.03], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.94, 0.25], [1.18, 0.25], [1.18, 0.37], [1.19, 0.39], [1.22, 0.39], [1.32, 0.32], [1.42, 0.39], [1.44, 0.4], [1.45, 0.39], [1.46, 0.37], [1.46, 0.25], [1.7, 0.25], [1.7, 0.79], [1.38, 0.94], [1.37, 0.85], [1.29, 0.79], [0.94, 0.69], [0.94, 0.25], [0.94, 0.25]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, 0.03], [0.03, 0.01], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.01], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.03], [-0.02, -0.03], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.94, 0.25], [1.18, 0.25], [1.18, 0.37], [1.19, 0.39], [1.22, 0.39], [1.32, 0.32], [1.42, 0.39], [1.44, 0.4], [1.45, 0.39], [1.46, 0.37], [1.46, 0.25], [1.7, 0.25], [1.7, 0.79], [1.38, 0.94], [1.37, 0.85], [1.29, 0.79], [0.94, 0.69], [0.94, 0.25], [0.94, 0.25]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, -0.02], [0, 0]], "o": [[0, 0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, 0]], "v": [[0.42, 1.32], [0.38, 1.35], [0.33, 1.35], [0.33, 0.75], [0.38, 0.75], [0.42, 0.78], [0.42, 1.32]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, -0.02], [0, 0]], "o": [[0, 0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, 0]], "v": [[0.42, 1.32], [0.38, 1.35], [0.33, 1.35], [0.33, 0.75], [0.38, 0.75], [0.42, 0.78], [0.42, 1.32]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.31, 0], [0, 0], [0, 0], [0, 0], [0, -0.31], [0, 0]], "o": [[0, 0.31], [0, 0], [0, 0], [0, 0], [0.31, 0], [0, 0], [0, 0]], "v": [[7.5, 23.77], [6.92, 24.34], [5.94, 24.34], [5.94, 13.42], [6.92, 13.42], [7.5, 13.99], [7.5, 23.77]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.31, 0], [0, 0], [0, 0], [0, 0], [0, -0.31], [0, 0]], "o": [[0, 0.31], [0, 0], [0, 0], [0, 0], [0.31, 0], [0, 0], [0, 0]], "v": [[7.5, 23.77], [6.92, 24.34], [5.94, 24.34], [5.94, 13.42], [6.92, 13.42], [7.5, 13.99], [7.5, 23.77]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, -0.02], [0, 0]], "o": [[0, 0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, 0]], "v": [[0.42, 1.32], [0.38, 1.35], [0.33, 1.35], [0.33, 0.75], [0.38, 0.75], [0.42, 0.78], [0.42, 1.32]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0.02, 0], [0, 0], [0, 0], [0, 0], [0, -0.02], [0, 0]], "o": [[0, 0.02], [0, 0], [0, 0], [0, 0], [0.02, 0], [0, 0], [0, 0]], "v": [[0.42, 1.32], [0.38, 1.35], [0.33, 1.35], [0.33, 0.75], [0.38, 0.75], [0.42, 0.78], [0.42, 1.32]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.1, 0.03], [0, 0], [0.06, -0.04], [0, 0], [0, 0], [-0.04, 0.05], [-0.05, -0.01], [0, 0], [-0.01, -0.02], [0.01, -0.02], [0.02, -0.01], [0.02, 0.01], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.01, 0], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.01], [-0.01, -0.02], [0.03, -0.02]], "o": [[0, 0], [-0.09, 0.06], [0, 0], [-0.07, -0.02], [0, 0], [0, 0], [0.06, -0.02], [0.04, -0.04], [0, 0], [0.02, 0.01], [0.01, 0.02], [-0.01, 0.02], [-0.02, 0.01], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.01, 0], [0.02, 0], [0, 0], [0, 0], [0.02, -0.01], [0.02, 0.01], [0.02, 0.04], [0, 0]], "v": [[1.87, 0.94], [1.4, 1.24], [1.09, 1.29], [0.73, 1.2], [0.53, 1.23], [0.47, 1.27], [0.47, 0.83], [0.62, 0.73], [0.77, 0.69], [1.28, 0.84], [1.32, 0.88], [1.33, 0.93], [1.29, 0.98], [1.23, 0.99], [0.95, 0.91], [0.92, 0.92], [0.94, 0.96], [1.22, 1.04], [1.25, 1.04], [1.31, 1.03], [1.31, 1.03], [1.8, 0.81], [1.86, 0.8], [1.9, 0.84], [1.87, 0.94]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.1, 0.03], [0, 0], [0.06, -0.04], [0, 0], [0, 0], [-0.04, 0.05], [-0.05, -0.01], [0, 0], [-0.01, -0.02], [0.01, -0.02], [0.02, -0.01], [0.02, 0.01], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.01, 0], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.01], [-0.01, -0.02], [0.03, -0.02]], "o": [[0, 0], [-0.09, 0.06], [0, 0], [-0.07, -0.02], [0, 0], [0, 0], [0.06, -0.02], [0.04, -0.04], [0, 0], [0.02, 0.01], [0.01, 0.02], [-0.01, 0.02], [-0.02, 0.01], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.01, 0], [0.02, 0], [0, 0], [0, 0], [0.02, -0.01], [0.02, 0.01], [0.02, 0.04], [0, 0]], "v": [[1.87, 0.94], [1.4, 1.24], [1.09, 1.29], [0.73, 1.2], [0.53, 1.23], [0.47, 1.27], [0.47, 0.83], [0.62, 0.73], [0.77, 0.69], [1.28, 0.84], [1.32, 0.88], [1.33, 0.93], [1.29, 0.98], [1.23, 0.99], [0.95, 0.91], [0.92, 0.92], [0.94, 0.96], [1.22, 1.04], [1.25, 1.04], [1.31, 1.03], [1.31, 1.03], [1.8, 0.81], [1.86, 0.8], [1.9, 0.84], [1.87, 0.94]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [1.86, 0.46], [0, 0], [1.06, -0.64], [0, 0], [0, 0], [-0.78, 0.84], [-0.94, -0.27], [0, 0], [-0.18, -0.33], [0.11, -0.35], [0.33, -0.18], [0.35, 0.1], [0, 0], [0.07, -0.25], [-0.25, -0.07], [0, 0], [-0.21, 0], [-0.33, 0.17], [0, 0], [0, 0], [-0.36, -0.12], [-0.16, -0.34], [0.58, -0.37]], "o": [[0, 0], [-1.62, 1.03], [0, 0], [-1.2, -0.3], [0, 0], [0, 0], [1.06, -0.39], [0.66, -0.71], [0, 0], [0.35, 0.1], [0.18, 0.33], [-0.1, 0.35], [-0.33, 0.18], [0, 0], [-0.25, -0.07], [-0.07, 0.25], [0, 0], [0.21, 0.06], [0.36, 0], [0, 0], [0, 0], [0.34, -0.17], [0.36, 0.13], [0.3, 0.64], [0, 0]], "v": [[33.72, 16.94], [25.13, 22.32], [19.65, 23.21], [13.09, 21.57], [9.58, 22.09], [8.44, 22.78], [8.44, 15.02], [11.21, 13.17], [13.81, 12.46], [22.95, 15.09], [23.78, 15.75], [23.9, 16.81], [23.24, 17.64], [22.18, 17.76], [17.1, 16.3], [16.52, 16.62], [16.84, 17.2], [21.92, 18.66], [22.57, 18.75], [23.6, 18.5], [23.61, 18.5], [32.34, 14.5], [33.43, 14.44], [34.23, 15.17], [33.72, 16.94]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [1.86, 0.46], [0, 0], [1.06, -0.64], [0, 0], [0, 0], [-0.78, 0.84], [-0.94, -0.27], [0, 0], [-0.18, -0.33], [0.11, -0.35], [0.33, -0.18], [0.35, 0.1], [0, 0], [0.07, -0.25], [-0.25, -0.07], [0, 0], [-0.21, 0], [-0.33, 0.17], [0, 0], [0, 0], [-0.36, -0.12], [-0.16, -0.34], [0.58, -0.37]], "o": [[0, 0], [-1.62, 1.03], [0, 0], [-1.2, -0.3], [0, 0], [0, 0], [1.06, -0.39], [0.66, -0.71], [0, 0], [0.35, 0.1], [0.18, 0.33], [-0.1, 0.35], [-0.33, 0.18], [0, 0], [-0.25, -0.07], [-0.07, 0.25], [0, 0], [0.21, 0.06], [0.36, 0], [0, 0], [0, 0], [0.34, -0.17], [0.36, 0.13], [0.3, 0.64], [0, 0]], "v": [[33.72, 16.94], [25.13, 22.32], [19.65, 23.21], [13.09, 21.57], [9.58, 22.09], [8.44, 22.78], [8.44, 15.02], [11.21, 13.17], [13.81, 12.46], [22.95, 15.09], [23.78, 15.75], [23.9, 16.81], [23.24, 17.64], [22.18, 17.76], [17.1, 16.3], [16.52, 16.62], [16.84, 17.2], [21.92, 18.66], [22.57, 18.75], [23.6, 18.5], [23.61, 18.5], [32.34, 14.5], [33.43, 14.44], [34.23, 15.17], [33.72, 16.94]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.1, 0.03], [0, 0], [0.06, -0.04], [0, 0], [0, 0], [-0.04, 0.05], [-0.05, -0.01], [0, 0], [-0.01, -0.02], [0.01, -0.02], [0.02, -0.01], [0.02, 0.01], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.01, 0], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.01], [-0.01, -0.02], [0.03, -0.02]], "o": [[0, 0], [-0.09, 0.06], [0, 0], [-0.07, -0.02], [0, 0], [0, 0], [0.06, -0.02], [0.04, -0.04], [0, 0], [0.02, 0.01], [0.01, 0.02], [-0.01, 0.02], [-0.02, 0.01], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.01, 0], [0.02, 0], [0, 0], [0, 0], [0.02, -0.01], [0.02, 0.01], [0.02, 0.04], [0, 0]], "v": [[1.87, 0.94], [1.4, 1.24], [1.09, 1.29], [0.73, 1.2], [0.53, 1.23], [0.47, 1.27], [0.47, 0.83], [0.62, 0.73], [0.77, 0.69], [1.28, 0.84], [1.32, 0.88], [1.33, 0.93], [1.29, 0.98], [1.23, 0.99], [0.95, 0.91], [0.92, 0.92], [0.94, 0.96], [1.22, 1.04], [1.25, 1.04], [1.31, 1.03], [1.31, 1.03], [1.8, 0.81], [1.86, 0.8], [1.9, 0.84], [1.87, 0.94]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.1, 0.03], [0, 0], [0.06, -0.04], [0, 0], [0, 0], [-0.04, 0.05], [-0.05, -0.01], [0, 0], [-0.01, -0.02], [0.01, -0.02], [0.02, -0.01], [0.02, 0.01], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.01, 0], [-0.02, 0.01], [0, 0], [0, 0], [-0.02, -0.01], [-0.01, -0.02], [0.03, -0.02]], "o": [[0, 0], [-0.09, 0.06], [0, 0], [-0.07, -0.02], [0, 0], [0, 0], [0.06, -0.02], [0.04, -0.04], [0, 0], [0.02, 0.01], [0.01, 0.02], [-0.01, 0.02], [-0.02, 0.01], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.01, 0], [0.02, 0], [0, 0], [0, 0], [0.02, -0.01], [0.02, 0.01], [0.02, 0.04], [0, 0]], "v": [[1.87, 0.94], [1.4, 1.24], [1.09, 1.29], [0.73, 1.2], [0.53, 1.23], [0.47, 1.27], [0.47, 0.83], [0.62, 0.73], [0.77, 0.69], [1.28, 0.84], [1.32, 0.88], [1.33, 0.93], [1.29, 0.98], [1.23, 0.99], [0.95, 0.91], [0.92, 0.92], [0.94, 0.96], [1.22, 1.04], [1.25, 1.04], [1.31, 1.03], [1.31, 1.03], [1.8, 0.81], [1.86, 0.8], [1.9, 0.84], [1.87, 0.94]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 1}, {"ty": 4, "nm": "Frame 14102 Bg", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}], "ind": 2}]}]}