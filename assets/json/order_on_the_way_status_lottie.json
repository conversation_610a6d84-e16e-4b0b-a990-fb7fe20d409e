{"nm": "Flow 12", "ddd": 0, "h": 60, "w": 60, "meta": {"g": "LottieFiles Figma v57"}, "layers": [{"ty": 0, "nm": "reshot-icon-pizza-delivery-NE62BC7Z4J 1", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": true, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "masksProperties": [{"nm": "", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}], "w": 60, "h": 60, "refId": "1", "ind": 1}, {"ty": 4, "nm": "Ellipse 60", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 2}], "v": "5.7.0", "fr": 60, "op": 217.19, "ip": 0, "assets": [{"nm": "[Asset] reshot-icon-pizza-delivery-NE62BC7Z4J 1", "id": "1", "layers": [{"ty": 0, "nm": "_x39_", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 217}, {"s": [1, 0.55], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "w": 60, "h": 60, "refId": "2", "ind": 1}, {"ty": 4, "nm": "reshot-icon-pizza-delivery-NE62BC7Z4J 1 Bg", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 217}, {"s": [1, 1, 1], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}}], "ind": 2}]}, {"nm": "[Asset] _x39_", "id": "2", "layers": [{"ty": 4, "nm": "icon_18_", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 217}, {"s": [1, 0.55], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 9.85], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.55], "t": 217}, {"s": [1, 0.55], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, -0.07], [0, 0], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [0, 0], [0.07, 0.01]], "o": [[-0.07, -0.01], [0, 0], [-0.01, 0.08], [0, 0], [0.07, 0], [0, 0], [0.01, -0.07], [0, 0]], "v": [[9.62, 12.39], [9.47, 12.51], [9.45, 12.64], [9.58, 12.8], [9.59, 12.8], [9.73, 12.68], [9.74, 12.54], [9.62, 12.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, -0.07], [0, 0], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [0, 0], [0.07, 0.01]], "o": [[-0.07, -0.01], [0, 0], [-0.01, 0.08], [0, 0], [0.07, 0], [0, 0], [0.01, -0.07], [0, 0]], "v": [[9.62, 12.39], [9.47, 12.51], [9.45, 12.64], [9.58, 12.8], [9.59, 12.8], [9.73, 12.68], [9.74, 12.54], [9.62, 12.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, -0.04], [0.23, -2.34], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [-0.02, 0.01], [0.05, 0.06]], "o": [[-0.05, -0.06], [-0.08, 0.07], [-0.01, 0.07], [0, 0], [0.07, 0], [0.22, -2.22], [0.06, -0.05], [0, 0]], "v": [[11.95, 8.15], [11.75, 8.12], [9.56, 12.05], [9.68, 12.2], [9.7, 12.2], [9.83, 12.08], [11.93, 8.34], [11.95, 8.15]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, -0.04], [0.23, -2.34], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [-0.02, 0.01], [0.05, 0.06]], "o": [[-0.05, -0.06], [-0.08, 0.07], [-0.01, 0.07], [0, 0], [0.07, 0], [0.22, -2.22], [0.06, -0.05], [0, 0]], "v": [[11.95, 8.15], [11.75, 8.12], [9.56, 12.05], [9.68, 12.2], [9.7, 12.2], [9.83, 12.08], [11.93, 8.34], [11.95, 8.15]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0.69], [0, 0.72], [0.41, 0.72], [0.41, 0.69], [0, 0.69]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0.69], [0, 0.72], [0.41, 0.72], [0.41, 0.69], [0, 0.69]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 12.47], [0, 13.03], [7.45, 13.03], [7.45, 12.47], [0, 12.47]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 12.47], [0, 13.03], [7.45, 13.03], [7.45, 12.47], [0, 12.47]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0.69], [0, 0.72], [0.41, 0.72], [0.41, 0.69], [0, 0.69]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0.69], [0, 0.72], [0.41, 0.72], [0.41, 0.69], [0, 0.69]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.17, 0.59], [0.17, 0.62], [0.41, 0.62], [0.41, 0.59], [0.17, 0.59]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.17, 0.59], [0.17, 0.62], [0.41, 0.62], [0.41, 0.59], [0.17, 0.59]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.04, 10.62], [3.04, 11.17], [7.45, 11.17], [7.45, 10.62], [3.04, 10.62]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.04, 10.62], [3.04, 11.17], [7.45, 11.17], [7.45, 10.62], [3.04, 10.62]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.17, 0.59], [0.17, 0.62], [0.41, 0.62], [0.41, 0.59], [0.17, 0.59]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.17, 0.59], [0.17, 0.62], [0.41, 0.62], [0.41, 0.59], [0.17, 0.59]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.08, 0.48], [0.08, 0.51], [0.45, 0.51], [0.45, 0.48], [0.08, 0.48]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.08, 0.48], [0.08, 0.51], [0.45, 0.51], [0.45, 0.48], [0.08, 0.48]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.38, 8.63], [1.38, 9.19], [8.05, 9.19], [8.05, 8.63], [1.38, 8.63]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.38, 8.63], [1.38, 9.19], [8.05, 9.19], [8.05, 8.63], [1.38, 8.63]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.08, 0.48], [0.08, 0.51], [0.45, 0.51], [0.45, 0.48], [0.08, 0.48]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.08, 0.48], [0.08, 0.51], [0.45, 0.51], [0.45, 0.48], [0.08, 0.48]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -1.24], [-1.24, 0], [0, 1.24], [1.24, 0]], "o": [[-1.24, 0], [0, 1.24], [1.24, 0], [0, -1.24], [0, 0]], "v": [[31.57, 13.45], [29.32, 15.7], [31.57, 17.94], [33.82, 15.7], [31.57, 13.45]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, -0.07], [-0.07, 0], [0, 0.07], [0.07, 0]], "o": [[-0.07, 0], [0, 0.07], [0.07, 0], [0, -0.07], [0, 0]], "v": [[1.75, 0.75], [1.63, 0.87], [1.75, 1], [1.88, 0.87], [1.75, 0.75]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.93], [-0.94, 0], [0, -0.94], [0.93, 0]], "o": [[-0.94, 0], [0, -0.94], [0.93, 0], [0, 0.93], [0, 0]], "v": [[31.57, 17.39], [29.88, 15.7], [31.57, 14], [33.26, 15.7], [31.57, 17.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.05], [-0.05, 0], [0, -0.05], [0.05, 0]], "o": [[-0.05, 0], [0, -0.05], [0.05, 0], [0, 0.05], [0, 0]], "v": [[1.75, 0.97], [1.66, 0.87], [1.75, 0.78], [1.85, 0.87], [1.75, 0.97]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, -0.04], [0.23, -2.34], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [-0.02, 0.01], [0.05, 0.06]], "o": [[-0.05, -0.06], [-0.08, 0.07], [-0.01, 0.07], [0, 0], [0.07, 0], [0.22, -2.22], [0.06, -0.05], [0, 0]], "v": [[11.95, 8.15], [11.75, 8.12], [9.56, 12.05], [9.68, 12.2], [9.7, 12.2], [9.83, 12.08], [11.93, 8.34], [11.95, 8.15]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, -0.04], [0.23, -2.34], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [-0.02, 0.01], [0.05, 0.06]], "o": [[-0.05, -0.06], [-0.08, 0.07], [-0.01, 0.07], [0, 0], [0.07, 0], [0.22, -2.22], [0.06, -0.05], [0, 0]], "v": [[11.95, 8.15], [11.75, 8.12], [9.56, 12.05], [9.68, 12.2], [9.7, 12.2], [9.83, 12.08], [11.93, 8.34], [11.95, 8.15]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.01, -0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.12], [0, 0], [0, 0]], "v": [[0.66, 0.45], [0.65, 0.45], [0.53, 0.67], [0.54, 0.68], [0.54, 0.68], [0.55, 0.67], [0.66, 0.46], [0.66, 0.45]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, -0.07], [0, 0], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [0, 0], [0.07, 0.01]], "o": [[-0.07, -0.01], [0, 0], [-0.01, 0.08], [0, 0], [0.07, 0], [0, 0], [0.01, -0.07], [0, 0]], "v": [[9.62, 12.39], [9.47, 12.51], [9.45, 12.64], [9.58, 12.8], [9.59, 12.8], [9.73, 12.68], [9.74, 12.54], [9.62, 12.39]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, -0.07], [0, 0], [-0.08, -0.01], [0, 0], [-0.01, 0.07], [0, 0], [0.07, 0.01]], "o": [[-0.07, -0.01], [0, 0], [-0.01, 0.08], [0, 0], [0.07, 0], [0, 0], [0.01, -0.07], [0, 0]], "v": [[9.62, 12.39], [9.47, 12.51], [9.45, 12.64], [9.58, 12.8], [9.59, 12.8], [9.73, 12.68], [9.74, 12.54], [9.62, 12.39]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.53, 0.69], [0.53, 0.69], [0.53, 0.7], [0.53, 0.71], [0.53, 0.71], [0.54, 0.7], [0.54, 0.7], [0.53, 0.69]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.01, -0.01]], "o": [[0, 0], [0, 0], [-0.01, 0.01], [0, 0]], "v": [[20.7, 7.16], [20.73, 7.16], [20.73, 7.14], [20.7, 7.16]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.02], [0.08, 0.06], [0.13, -0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0.04], [0, 0.03], [0.01, 0.02], [0.01, 0], [0.02, -0.02], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.02, -0.05], [0, 0], [0.05, 0], [0.01, 0.01], [0, 0.04], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0, 0.02], [0, 0], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0.02, 0.03], [0, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0, -0.02], [-0.05, -0.04], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0.01, 0], [0, -0.02], [0, -0.03], [-0.02, -0.03], [-0.03, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.03, 0.08], [0, 0], [-0.05, 0], [-0.04, 0], [-0.01, -0.01], [0, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.12, 0.13], [0, 0], [0, 0], [0, 0.02], [0, 0.12], [0.12, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0.02], [0, 0.12], [0.12, 0], [0, -0.04], [0, 0], [0.02, 0], [0, 0]], "v": [[1.99, 0.74], [2, 0.7], [1.9, 0.55], [1.64, 0.48], [1.56, 0.33], [1.52, 0.27], [1.54, 0.27], [1.56, 0.27], [1.6, 0.22], [1.6, 0.15], [1.58, 0.08], [1.53, 0.05], [1.45, 0.09], [1.44, 0.12], [1.4, 0.05], [1.24, 0.09], [1.22, 0.12], [1.25, 0.13], [1.31, 0.12], [1.48, 0.5], [1.39, 0.68], [1.15, 0.69], [1.08, 0.67], [1.06, 0.6], [1.18, 0.44], [1.18, 0.44], [1.18, 0.36], [1.15, 0.36], [1.15, 0], [0.51, 0], [0.51, 0.36], [0.46, 0.36], [0.46, 0.44], [0.57, 0.44], [0.44, 0.8], [0.44, 0.82], [0.55, 0.82], [0.55, 0.87], [0.77, 1.09], [0.99, 0.87], [0.98, 0.82], [1.5, 0.82], [1.51, 0.8], [1.54, 0.8], [1.53, 0.87], [1.75, 1.09], [1.98, 0.87], [1.94, 0.76], [1.95, 0.76], [1.99, 0.74]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.02], [0.08, 0.06], [0.13, -0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0.04], [0, 0.03], [0.01, 0.02], [0.01, 0], [0.02, -0.02], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.02, -0.05], [0, 0], [0.05, 0], [0.01, 0.01], [0, 0.04], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0, 0.02], [0, 0], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0.02, 0.03], [0, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0, -0.02], [-0.05, -0.04], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0.01, 0], [0, -0.02], [0, -0.03], [-0.02, -0.03], [-0.03, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.03, 0.08], [0, 0], [-0.05, 0], [-0.04, 0], [-0.01, -0.01], [0, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.12, 0.13], [0, 0], [0, 0], [0, 0.02], [0, 0.12], [0.12, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0.02], [0, 0.12], [0.12, 0], [0, -0.04], [0, 0], [0.02, 0], [0, 0]], "v": [[1.99, 0.74], [2, 0.7], [1.9, 0.55], [1.64, 0.48], [1.56, 0.33], [1.52, 0.27], [1.54, 0.27], [1.56, 0.27], [1.6, 0.22], [1.6, 0.15], [1.58, 0.08], [1.53, 0.05], [1.45, 0.09], [1.44, 0.12], [1.4, 0.05], [1.24, 0.09], [1.22, 0.12], [1.25, 0.13], [1.31, 0.12], [1.48, 0.5], [1.39, 0.68], [1.15, 0.69], [1.08, 0.67], [1.06, 0.6], [1.18, 0.44], [1.18, 0.44], [1.18, 0.36], [1.15, 0.36], [1.15, 0], [0.51, 0], [0.51, 0.36], [0.46, 0.36], [0.46, 0.44], [0.57, 0.44], [0.44, 0.8], [0.44, 0.82], [0.55, 0.82], [0.55, 0.87], [0.77, 1.09], [0.99, 0.87], [0.98, 0.82], [1.5, 0.82], [1.51, 0.8], [1.54, 0.8], [1.53, 0.87], [1.75, 1.09], [1.98, 0.87], [1.94, 0.76], [1.95, 0.76], [1.99, 0.74]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, 0.27], [1.38, 1.08], [2.32, -0.1], [0, 0], [0, 0], [-0.14, 0], [-0.1, 0.02], [-0.14, 0.69], [0.07, 0.45], [0.17, 0.31], [0.22, -0.04], [0.31, -0.43], [0.06, -0.19], [0, 0], [0, 0], [-0.06, -0.22], [-0.22, 0.06], [0, 0], [-0.41, -0.95], [0, 0], [0.93, -0.06], [0.24, 0.23], [0, 0.67], [-0.39, 0.23], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.2], [0, 0], [0, 0], [0, -0.31], [-2.2, 0], [0, 2.2], [0.07, 0.3], [0, 0], [0, 0], [0, 0], [0, -0.42], [-2.2, 0], [0, 2.2], [0.39, 0.62], [-0.02, 0], [-0.19, 0.2]], "o": [[0.19, -0.2], [-0.08, -0.37], [-0.85, -0.67], [0, 0], [0, 0], [0.13, 0.03], [0.1, 0], [0.22, -0.03], [0.07, -0.35], [-0.07, -0.45], [-0.35, -0.61], [-0.52, 0.08], [-0.12, 0.16], [0, 0], [0, 0], [-0.22, 0.06], [0.06, 0.22], [0, 0], [0.61, 1.37], [0, 0], [-0.83, 0], [-0.66, 0.04], [-0.26, -0.24], [0, -1.5], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.07, 2.29], [0, 0], [0, 0], [-0.07, 0.3], [0, 2.2], [2.2, 0], [0, -0.31], [0, 0], [0, 0], [0, 0], [-0.12, 0.39], [0, 2.2], [2.2, 0], [0, -0.76], [0.02, 0], [0.27, 0.05], [0, 0]], "v": [[35.76, 13.38], [35.98, 12.63], [34.17, 9.91], [29.5, 8.6], [28.06, 5.96], [27.39, 4.8], [27.79, 4.84], [28.11, 4.82], [28.85, 3.93], [28.84, 2.69], [28.47, 1.52], [27.48, 0.91], [26.19, 1.7], [25.92, 2.23], [25.11, 0.82], [22.28, 1.58], [21.99, 2.09], [22.5, 2.38], [23.63, 2.08], [26.63, 8.98], [25.03, 12.29], [20.7, 12.36], [19.41, 12.1], [19.03, 10.73], [21.19, 7.84], [21.27, 7.84], [21.27, 6.47], [20.73, 6.47], [20.73, 0], [9.11, 0], [9.11, 6.48], [8.33, 6.48], [8.33, 7.84], [10.21, 7.84], [7.88, 14.35], [7.86, 14.79], [9.92, 14.79], [9.82, 15.7], [13.81, 19.69], [17.8, 15.7], [17.7, 14.79], [26.94, 14.79], [27.13, 14.39], [27.76, 14.49], [27.57, 15.7], [31.57, 19.69], [35.56, 15.7], [34.97, 13.6], [35.02, 13.61], [35.76, 13.38]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.06, 0.27], [1.38, 1.08], [2.32, -0.1], [0, 0], [0, 0], [-0.14, 0], [-0.1, 0.02], [-0.14, 0.69], [0.07, 0.45], [0.17, 0.31], [0.22, -0.04], [0.31, -0.43], [0.06, -0.19], [0, 0], [0, 0], [-0.06, -0.22], [-0.22, 0.06], [0, 0], [-0.41, -0.95], [0, 0], [0.93, -0.06], [0.24, 0.23], [0, 0.67], [-0.39, 0.23], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, -0.2], [0, 0], [0, 0], [0, -0.31], [-2.2, 0], [0, 2.2], [0.07, 0.3], [0, 0], [0, 0], [0, 0], [0, -0.42], [-2.2, 0], [0, 2.2], [0.39, 0.62], [-0.02, 0], [-0.19, 0.2]], "o": [[0.19, -0.2], [-0.08, -0.37], [-0.85, -0.67], [0, 0], [0, 0], [0.13, 0.03], [0.1, 0], [0.22, -0.03], [0.07, -0.35], [-0.07, -0.45], [-0.35, -0.61], [-0.52, 0.08], [-0.12, 0.16], [0, 0], [0, 0], [-0.22, 0.06], [0.06, 0.22], [0, 0], [0.61, 1.37], [0, 0], [-0.83, 0], [-0.66, 0.04], [-0.26, -0.24], [0, -1.5], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.07, 2.29], [0, 0], [0, 0], [-0.07, 0.3], [0, 2.2], [2.2, 0], [0, -0.31], [0, 0], [0, 0], [0, 0], [-0.12, 0.39], [0, 2.2], [2.2, 0], [0, -0.76], [0.02, 0], [0.27, 0.05], [0, 0]], "v": [[35.76, 13.38], [35.98, 12.63], [34.17, 9.91], [29.5, 8.6], [28.06, 5.96], [27.39, 4.8], [27.79, 4.84], [28.11, 4.82], [28.85, 3.93], [28.84, 2.69], [28.47, 1.52], [27.48, 0.91], [26.19, 1.7], [25.92, 2.23], [25.11, 0.82], [22.28, 1.58], [21.99, 2.09], [22.5, 2.38], [23.63, 2.08], [26.63, 8.98], [25.03, 12.29], [20.7, 12.36], [19.41, 12.1], [19.03, 10.73], [21.19, 7.84], [21.27, 7.84], [21.27, 6.47], [20.73, 6.47], [20.73, 0], [9.11, 0], [9.11, 6.48], [8.33, 6.48], [8.33, 7.84], [10.21, 7.84], [7.88, 14.35], [7.86, 14.79], [9.92, 14.79], [9.82, 15.7], [13.81, 19.69], [17.8, 15.7], [17.7, 14.79], [26.94, 14.79], [27.13, 14.39], [27.76, 14.49], [27.57, 15.7], [31.57, 19.69], [35.56, 15.7], [34.97, 13.6], [35.02, 13.61], [35.76, 13.38]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.02], [0.08, 0.06], [0.13, -0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0.04], [0, 0.03], [0.01, 0.02], [0.01, 0], [0.02, -0.02], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.02, -0.05], [0, 0], [0.05, 0], [0.01, 0.01], [0, 0.04], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0, 0.02], [0, 0], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0.02, 0.03], [0, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0, -0.02], [-0.05, -0.04], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0.01, 0], [0, -0.02], [0, -0.03], [-0.02, -0.03], [-0.03, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.03, 0.08], [0, 0], [-0.05, 0], [-0.04, 0], [-0.01, -0.01], [0, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.12, 0.13], [0, 0], [0, 0], [0, 0.02], [0, 0.12], [0.12, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0.02], [0, 0.12], [0.12, 0], [0, -0.04], [0, 0], [0.02, 0], [0, 0]], "v": [[1.99, 0.74], [2, 0.7], [1.9, 0.55], [1.64, 0.48], [1.56, 0.33], [1.52, 0.27], [1.54, 0.27], [1.56, 0.27], [1.6, 0.22], [1.6, 0.15], [1.58, 0.08], [1.53, 0.05], [1.45, 0.09], [1.44, 0.12], [1.4, 0.05], [1.24, 0.09], [1.22, 0.12], [1.25, 0.13], [1.31, 0.12], [1.48, 0.5], [1.39, 0.68], [1.15, 0.69], [1.08, 0.67], [1.06, 0.6], [1.18, 0.44], [1.18, 0.44], [1.18, 0.36], [1.15, 0.36], [1.15, 0], [0.51, 0], [0.51, 0.36], [0.46, 0.36], [0.46, 0.44], [0.57, 0.44], [0.44, 0.8], [0.44, 0.82], [0.55, 0.82], [0.55, 0.87], [0.77, 1.09], [0.99, 0.87], [0.98, 0.82], [1.5, 0.82], [1.51, 0.8], [1.54, 0.8], [1.53, 0.87], [1.75, 1.09], [1.98, 0.87], [1.94, 0.76], [1.95, 0.76], [1.99, 0.74]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.02], [0.08, 0.06], [0.13, -0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0.04], [0, 0.03], [0.01, 0.02], [0.01, 0], [0.02, -0.02], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [-0.01, 0], [0, 0], [-0.02, -0.05], [0, 0], [0.05, 0], [0.01, 0.01], [0, 0.04], [-0.02, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0, 0.02], [0, 0], [0, 0], [0, 0], [0, -0.02], [-0.12, 0], [0, 0.12], [0.02, 0.03], [0, 0], [-0.01, 0.01]], "o": [[0.01, -0.01], [0, -0.02], [-0.05, -0.04], [0, 0], [0, 0], [0.01, 0], [0.01, 0], [0.01, 0], [0, -0.02], [0, -0.03], [-0.02, -0.03], [-0.03, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0.01], [0, 0], [0.03, 0.08], [0, 0], [-0.05, 0], [-0.04, 0], [-0.01, -0.01], [0, -0.08], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.12, 0.13], [0, 0], [0, 0], [0, 0.02], [0, 0.12], [0.12, 0], [0, -0.02], [0, 0], [0, 0], [0, 0], [-0.01, 0.02], [0, 0.12], [0.12, 0], [0, -0.04], [0, 0], [0.02, 0], [0, 0]], "v": [[1.99, 0.74], [2, 0.7], [1.9, 0.55], [1.64, 0.48], [1.56, 0.33], [1.52, 0.27], [1.54, 0.27], [1.56, 0.27], [1.6, 0.22], [1.6, 0.15], [1.58, 0.08], [1.53, 0.05], [1.45, 0.09], [1.44, 0.12], [1.4, 0.05], [1.24, 0.09], [1.22, 0.12], [1.25, 0.13], [1.31, 0.12], [1.48, 0.5], [1.39, 0.68], [1.15, 0.69], [1.08, 0.67], [1.06, 0.6], [1.18, 0.44], [1.18, 0.44], [1.18, 0.36], [1.15, 0.36], [1.15, 0], [0.51, 0], [0.51, 0.36], [0.46, 0.36], [0.46, 0.44], [0.57, 0.44], [0.44, 0.8], [0.44, 0.82], [0.55, 0.82], [0.55, 0.87], [0.77, 1.09], [0.99, 0.87], [0.98, 0.82], [1.5, 0.82], [1.51, 0.8], [1.54, 0.8], [1.53, 0.87], [1.75, 1.09], [1.98, 0.87], [1.94, 0.76], [1.95, 0.76], [1.99, 0.74]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0.01]], "o": [[0, 0], [0, 0], [0.01, -0.01], [0, 0]], "v": [[20.73, 7.14], [20.73, 7.16], [20.7, 7.16], [20.73, 7.14]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.01, 0.01]], "o": [[0, 0], [0, 0], [0.01, -0.01], [0, 0]], "v": [[20.73, 7.14], [20.73, 7.16], [20.7, 7.16], [20.73, 7.14]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.15, 0.4], [1.15, 0.4], [1.15, 0.4], [1.15, 0.4]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0], [-0.01, -0.04], [0, -0.01], [0.01, 0.03], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.01, 0], [0.01, 0.04], [-0.03, 0.01], [0, -0.02], [0, 0]], "v": [[1.49, 0.12], [1.53, 0.1], [1.56, 0.16], [1.55, 0.22], [1.48, 0.17], [1.49, 0.12]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0], [-0.01, -0.04], [0, -0.01], [0.01, 0.03], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.01, 0], [0.01, 0.04], [-0.03, 0.01], [0, -0.02], [0, 0]], "v": [[1.49, 0.12], [1.53, 0.1], [1.56, 0.16], [1.55, 0.22], [1.48, 0.17], [1.49, 0.12]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0.05], [-0.1, -0.66], [0.08, -0.09], [0.1, 0.62], [-0.18, 0.25]], "o": [[0.18, -0.25], [0.1, 0.06], [0.11, 0.66], [-0.62, 0.09], [-0.05, -0.3], [0, 0]], "v": [[26.86, 2.19], [27.59, 1.73], [28.03, 2.82], [27.96, 4], [26.66, 3.04], [26.86, 2.19]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0.05], [-0.1, -0.66], [0.08, -0.09], [0.1, 0.62], [-0.18, 0.25]], "o": [[0.18, -0.25], [0.1, 0.06], [0.11, 0.66], [-0.62, 0.09], [-0.05, -0.3], [0, 0]], "v": [[26.86, 2.19], [27.59, 1.73], [28.03, 2.82], [27.96, 4], [26.66, 3.04], [26.86, 2.19]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.02, 0], [-0.01, -0.04], [0, -0.01], [0.01, 0.03], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.01, 0], [0.01, 0.04], [-0.03, 0.01], [0, -0.02], [0, 0]], "v": [[1.49, 0.12], [1.53, 0.1], [1.56, 0.16], [1.55, 0.22], [1.48, 0.17], [1.49, 0.12]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [-0.02, 0], [-0.01, -0.04], [0, -0.01], [0.01, 0.03], [-0.01, 0.01]], "o": [[0.01, -0.01], [0.01, 0], [0.01, 0.04], [-0.03, 0.01], [0, -0.02], [0, 0]], "v": [[1.49, 0.12], [1.53, 0.1], [1.56, 0.16], [1.55, 0.22], [1.48, 0.17], [1.49, 0.12]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.05], [1.11, 0.05], [1.11, 0.09], [0.55, 0.09], [0.55, 0.05]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.05], [1.11, 0.05], [1.11, 0.09], [0.55, 0.09], [0.55, 0.05]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.94, 0.83], [19.9, 0.83], [19.9, 1.64], [9.94, 1.64], [9.94, 0.83]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.94, 0.83], [19.9, 0.83], [19.9, 1.64], [9.94, 1.64], [9.94, 0.83]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.05], [1.11, 0.05], [1.11, 0.09], [0.55, 0.09], [0.55, 0.05]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.05], [1.11, 0.05], [1.11, 0.09], [0.55, 0.09], [0.55, 0.05]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.35], [0.55, 0.12], [0.79, 0.12], [0.79, 0.22], [0.86, 0.22], [0.86, 0.12], [1.11, 0.12], [1.11, 0.35], [0.55, 0.35]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.35], [0.55, 0.12], [0.79, 0.12], [0.79, 0.22], [0.86, 0.22], [0.86, 0.12], [1.11, 0.12], [1.11, 0.35], [0.55, 0.35]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.94, 6.33], [9.94, 2.19], [14.21, 2.19], [14.21, 3.87], [15.41, 3.87], [15.41, 2.19], [19.9, 2.19], [19.9, 6.33], [9.94, 6.33]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.94, 6.33], [9.94, 2.19], [14.21, 2.19], [14.21, 3.87], [15.41, 3.87], [15.41, 2.19], [19.9, 2.19], [19.9, 6.33], [9.94, 6.33]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.35], [0.55, 0.12], [0.79, 0.12], [0.79, 0.22], [0.86, 0.22], [0.86, 0.12], [1.11, 0.12], [1.11, 0.35], [0.55, 0.35]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.55, 0.35], [0.55, 0.12], [0.79, 0.12], [0.79, 0.22], [0.86, 0.22], [0.86, 0.12], [1.11, 0.12], [1.11, 0.35], [0.55, 0.35]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [0, 0.02], [0, 0], [0, -0.02], [-0.07, 0], [0, 0.07], [0.01, 0.02], [0, 0], [0, -0.02], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [-0.01, 0.02], [0, 0.07], [0.07, 0], [0, -0.02], [0, 0], [0, 0.02], [0, 0.1], [0, 0]], "v": [[0.77, 1.05], [0.59, 0.87], [0.6, 0.82], [0.65, 0.82], [0.64, 0.87], [0.77, 1], [0.89, 0.87], [0.88, 0.82], [0.94, 0.82], [0.94, 0.87], [0.77, 1.05]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [0, 0.02], [0, 0], [0, -0.02], [-0.07, 0], [0, 0.07], [0.01, 0.02], [0, 0], [0, -0.02], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [-0.01, 0.02], [0, 0.07], [0.07, 0], [0, -0.02], [0, 0], [0, 0.02], [0, 0.1], [0, 0]], "v": [[0.77, 1.05], [0.59, 0.87], [0.6, 0.82], [0.65, 0.82], [0.64, 0.87], [0.77, 1], [0.89, 0.87], [0.88, 0.82], [0.94, 0.82], [0.94, 0.87], [0.77, 1.05]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.74], [-0.09, 0.29], [0, 0], [0, -0.32], [-1.24, 0], [0, 1.24], [0.13, 0.28], [0, 0], [0, -0.31], [1.75, 0]], "o": [[-1.74, 0], [0, -0.31], [0, 0], [-0.13, 0.28], [0, 1.24], [1.24, 0], [0, -0.32], [0, 0], [0.09, 0.29], [0, 1.74], [0, 0]], "v": [[13.81, 18.86], [10.64, 15.7], [10.78, 14.79], [11.76, 14.79], [11.57, 15.7], [13.81, 17.94], [16.06, 15.7], [15.86, 14.79], [16.85, 14.79], [16.98, 15.7], [13.81, 18.86]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.74], [-0.09, 0.29], [0, 0], [0, -0.32], [-1.24, 0], [0, 1.24], [0.13, 0.28], [0, 0], [0, -0.31], [1.75, 0]], "o": [[-1.74, 0], [0, -0.31], [0, 0], [-0.13, 0.28], [0, 1.24], [1.24, 0], [0, -0.32], [0, 0], [0.09, 0.29], [0, 1.74], [0, 0]], "v": [[13.81, 18.86], [10.64, 15.7], [10.78, 14.79], [11.76, 14.79], [11.57, 15.7], [13.81, 17.94], [16.06, 15.7], [15.86, 14.79], [16.85, 14.79], [16.98, 15.7], [13.81, 18.86]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [0, 0.02], [0, 0], [0, -0.02], [-0.07, 0], [0, 0.07], [0.01, 0.02], [0, 0], [0, -0.02], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [-0.01, 0.02], [0, 0.07], [0.07, 0], [0, -0.02], [0, 0], [0, 0.02], [0, 0.1], [0, 0]], "v": [[0.77, 1.05], [0.59, 0.87], [0.6, 0.82], [0.65, 0.82], [0.64, 0.87], [0.77, 1], [0.89, 0.87], [0.88, 0.82], [0.94, 0.82], [0.94, 0.87], [0.77, 1.05]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.1], [0, 0.02], [0, 0], [0, -0.02], [-0.07, 0], [0, 0.07], [0.01, 0.02], [0, 0], [0, -0.02], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [-0.01, 0.02], [0, 0.07], [0.07, 0], [0, -0.02], [0, 0], [0, 0.02], [0, 0.1], [0, 0]], "v": [[0.77, 1.05], [0.59, 0.87], [0.6, 0.82], [0.65, 0.82], [0.64, 0.87], [0.77, 1], [0.89, 0.87], [0.88, 0.82], [0.94, 0.82], [0.94, 0.87], [0.77, 1.05]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.02], [0, 0], [0, -0.02], [0.05, 0], [0, 0.05]], "o": [[0, -0.02], [0, 0], [0.01, 0.02], [0, 0.05], [-0.05, 0], [0, 0]], "v": [[0.67, 0.87], [0.69, 0.82], [0.85, 0.82], [0.86, 0.87], [0.77, 0.97], [0.67, 0.87]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.02], [0, 0], [0, -0.02], [0.05, 0], [0, 0.05]], "o": [[0, -0.02], [0, 0], [0.01, 0.02], [0, 0.05], [-0.05, 0], [0, 0]], "v": [[0.67, 0.87], [0.69, 0.82], [0.85, 0.82], [0.86, 0.87], [0.77, 0.97], [0.67, 0.87]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.17, 0.27], [0, 0], [0, -0.33], [0.93, 0], [0, 0.93]], "o": [[0, -0.33], [0, 0], [0.17, 0.27], [0, 0.93], [-0.93, 0], [0, 0]], "v": [[12.12, 15.7], [12.38, 14.79], [15.24, 14.79], [15.5, 15.7], [13.81, 17.39], [12.12, 15.7]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.17, 0.27], [0, 0], [0, -0.33], [0.93, 0], [0, 0.93]], "o": [[0, -0.33], [0, 0], [0.17, 0.27], [0, 0.93], [-0.93, 0], [0, 0]], "v": [[12.12, 15.7], [12.38, 14.79], [15.24, 14.79], [15.5, 15.7], [13.81, 17.39], [12.12, 15.7]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.02], [0, 0], [0, -0.02], [0.05, 0], [0, 0.05]], "o": [[0, -0.02], [0, 0], [0.01, 0.02], [0, 0.05], [-0.05, 0], [0, 0]], "v": [[0.67, 0.87], [0.69, 0.82], [0.85, 0.82], [0.86, 0.87], [0.77, 0.97], [0.67, 0.87]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [-0.01, 0.02], [0, 0], [0, -0.02], [0.05, 0], [0, 0.05]], "o": [[0, -0.02], [0, 0], [0.01, 0.02], [0, 0.05], [-0.05, 0], [0, 0]], "v": [[0.67, 0.87], [0.69, 0.82], [0.85, 0.82], [0.86, 0.87], [0.77, 0.97], [0.67, 0.87]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.11, 0.08], [0, 0], [0, -0.07], [-0.02, -0.02], [-0.03, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.04, 0.09], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.01, -0.07], [0, 0], [-0.04, 0.04], [0, 0.05], [0.02, 0.02], [0.01, 0], [0.06, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.47, 0.78], [0.49, 0.78], [0.63, 0.44], [1.1, 0.44], [1.01, 0.6], [1.05, 0.71], [1.13, 0.73], [1.15, 0.73], [1.4, 0.73], [1.42, 0.73], [1.53, 0.5], [1.53, 0.49], [1.36, 0.1], [1.37, 0.1], [1.52, 0.35], [1.6, 0.5], [1.47, 0.78]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.11, 0.08], [0, 0], [0, -0.07], [-0.02, -0.02], [-0.03, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.04, 0.09], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.01, -0.07], [0, 0], [-0.04, 0.04], [0, 0.05], [0.02, 0.02], [0.01, 0], [0.06, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.47, 0.78], [0.49, 0.78], [0.63, 0.44], [1.1, 0.44], [1.01, 0.6], [1.05, 0.71], [1.13, 0.73], [1.15, 0.73], [1.4, 0.73], [1.42, 0.73], [1.53, 0.5], [1.53, 0.49], [1.36, 0.1], [1.37, 0.1], [1.52, 0.35], [1.6, 0.5], [1.47, 0.78]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.99, 1.49], [0, 0], [0, -1.25], [-0.44, -0.41], [-0.54, 0], [-0.12, 0.01], [-0.04, 0], [0, 0], [0, 0], [0, 0], [0.73, 1.66], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.14, -1.21], [0, 0], [-0.74, 0.64], [0, 0.92], [0.43, 0.4], [0.13, 0], [1.07, -0.07], [0, 0], [0, 0], [0, 0], [-0.1, -0.24], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[26.42, 13.96], [8.75, 13.96], [11.43, 7.84], [19.79, 7.84], [18.2, 10.73], [18.85, 12.7], [20.37, 13.2], [20.75, 13.19], [25.29, 13.12], [25.54, 13.12], [27.54, 9], [27.47, 8.83], [24.44, 1.86], [24.71, 1.78], [27.34, 6.37], [28.8, 9.04], [26.42, 13.96]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.99, 1.49], [0, 0], [0, -1.25], [-0.44, -0.41], [-0.54, 0], [-0.12, 0.01], [-0.04, 0], [0, 0], [0, 0], [0, 0], [0.73, 1.66], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.14, -1.21], [0, 0], [-0.74, 0.64], [0, 0.92], [0.43, 0.4], [0.13, 0], [1.07, -0.07], [0, 0], [0, 0], [0, 0], [-0.1, -0.24], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[26.42, 13.96], [8.75, 13.96], [11.43, 7.84], [19.79, 7.84], [18.2, 10.73], [18.85, 12.7], [20.37, 13.2], [20.75, 13.19], [25.29, 13.12], [25.54, 13.12], [27.54, 9], [27.47, 8.83], [24.44, 1.86], [24.71, 1.78], [27.34, 6.37], [28.8, 9.04], [26.42, 13.96]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.11, 0.08], [0, 0], [0, -0.07], [-0.02, -0.02], [-0.03, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.04, 0.09], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.01, -0.07], [0, 0], [-0.04, 0.04], [0, 0.05], [0.02, 0.02], [0.01, 0], [0.06, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.47, 0.78], [0.49, 0.78], [0.63, 0.44], [1.1, 0.44], [1.01, 0.6], [1.05, 0.71], [1.13, 0.73], [1.15, 0.73], [1.4, 0.73], [1.42, 0.73], [1.53, 0.5], [1.53, 0.49], [1.36, 0.1], [1.37, 0.1], [1.52, 0.35], [1.6, 0.5], [1.47, 0.78]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [-0.11, 0.08], [0, 0], [0, -0.07], [-0.02, -0.02], [-0.03, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.04, 0.09], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.01, -0.07], [0, 0], [-0.04, 0.04], [0, 0.05], [0.02, 0.02], [0.01, 0], [0.06, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.47, 0.78], [0.49, 0.78], [0.63, 0.44], [1.1, 0.44], [1.01, 0.6], [1.05, 0.71], [1.13, 0.73], [1.15, 0.73], [1.4, 0.73], [1.42, 0.73], [1.53, 0.5], [1.53, 0.49], [1.36, 0.1], [1.37, 0.1], [1.52, 0.35], [1.6, 0.5], [1.47, 0.78]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [-0.01, 0.02], [0, 0], [-0.07, -0.01], [0, 0], [0, -0.05], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [0.03, -0.01], [0, 0], [0.04, 0.03], [0, 0.1], [0, 0]], "v": [[1.75, 1.05], [1.58, 0.87], [1.59, 0.8], [1.68, 0.74], [1.88, 0.74], [1.88, 0.74], [1.93, 0.87], [1.75, 1.05]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [-0.01, 0.02], [0, 0], [-0.07, -0.01], [0, 0], [0, -0.05], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [0.03, -0.01], [0, 0], [0.04, 0.03], [0, 0.1], [0, 0]], "v": [[1.75, 1.05], [1.58, 0.87], [1.59, 0.8], [1.68, 0.74], [1.88, 0.74], [1.88, 0.74], [1.93, 0.87], [1.75, 1.05]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.74], [-0.18, 0.4], [0, 0], [-1.33, -0.19], [0, 0], [0, -0.87], [1.74, 0]], "o": [[-1.75, 0], [0, -0.44], [0, 0], [0.58, -0.22], [0, 0], [0.63, 0.6], [0, 1.74], [0, 0]], "v": [[31.57, 18.86], [28.4, 15.7], [28.67, 14.42], [30.32, 13.31], [33.76, 13.4], [33.76, 13.41], [34.74, 15.7], [31.57, 18.86]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 1.74], [-0.18, 0.4], [0, 0], [-1.33, -0.19], [0, 0], [0, -0.87], [1.74, 0]], "o": [[-1.75, 0], [0, -0.44], [0, 0], [0.58, -0.22], [0, 0], [0.63, 0.6], [0, 1.74], [0, 0]], "v": [[31.57, 18.86], [28.4, 15.7], [28.67, 14.42], [30.32, 13.31], [33.76, 13.4], [33.76, 13.41], [34.74, 15.7], [31.57, 18.86]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.1], [-0.01, 0.02], [0, 0], [-0.07, -0.01], [0, 0], [0, -0.05], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [0.03, -0.01], [0, 0], [0.04, 0.03], [0, 0.1], [0, 0]], "v": [[1.75, 1.05], [1.58, 0.87], [1.59, 0.8], [1.68, 0.74], [1.88, 0.74], [1.88, 0.74], [1.93, 0.87], [1.75, 1.05]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0.1], [-0.01, 0.02], [0, 0], [-0.07, -0.01], [0, 0], [0, -0.05], [0.1, 0]], "o": [[-0.1, 0], [0, -0.02], [0, 0], [0.03, -0.01], [0, 0], [0.04, 0.03], [0, 0.1], [0, 0]], "v": [[1.75, 1.05], [1.58, 0.87], [1.59, 0.8], [1.68, 0.74], [1.88, 0.74], [1.88, 0.74], [1.93, 0.87], [1.75, 1.05]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.06], [0.07, -0.03]], "o": [[0, 0], [0, 0], [0, 0], [0.22, -0.01], [-0.02, 0], [0, 0]], "v": [[1.66, 0.7], [1.57, 0.76], [1.53, 0.76], [1.64, 0.52], [1.95, 0.71], [1.66, 0.7]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.06], [0.07, -0.03]], "o": [[0, 0], [0, 0], [0, 0], [0.22, -0.01], [-0.02, 0], [0, 0]], "v": [[1.66, 0.7], [1.57, 0.76], [1.53, 0.76], [1.64, 0.52], [1.95, 0.71], [1.66, 0.7]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.22, -1.01], [1.21, -0.52]], "o": [[0, 0], [0, 0], [0, 0], [3.99, -0.19], [-0.41, -0.08], [0, 0]], "v": [[29.96, 12.56], [28.24, 13.72], [27.51, 13.61], [29.53, 9.43], [35.18, 12.8], [29.96, 12.56]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.22, -1.01], [1.21, -0.52]], "o": [[0, 0], [0, 0], [0, 0], [3.99, -0.19], [-0.41, -0.08], [0, 0]], "v": [[29.96, 12.56], [28.24, 13.72], [27.51, 13.61], [29.53, 9.43], [35.18, 12.8], [29.96, 12.56]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.06], [0.07, -0.03]], "o": [[0, 0], [0, 0], [0, 0], [0.22, -0.01], [-0.02, 0], [0, 0]], "v": [[1.66, 0.7], [1.57, 0.76], [1.53, 0.76], [1.64, 0.52], [1.95, 0.71], [1.66, 0.7]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.06], [0.07, -0.03]], "o": [[0, 0], [0, 0], [0, 0], [0.22, -0.01], [-0.02, 0], [0, 0]], "v": [[1.66, 0.7], [1.57, 0.76], [1.53, 0.76], [1.64, 0.52], [1.95, 0.71], [1.66, 0.7]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 1}]}]}