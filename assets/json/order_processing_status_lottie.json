{"nm": "Flow 11", "ddd": 0, "h": 60, "w": 60, "meta": {"g": "LottieFiles Figma v57"}, "layers": [{"ty": 0, "nm": "Frame 14102", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": true, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "masksProperties": [{"nm": "", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}], "w": 60, "h": 60, "refId": "1", "ind": 1}, {"ty": 4, "nm": "Ellipse 60", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30, 30], "t": 217}, {"s": [30, 30], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [16.57, 0], [0, 16.57], [-16.57, 0], [0, -16.57]], "o": [[0, 16.57], [-16.57, 0], [0, -16.57], [16.57, 0], [0, 0]], "v": [[60, 30], [30, 60], [0, 30], [30, 0], [60, 30]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 2}], "v": "5.7.0", "fr": 60, "op": 217.19, "ip": 0, "assets": [{"nm": "[Asset] Frame 14102", "id": "1", "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.67, 0.89], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.67, 0.89], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [12, 16.06], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [12, 16.06], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.67, 0.89], "t": 217}, {"s": [0.67, 0.89], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.98], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.98], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 17.56], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 17.56], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 0.98], "t": 217}, {"s": [1, 0.98], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.01], [0, 0], [-0.01, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0.01]], "o": [[-0.01, -0.01], [0, 0], [0.01, -0.01], [0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0]], "v": [[0.93, 1.45], [0.94, 1.41], [1.08, 1.33], [1.11, 1.34], [1.1, 1.37], [0.96, 1.45], [0.95, 1.46], [0.93, 1.45]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.01], [0, 0], [-0.01, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0.01]], "o": [[-0.01, -0.01], [0, 0], [0.01, -0.01], [0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0]], "v": [[0.93, 1.45], [0.94, 1.41], [1.08, 1.33], [1.11, 1.34], [1.1, 1.37], [0.96, 1.45], [0.95, 1.46], [0.93, 1.45]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.13], [0, 0], [-0.13, -0.22], [0.22, -0.13], [0, 0], [0.08, 0], [0.09, 0.15]], "o": [[-0.13, -0.22], [0, 0], [0.22, -0.13], [0.13, 0.22], [0, 0], [-0.07, 0.04], [-0.16, 0], [0, 0]], "v": [[16.7, 26.02], [16.86, 25.39], [19.41, 23.92], [20.03, 24.09], [19.86, 24.71], [17.32, 26.18], [17.09, 26.24], [16.7, 26.02]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.13], [0, 0], [-0.13, -0.22], [0.22, -0.13], [0, 0], [0.08, 0], [0.09, 0.15]], "o": [[-0.13, -0.22], [0, 0], [0.22, -0.13], [0.13, 0.22], [0, 0], [-0.07, 0.04], [-0.16, 0], [0, 0]], "v": [[16.7, 26.02], [16.86, 25.39], [19.41, 23.92], [20.03, 24.09], [19.86, 24.71], [17.32, 26.18], [17.09, 26.24], [16.7, 26.02]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.01, 0.01], [0, 0], [-0.01, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0.01]], "o": [[-0.01, -0.01], [0, 0], [0.01, -0.01], [0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0]], "v": [[0.93, 1.45], [0.94, 1.41], [1.08, 1.33], [1.11, 1.34], [1.1, 1.37], [0.96, 1.45], [0.95, 1.46], [0.93, 1.45]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [-0.01, 0.01], [0, 0], [-0.01, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0.01]], "o": [[-0.01, -0.01], [0, 0], [0.01, -0.01], [0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [0, 0]], "v": [[0.93, 1.45], [0.94, 1.41], [1.08, 1.33], [1.11, 1.34], [1.1, 1.37], [0.96, 1.45], [0.95, 1.46], [0.93, 1.45]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0]], "o": [[0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[1.33, 0.91], [1.32, 0.92], [1.26, 0.96], [1.26, 1.43], [1.24, 1.45], [0.68, 1.78], [0.67, 1.78], [0.65, 1.78], [0.09, 1.45], [0.08, 1.43], [0.08, 0.96], [0.01, 0.92], [0, 0.91], [0, 0.89], [0.08, 0.77], [0.09, 0.76], [0.09, 0.76], [0.54, 0.5], [0.46, 0.39], [0.46, 0.36], [0.48, 0.35], [0.54, 0.35], [0.54, 0.03], [0.56, 0], [0.77, 0], [0.8, 0.03], [0.8, 0.35], [0.85, 0.35], [0.88, 0.36], [0.87, 0.39], [0.79, 0.5], [1.24, 0.76], [1.24, 0.76], [1.25, 0.77], [1.33, 0.89], [1.33, 0.91], [1.33, 0.91]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0]], "o": [[0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[1.33, 0.91], [1.32, 0.92], [1.26, 0.96], [1.26, 1.43], [1.24, 1.45], [0.68, 1.78], [0.67, 1.78], [0.65, 1.78], [0.09, 1.45], [0.08, 1.43], [0.08, 0.96], [0.01, 0.92], [0, 0.91], [0, 0.89], [0.08, 0.77], [0.09, 0.76], [0.09, 0.76], [0.54, 0.5], [0.46, 0.39], [0.46, 0.36], [0.48, 0.35], [0.54, 0.35], [0.54, 0.03], [0.56, 0], [0.77, 0], [0.8, 0.03], [0.8, 0.35], [0.85, 0.35], [0.88, 0.36], [0.87, 0.39], [0.79, 0.5], [1.24, 0.76], [1.24, 0.76], [1.25, 0.77], [1.33, 0.89], [1.33, 0.91], [1.33, 0.91]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.11, -0.06], [0, 0], [0, 0], [0.14, -0.08], [0, 0], [0.08, 0], [0.07, 0.04], [0, 0], [0, 0.16], [0, 0], [0, 0], [0.03, 0.12], [-0.06, 0.1], [0, 0], [-0.06, 0.04], [0, 0], [0, 0], [0, 0], [-0.08, 0.16], [-0.17, 0], [0, 0], [0, 0], [-0.25, 0], [0, 0], [0, -0.25], [0, 0], [0, 0], [-0.08, -0.16], [0.11, -0.14], [0, 0], [0, 0], [0, 0], [-0.04, -0.07], [0, 0], [0.03, -0.12], [0, 0]], "o": [[-0.03, 0.12], [0, 0], [0, 0], [0, 0.16], [0, 0], [-0.07, 0.04], [-0.08, 0], [0, 0], [-0.14, -0.08], [0, 0], [0, 0], [-0.11, -0.06], [-0.03, -0.12], [0, 0], [0.04, -0.07], [0, 0], [0, 0], [0, 0], [-0.11, -0.14], [0.08, -0.16], [0, 0], [0, 0], [0, -0.25], [0, 0], [0.25, 0], [0, 0], [0, 0], [0.17, 0], [0.08, 0.16], [0, 0], [0, 0], [0, 0], [0.06, 0.04], [0, 0], [0.06, 0.1], [0, 0], [0, 0]], "v": [[23.98, 16.36], [23.77, 16.64], [22.64, 17.3], [22.64, 25.79], [22.41, 26.18], [12.23, 32.06], [12, 32.12], [11.77, 32.06], [1.59, 26.18], [1.36, 25.79], [1.36, 17.3], [0.23, 16.64], [0.01, 16.36], [0.07, 16.01], [1.43, 13.79], [1.59, 13.64], [1.59, 13.64], [9.76, 8.92], [8.28, 7.01], [8.23, 6.53], [8.64, 6.28], [9.67, 6.28], [9.67, 0.46], [10.13, 0], [13.87, 0], [14.33, 0.46], [14.33, 6.28], [15.36, 6.28], [15.77, 6.53], [15.72, 7.01], [14.24, 8.92], [22.41, 13.64], [22.41, 13.64], [22.57, 13.79], [23.93, 16.01], [23.99, 16.36], [23.98, 16.36]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.11, -0.06], [0, 0], [0, 0], [0.14, -0.08], [0, 0], [0.08, 0], [0.07, 0.04], [0, 0], [0, 0.16], [0, 0], [0, 0], [0.03, 0.12], [-0.06, 0.1], [0, 0], [-0.06, 0.04], [0, 0], [0, 0], [0, 0], [-0.08, 0.16], [-0.17, 0], [0, 0], [0, 0], [-0.25, 0], [0, 0], [0, -0.25], [0, 0], [0, 0], [-0.08, -0.16], [0.11, -0.14], [0, 0], [0, 0], [0, 0], [-0.04, -0.07], [0, 0], [0.03, -0.12], [0, 0]], "o": [[-0.03, 0.12], [0, 0], [0, 0], [0, 0.16], [0, 0], [-0.07, 0.04], [-0.08, 0], [0, 0], [-0.14, -0.08], [0, 0], [0, 0], [-0.11, -0.06], [-0.03, -0.12], [0, 0], [0.04, -0.07], [0, 0], [0, 0], [0, 0], [-0.11, -0.14], [0.08, -0.16], [0, 0], [0, 0], [0, -0.25], [0, 0], [0.25, 0], [0, 0], [0, 0], [0.17, 0], [0.08, 0.16], [0, 0], [0, 0], [0, 0], [0.06, 0.04], [0, 0], [0.06, 0.1], [0, 0], [0, 0]], "v": [[23.98, 16.36], [23.77, 16.64], [22.64, 17.3], [22.64, 25.79], [22.41, 26.18], [12.23, 32.06], [12, 32.12], [11.77, 32.06], [1.59, 26.18], [1.36, 25.79], [1.36, 17.3], [0.23, 16.64], [0.01, 16.36], [0.07, 16.01], [1.43, 13.79], [1.59, 13.64], [1.59, 13.64], [9.76, 8.92], [8.28, 7.01], [8.23, 6.53], [8.64, 6.28], [9.67, 6.28], [9.67, 0.46], [10.13, 0], [13.87, 0], [14.33, 0.46], [14.33, 6.28], [15.36, 6.28], [15.77, 6.53], [15.72, 7.01], [14.24, 8.92], [22.41, 13.64], [22.41, 13.64], [22.57, 13.79], [23.93, 16.01], [23.99, 16.36], [23.98, 16.36]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0]], "o": [[0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[1.33, 0.91], [1.32, 0.92], [1.26, 0.96], [1.26, 1.43], [1.24, 1.45], [0.68, 1.78], [0.67, 1.78], [0.65, 1.78], [0.09, 1.45], [0.08, 1.43], [0.08, 0.96], [0.01, 0.92], [0, 0.91], [0, 0.89], [0.08, 0.77], [0.09, 0.76], [0.09, 0.76], [0.54, 0.5], [0.46, 0.39], [0.46, 0.36], [0.48, 0.35], [0.54, 0.35], [0.54, 0.03], [0.56, 0], [0.77, 0], [0.8, 0.03], [0.8, 0.35], [0.85, 0.35], [0.88, 0.36], [0.87, 0.39], [0.79, 0.5], [1.24, 0.76], [1.24, 0.76], [1.25, 0.77], [1.33, 0.89], [1.33, 0.91], [1.33, 0.91]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.01], [0, 0]], "o": [[0, 0.01], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [-0.01, 0], [0, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, -0.01], [0, -0.01], [0, 0], [0, 0], [0, -0.01], [0, 0], [0.01, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[1.33, 0.91], [1.32, 0.92], [1.26, 0.96], [1.26, 1.43], [1.24, 1.45], [0.68, 1.78], [0.67, 1.78], [0.65, 1.78], [0.09, 1.45], [0.08, 1.43], [0.08, 0.96], [0.01, 0.92], [0, 0.91], [0, 0.89], [0.08, 0.77], [0.09, 0.76], [0.09, 0.76], [0.54, 0.5], [0.46, 0.39], [0.46, 0.36], [0.48, 0.35], [0.54, 0.35], [0.54, 0.03], [0.56, 0], [0.77, 0], [0.8, 0.03], [0.8, 0.35], [0.85, 0.35], [0.88, 0.36], [0.87, 0.39], [0.79, 0.5], [1.24, 0.76], [1.24, 0.76], [1.25, 0.77], [1.33, 0.89], [1.33, 0.91], [1.33, 0.91]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[0.53, 0.4], [0.67, 0.57], [0.8, 0.4], [0.77, 0.4], [0.75, 0.37], [0.75, 0.05], [0.59, 0.05], [0.59, 0.37], [0.56, 0.4], [0.53, 0.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[0.53, 0.4], [0.67, 0.57], [0.8, 0.4], [0.77, 0.4], [0.75, 0.37], [0.75, 0.05], [0.59, 0.05], [0.59, 0.37], [0.56, 0.4], [0.53, 0.4]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.25], [0, 0], [0, 0], [0, 0], [0.25, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.25, 0], [0, 0], [0, 0], [0, 0], [0, 0.25], [0, 0], [0, 0]], "v": [[9.57, 7.19], [12, 10.32], [14.43, 7.19], [13.87, 7.19], [13.42, 6.73], [13.42, 0.91], [10.58, 0.91], [10.58, 6.73], [10.13, 7.19], [9.57, 7.19]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.25], [0, 0], [0, 0], [0, 0], [0.25, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.25, 0], [0, 0], [0, 0], [0, 0], [0, 0.25], [0, 0], [0, 0]], "v": [[9.57, 7.19], [12, 10.32], [14.43, 7.19], [13.87, 7.19], [13.42, 6.73], [13.42, 0.91], [10.58, 0.91], [10.58, 6.73], [10.13, 7.19], [9.57, 7.19]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[0.53, 0.4], [0.67, 0.57], [0.8, 0.4], [0.77, 0.4], [0.75, 0.37], [0.75, 0.05], [0.59, 0.05], [0.59, 0.37], [0.56, 0.4], [0.53, 0.4]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0]], "v": [[0.53, 0.4], [0.67, 0.57], [0.8, 0.4], [0.77, 0.4], [0.75, 0.37], [0.75, 0.05], [0.59, 0.05], [0.59, 0.37], [0.56, 0.4], [0.53, 0.4]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.57, 0.54], [0.15, 0.78], [0.67, 1.08], [1.18, 0.78], [0.76, 0.54], [0.69, 0.63], [0.67, 0.64], [0.65, 0.63], [0.57, 0.54], [0.57, 0.54]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.57, 0.54], [0.15, 0.78], [0.67, 1.08], [1.18, 0.78], [0.76, 0.54], [0.69, 0.63], [0.67, 0.64], [0.65, 0.63], [0.57, 0.54], [0.57, 0.54]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.14, 0], [0.09, 0.11], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.11], [-0.14, 0], [0, 0], [0, 0], [0, 0]], "v": [[10.33, 9.65], [2.73, 14.03], [12, 19.38], [21.27, 14.03], [13.67, 9.65], [12.36, 11.34], [12, 11.51], [11.64, 11.34], [10.33, 9.65], [10.33, 9.65]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.14, 0], [0.09, 0.11], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.09, 0.11], [-0.14, 0], [0, 0], [0, 0], [0, 0]], "v": [[10.33, 9.65], [2.73, 14.03], [12, 19.38], [21.27, 14.03], [13.67, 9.65], [12.36, 11.34], [12, 11.51], [11.64, 11.34], [10.33, 9.65], [10.33, 9.65]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.57, 0.54], [0.15, 0.78], [0.67, 1.08], [1.18, 0.78], [0.76, 0.54], [0.69, 0.63], [0.67, 0.64], [0.65, 0.63], [0.57, 0.54], [0.57, 0.54]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.01], [-0.01, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.57, 0.54], [0.15, 0.78], [0.67, 1.08], [1.18, 0.78], [0.76, 0.54], [0.69, 0.63], [0.67, 0.64], [0.65, 0.63], [0.57, 0.54], [0.57, 0.54]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.06, 0.89], [0.58, 1.19], [0.63, 1.12], [0.11, 0.81], [0.06, 0.89]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.06, 0.89], [0.58, 1.19], [0.63, 1.12], [0.11, 0.81], [0.06, 0.89]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.09, 16.09], [10.48, 21.51], [11.37, 20.07], [1.97, 14.65], [1.09, 16.09]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.09, 16.09], [10.48, 21.51], [11.37, 20.07], [1.97, 14.65], [1.09, 16.09]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.06, 0.89], [0.58, 1.19], [0.63, 1.12], [0.11, 0.81], [0.06, 0.89]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.06, 0.89], [0.58, 1.19], [0.63, 1.12], [0.11, 0.81], [0.06, 0.89]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.13, 1.42], [0.64, 1.72], [0.64, 1.2], [0.61, 1.24], [0.59, 1.25], [0.58, 1.25], [0.13, 0.99], [0.13, 1.42]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.13, 1.42], [0.64, 1.72], [0.64, 1.2], [0.61, 1.24], [0.59, 1.25], [0.58, 1.25], [0.13, 0.99], [0.13, 1.42]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.15, 0], [0.07, 0.04], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.09, 0.14], [-0.08, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.28, 25.53], [11.55, 30.88], [11.55, 21.52], [11.03, 22.36], [10.64, 22.58], [10.41, 22.52], [2.28, 17.82], [2.28, 25.53]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.15, 0], [0.07, 0.04], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.09, 0.14], [-0.08, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.28, 25.53], [11.55, 30.88], [11.55, 21.52], [11.03, 22.36], [10.64, 22.58], [10.41, 22.52], [2.28, 17.82], [2.28, 25.53]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.13, 1.42], [0.64, 1.72], [0.64, 1.2], [0.61, 1.24], [0.59, 1.25], [0.58, 1.25], [0.13, 0.99], [0.13, 1.42]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.01, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.13, 1.42], [0.64, 1.72], [0.64, 1.2], [0.61, 1.24], [0.59, 1.25], [0.58, 1.25], [0.13, 0.99], [0.13, 1.42]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.21, 0.99], [0.76, 1.25], [0.74, 1.25], [0.72, 1.24], [0.69, 1.2], [0.69, 1.72], [1.21, 1.42], [1.21, 0.99]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.21, 0.99], [0.76, 1.25], [0.74, 1.25], [0.72, 1.24], [0.69, 1.2], [0.69, 1.72], [1.21, 1.42], [1.21, 0.99]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.08, 0], [0.09, 0.14], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-0.07, 0.04], [-0.15, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[21.72, 17.82], [13.59, 22.52], [13.36, 22.58], [12.98, 22.36], [12.45, 21.52], [12.45, 30.88], [21.72, 25.53], [21.72, 17.82]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.08, 0], [0.09, 0.14], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-0.07, 0.04], [-0.15, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[21.72, 17.82], [13.59, 22.52], [13.36, 22.58], [12.98, 22.36], [12.45, 21.52], [12.45, 30.88], [21.72, 25.53], [21.72, 17.82]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.21, 0.99], [0.76, 1.25], [0.74, 1.25], [0.72, 1.24], [0.69, 1.2], [0.69, 1.72], [1.21, 1.42], [1.21, 0.99]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0.01], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.21, 0.99], [0.76, 1.25], [0.74, 1.25], [0.72, 1.24], [0.69, 1.2], [0.69, 1.72], [1.21, 1.42], [1.21, 0.99]]}], "t": 229}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.27, 0.89], [1.22, 0.81], [0.7, 1.12], [0.75, 1.19], [1.27, 0.89]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.27, 0.89], [1.22, 0.81], [0.7, 1.12], [0.75, 1.19], [1.27, 0.89]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.91, 16.09], [22.02, 14.65], [12.63, 20.07], [13.52, 21.51], [22.91, 16.09]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.91, 16.09], [22.02, 14.65], [12.63, 20.07], [13.52, 21.51], [22.91, 16.09]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.27, 0.89], [1.22, 0.81], [0.7, 1.12], [0.75, 1.19], [1.27, 0.89]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.27, 0.89], [1.22, 0.81], [0.7, 1.12], [0.75, 1.19], [1.27, 0.89]]}], "t": 229}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.3157, 0.4562, 0.9542], "t": 217}, {"s": [0.3157, 0.4562, 0.9542], "t": 229}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}], "ind": 1}, {"ty": 4, "nm": "Frame 14102 Bg", "sr": 1, "st": 0, "op": 218.19, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 217}, {"s": [100, 100], "t": 229}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [18, 18], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1], "t": 217}, {"s": [1, 1], "t": 229}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 217}, {"s": [0], "t": 229}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 217}, {"s": [100], "t": 229}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 12}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 84}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36, 0], [36, 36], [0, 36], [0, 0]]}], "t": 156}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 217}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2, 0], [2, 2], [0, 2], [0, 0]]}], "t": 229}]}}], "ind": 2}]}]}